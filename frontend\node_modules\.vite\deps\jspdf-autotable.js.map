{"version": 3, "sources": ["../../jspdf-autotable/dist/jspdf.plugin.autotable.mjs"], "sourcesContent": ["/**\r\n * Improved text function with halign and valign support\r\n * Inspiration from: http://stackoverflow.com/questions/28327510/align-text-right-using-jspdf/28433113#28433113\r\n */\r\nfunction autoTableText (text, x, y, styles, doc) {\r\n    styles = styles || {};\r\n    var PHYSICAL_LINE_HEIGHT = 1.15;\r\n    var k = doc.internal.scaleFactor;\r\n    var fontSize = doc.internal.getFontSize() / k;\r\n    var lineHeightFactor = doc.getLineHeightFactor\r\n        ? doc.getLineHeightFactor()\r\n        : PHYSICAL_LINE_HEIGHT;\r\n    var lineHeight = fontSize * lineHeightFactor;\r\n    var splitRegex = /\\r\\n|\\r|\\n/g;\r\n    var splitText = '';\r\n    var lineCount = 1;\r\n    if (styles.valign === 'middle' ||\r\n        styles.valign === 'bottom' ||\r\n        styles.halign === 'center' ||\r\n        styles.halign === 'right') {\r\n        splitText = typeof text === 'string' ? text.split(splitRegex) : text;\r\n        lineCount = splitText.length || 1;\r\n    }\r\n    // Align the top\r\n    y += fontSize * (2 - PHYSICAL_LINE_HEIGHT);\r\n    if (styles.valign === 'middle')\r\n        y -= (lineCount / 2) * lineHeight;\r\n    else if (styles.valign === 'bottom')\r\n        y -= lineCount * lineHeight;\r\n    if (styles.halign === 'center' || styles.halign === 'right') {\r\n        var alignSize = fontSize;\r\n        if (styles.halign === 'center')\r\n            alignSize *= 0.5;\r\n        if (splitText && lineCount >= 1) {\r\n            for (var iLine = 0; iLine < splitText.length; iLine++) {\r\n                doc.text(splitText[iLine], x - doc.getStringUnitWidth(splitText[iLine]) * alignSize, y);\r\n                y += lineHeight;\r\n            }\r\n            return doc;\r\n        }\r\n        x -= doc.getStringUnitWidth(text) * alignSize;\r\n    }\r\n    if (styles.halign === 'justify') {\r\n        doc.text(text, x, y, { maxWidth: styles.maxWidth || 100, align: 'justify' });\r\n    }\r\n    else {\r\n        doc.text(text, x, y);\r\n    }\r\n    return doc;\r\n}\r\n\r\nvar globalDefaults = {};\r\nvar DocHandler = /** @class */ (function () {\r\n    function DocHandler(jsPDFDocument) {\r\n        this.jsPDFDocument = jsPDFDocument;\r\n        this.userStyles = {\r\n            // Black for versions of jspdf without getTextColor\r\n            textColor: jsPDFDocument.getTextColor\r\n                ? this.jsPDFDocument.getTextColor()\r\n                : 0,\r\n            fontSize: jsPDFDocument.internal.getFontSize(),\r\n            fontStyle: jsPDFDocument.internal.getFont().fontStyle,\r\n            font: jsPDFDocument.internal.getFont().fontName,\r\n            // 0 for versions of jspdf without getLineWidth\r\n            lineWidth: jsPDFDocument.getLineWidth\r\n                ? this.jsPDFDocument.getLineWidth()\r\n                : 0,\r\n            // Black for versions of jspdf without getDrawColor\r\n            lineColor: jsPDFDocument.getDrawColor\r\n                ? this.jsPDFDocument.getDrawColor()\r\n                : 0,\r\n        };\r\n    }\r\n    DocHandler.setDefaults = function (defaults, doc) {\r\n        if (doc === void 0) { doc = null; }\r\n        if (doc) {\r\n            doc.__autoTableDocumentDefaults = defaults;\r\n        }\r\n        else {\r\n            globalDefaults = defaults;\r\n        }\r\n    };\r\n    DocHandler.unifyColor = function (c) {\r\n        if (Array.isArray(c)) {\r\n            return c;\r\n        }\r\n        else if (typeof c === 'number') {\r\n            return [c, c, c];\r\n        }\r\n        else if (typeof c === 'string') {\r\n            return [c];\r\n        }\r\n        else {\r\n            return null;\r\n        }\r\n    };\r\n    DocHandler.prototype.applyStyles = function (styles, fontOnly) {\r\n        // Font style needs to be applied before font\r\n        // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/632\r\n        var _a, _b, _c;\r\n        if (fontOnly === void 0) { fontOnly = false; }\r\n        if (styles.fontStyle && this.jsPDFDocument.setFontStyle) {\r\n            this.jsPDFDocument.setFontStyle(styles.fontStyle);\r\n        }\r\n        var _d = this.jsPDFDocument.internal.getFont(), fontStyle = _d.fontStyle, fontName = _d.fontName;\r\n        if (styles.font)\r\n            fontName = styles.font;\r\n        if (styles.fontStyle) {\r\n            fontStyle = styles.fontStyle;\r\n            var availableFontStyles = this.getFontList()[fontName];\r\n            if (availableFontStyles &&\r\n                availableFontStyles.indexOf(fontStyle) === -1 &&\r\n                this.jsPDFDocument.setFontStyle) {\r\n                // Common issue was that the default bold in headers\r\n                // made custom fonts not work. For example:\r\n                // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/653\r\n                this.jsPDFDocument.setFontStyle(availableFontStyles[0]);\r\n                fontStyle = availableFontStyles[0];\r\n            }\r\n        }\r\n        this.jsPDFDocument.setFont(fontName, fontStyle);\r\n        if (styles.fontSize)\r\n            this.jsPDFDocument.setFontSize(styles.fontSize);\r\n        if (fontOnly) {\r\n            return; // Performance improvement\r\n        }\r\n        var color = DocHandler.unifyColor(styles.fillColor);\r\n        if (color)\r\n            (_a = this.jsPDFDocument).setFillColor.apply(_a, color);\r\n        color = DocHandler.unifyColor(styles.textColor);\r\n        if (color)\r\n            (_b = this.jsPDFDocument).setTextColor.apply(_b, color);\r\n        color = DocHandler.unifyColor(styles.lineColor);\r\n        if (color)\r\n            (_c = this.jsPDFDocument).setDrawColor.apply(_c, color);\r\n        if (typeof styles.lineWidth === 'number') {\r\n            this.jsPDFDocument.setLineWidth(styles.lineWidth);\r\n        }\r\n    };\r\n    DocHandler.prototype.splitTextToSize = function (text, size, opts) {\r\n        return this.jsPDFDocument.splitTextToSize(text, size, opts);\r\n    };\r\n    /**\r\n     * Adds a rectangle to the PDF\r\n     * @param x Coordinate (in units declared at inception of PDF document) against left edge of the page\r\n     * @param y Coordinate (in units declared at inception of PDF document) against upper edge of the page\r\n     * @param width Width (in units declared at inception of PDF document)\r\n     * @param height Height (in units declared at inception of PDF document)\r\n     * @param fillStyle A string specifying the painting style or null. Valid styles include: 'S' [default] - stroke, 'F' - fill, and 'DF' (or 'FD') - fill then stroke.\r\n     */\r\n    DocHandler.prototype.rect = function (x, y, width, height, fillStyle) {\r\n        // null is excluded from fillStyle possible values because it isn't needed\r\n        // and is prone to bugs as it's used to postpone setting the style\r\n        // https://rawgit.com/MrRio/jsPDF/master/docs/jsPDF.html#rect\r\n        return this.jsPDFDocument.rect(x, y, width, height, fillStyle);\r\n    };\r\n    DocHandler.prototype.getLastAutoTable = function () {\r\n        return this.jsPDFDocument.lastAutoTable || null;\r\n    };\r\n    DocHandler.prototype.getTextWidth = function (text) {\r\n        return this.jsPDFDocument.getTextWidth(text);\r\n    };\r\n    DocHandler.prototype.getDocument = function () {\r\n        return this.jsPDFDocument;\r\n    };\r\n    DocHandler.prototype.setPage = function (page) {\r\n        this.jsPDFDocument.setPage(page);\r\n    };\r\n    DocHandler.prototype.addPage = function () {\r\n        return this.jsPDFDocument.addPage();\r\n    };\r\n    DocHandler.prototype.getFontList = function () {\r\n        return this.jsPDFDocument.getFontList();\r\n    };\r\n    DocHandler.prototype.getGlobalOptions = function () {\r\n        return globalDefaults || {};\r\n    };\r\n    DocHandler.prototype.getDocumentOptions = function () {\r\n        return this.jsPDFDocument.__autoTableDocumentDefaults || {};\r\n    };\r\n    DocHandler.prototype.pageSize = function () {\r\n        var pageSize = this.jsPDFDocument.internal.pageSize;\r\n        // JSPDF 1.4 uses get functions instead of properties on pageSize\r\n        if (pageSize.width == null) {\r\n            pageSize = { width: pageSize.getWidth(), height: pageSize.getHeight() };\r\n        }\r\n        return pageSize;\r\n    };\r\n    DocHandler.prototype.scaleFactor = function () {\r\n        return this.jsPDFDocument.internal.scaleFactor;\r\n    };\r\n    DocHandler.prototype.getLineHeightFactor = function () {\r\n        var doc = this.jsPDFDocument;\r\n        return doc.getLineHeightFactor ? doc.getLineHeightFactor() : 1.15;\r\n    };\r\n    DocHandler.prototype.getLineHeight = function (fontSize) {\r\n        return (fontSize / this.scaleFactor()) * this.getLineHeightFactor();\r\n    };\r\n    DocHandler.prototype.pageNumber = function () {\r\n        var pageInfo = this.jsPDFDocument.internal.getCurrentPageInfo();\r\n        if (!pageInfo) {\r\n            // Only recent versions of jspdf has pageInfo\r\n            return this.jsPDFDocument.internal.getNumberOfPages();\r\n        }\r\n        return pageInfo.pageNumber;\r\n    };\r\n    return DocHandler;\r\n}());\r\n\r\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nfunction __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\r\n\r\nvar HtmlRowInput = /** @class */ (function (_super) {\r\n    __extends(HtmlRowInput, _super);\r\n    function HtmlRowInput(element) {\r\n        var _this = _super.call(this) || this;\r\n        _this._element = element;\r\n        return _this;\r\n    }\r\n    return HtmlRowInput;\r\n}(Array));\r\n// Base style for all themes\r\nfunction defaultStyles(scaleFactor) {\r\n    return {\r\n        font: 'helvetica', // helvetica, times, courier\r\n        fontStyle: 'normal', // normal, bold, italic, bolditalic\r\n        overflow: 'linebreak', // linebreak, ellipsize, visible or hidden\r\n        fillColor: false, // Either false for transparent, rbg array e.g. [255, 255, 255] or gray level e.g 200\r\n        textColor: 20,\r\n        halign: 'left', // left, center, right, justify\r\n        valign: 'top', // top, middle, bottom\r\n        fontSize: 10,\r\n        cellPadding: 5 / scaleFactor, // number or {top,left,right,left,vertical,horizontal}\r\n        lineColor: 200,\r\n        lineWidth: 0,\r\n        cellWidth: 'auto', // 'auto'|'wrap'|number\r\n        minCellHeight: 0,\r\n        minCellWidth: 0,\r\n    };\r\n}\r\nfunction getTheme(name) {\r\n    var themes = {\r\n        striped: {\r\n            table: { fillColor: 255, textColor: 80, fontStyle: 'normal' },\r\n            head: { textColor: 255, fillColor: [41, 128, 185], fontStyle: 'bold' },\r\n            body: {},\r\n            foot: { textColor: 255, fillColor: [41, 128, 185], fontStyle: 'bold' },\r\n            alternateRow: { fillColor: 245 },\r\n        },\r\n        grid: {\r\n            table: {\r\n                fillColor: 255,\r\n                textColor: 80,\r\n                fontStyle: 'normal',\r\n                lineWidth: 0.1,\r\n            },\r\n            head: {\r\n                textColor: 255,\r\n                fillColor: [26, 188, 156],\r\n                fontStyle: 'bold',\r\n                lineWidth: 0,\r\n            },\r\n            body: {},\r\n            foot: {\r\n                textColor: 255,\r\n                fillColor: [26, 188, 156],\r\n                fontStyle: 'bold',\r\n                lineWidth: 0,\r\n            },\r\n            alternateRow: {},\r\n        },\r\n        plain: { head: { fontStyle: 'bold' }, foot: { fontStyle: 'bold' } },\r\n    };\r\n    return themes[name];\r\n}\r\n\r\nfunction getStringWidth(text, styles, doc) {\r\n    doc.applyStyles(styles, true);\r\n    var textArr = Array.isArray(text) ? text : [text];\r\n    var widestLineWidth = textArr\r\n        .map(function (text) { return doc.getTextWidth(text); })\r\n        .reduce(function (a, b) { return Math.max(a, b); }, 0);\r\n    return widestLineWidth;\r\n}\r\nfunction addTableBorder(doc, table, startPos, cursor) {\r\n    var lineWidth = table.settings.tableLineWidth;\r\n    var lineColor = table.settings.tableLineColor;\r\n    doc.applyStyles({ lineWidth: lineWidth, lineColor: lineColor });\r\n    var fillStyle = getFillStyle(lineWidth, false);\r\n    if (fillStyle) {\r\n        doc.rect(startPos.x, startPos.y, table.getWidth(doc.pageSize().width), cursor.y - startPos.y, fillStyle);\r\n    }\r\n}\r\nfunction getFillStyle(lineWidth, fillColor) {\r\n    var drawLine = lineWidth > 0;\r\n    var drawBackground = fillColor || fillColor === 0;\r\n    if (drawLine && drawBackground) {\r\n        return 'DF'; // Fill then stroke\r\n    }\r\n    else if (drawLine) {\r\n        return 'S'; // Only stroke (transparent background)\r\n    }\r\n    else if (drawBackground) {\r\n        return 'F'; // Only fill, no stroke\r\n    }\r\n    else {\r\n        return null;\r\n    }\r\n}\r\nfunction parseSpacing(value, defaultValue) {\r\n    var _a, _b, _c, _d;\r\n    value = value || defaultValue;\r\n    if (Array.isArray(value)) {\r\n        if (value.length >= 4) {\r\n            return {\r\n                top: value[0],\r\n                right: value[1],\r\n                bottom: value[2],\r\n                left: value[3],\r\n            };\r\n        }\r\n        else if (value.length === 3) {\r\n            return {\r\n                top: value[0],\r\n                right: value[1],\r\n                bottom: value[2],\r\n                left: value[1],\r\n            };\r\n        }\r\n        else if (value.length === 2) {\r\n            return {\r\n                top: value[0],\r\n                right: value[1],\r\n                bottom: value[0],\r\n                left: value[1],\r\n            };\r\n        }\r\n        else if (value.length === 1) {\r\n            value = value[0];\r\n        }\r\n        else {\r\n            value = defaultValue;\r\n        }\r\n    }\r\n    if (typeof value === 'object') {\r\n        if (typeof value.vertical === 'number') {\r\n            value.top = value.vertical;\r\n            value.bottom = value.vertical;\r\n        }\r\n        if (typeof value.horizontal === 'number') {\r\n            value.right = value.horizontal;\r\n            value.left = value.horizontal;\r\n        }\r\n        return {\r\n            left: (_a = value.left) !== null && _a !== void 0 ? _a : defaultValue,\r\n            top: (_b = value.top) !== null && _b !== void 0 ? _b : defaultValue,\r\n            right: (_c = value.right) !== null && _c !== void 0 ? _c : defaultValue,\r\n            bottom: (_d = value.bottom) !== null && _d !== void 0 ? _d : defaultValue,\r\n        };\r\n    }\r\n    if (typeof value !== 'number') {\r\n        value = defaultValue;\r\n    }\r\n    return { top: value, right: value, bottom: value, left: value };\r\n}\r\nfunction getPageAvailableWidth(doc, table) {\r\n    var margins = parseSpacing(table.settings.margin, 0);\r\n    return doc.pageSize().width - (margins.left + margins.right);\r\n}\r\n\r\n// Limitations\r\n// - No support for border spacing\r\n// - No support for transparency\r\nfunction parseCss(supportedFonts, element, scaleFactor, style, window) {\r\n    var result = {};\r\n    var pxScaleFactor = 96 / 72;\r\n    var backgroundColor = parseColor(element, function (elem) {\r\n        return window.getComputedStyle(elem)['backgroundColor'];\r\n    });\r\n    if (backgroundColor != null)\r\n        result.fillColor = backgroundColor;\r\n    var textColor = parseColor(element, function (elem) {\r\n        return window.getComputedStyle(elem)['color'];\r\n    });\r\n    if (textColor != null)\r\n        result.textColor = textColor;\r\n    var padding = parsePadding(style, scaleFactor);\r\n    if (padding)\r\n        result.cellPadding = padding;\r\n    var borderColorSide = 'borderTopColor';\r\n    var finalScaleFactor = pxScaleFactor * scaleFactor;\r\n    var btw = style.borderTopWidth;\r\n    if (style.borderBottomWidth === btw &&\r\n        style.borderRightWidth === btw &&\r\n        style.borderLeftWidth === btw) {\r\n        var borderWidth = (parseFloat(btw) || 0) / finalScaleFactor;\r\n        if (borderWidth)\r\n            result.lineWidth = borderWidth;\r\n    }\r\n    else {\r\n        result.lineWidth = {\r\n            top: (parseFloat(style.borderTopWidth) || 0) / finalScaleFactor,\r\n            right: (parseFloat(style.borderRightWidth) || 0) / finalScaleFactor,\r\n            bottom: (parseFloat(style.borderBottomWidth) || 0) / finalScaleFactor,\r\n            left: (parseFloat(style.borderLeftWidth) || 0) / finalScaleFactor,\r\n        };\r\n        // Choose border color of first available side\r\n        // could be improved by supporting object as lineColor\r\n        if (!result.lineWidth.top) {\r\n            if (result.lineWidth.right) {\r\n                borderColorSide = 'borderRightColor';\r\n            }\r\n            else if (result.lineWidth.bottom) {\r\n                borderColorSide = 'borderBottomColor';\r\n            }\r\n            else if (result.lineWidth.left) {\r\n                borderColorSide = 'borderLeftColor';\r\n            }\r\n        }\r\n    }\r\n    var borderColor = parseColor(element, function (elem) {\r\n        return window.getComputedStyle(elem)[borderColorSide];\r\n    });\r\n    if (borderColor != null)\r\n        result.lineColor = borderColor;\r\n    var accepted = ['left', 'right', 'center', 'justify'];\r\n    if (accepted.indexOf(style.textAlign) !== -1) {\r\n        result.halign = style.textAlign;\r\n    }\r\n    accepted = ['middle', 'bottom', 'top'];\r\n    if (accepted.indexOf(style.verticalAlign) !== -1) {\r\n        result.valign = style.verticalAlign;\r\n    }\r\n    var res = parseInt(style.fontSize || '');\r\n    if (!isNaN(res))\r\n        result.fontSize = res / pxScaleFactor;\r\n    var fontStyle = parseFontStyle(style);\r\n    if (fontStyle)\r\n        result.fontStyle = fontStyle;\r\n    var font = (style.fontFamily || '').toLowerCase();\r\n    if (supportedFonts.indexOf(font) !== -1) {\r\n        result.font = font;\r\n    }\r\n    return result;\r\n}\r\nfunction parseFontStyle(style) {\r\n    var res = '';\r\n    if (style.fontWeight === 'bold' ||\r\n        style.fontWeight === 'bolder' ||\r\n        parseInt(style.fontWeight) >= 700) {\r\n        res = 'bold';\r\n    }\r\n    if (style.fontStyle === 'italic' || style.fontStyle === 'oblique') {\r\n        res += 'italic';\r\n    }\r\n    return res;\r\n}\r\nfunction parseColor(element, styleGetter) {\r\n    var cssColor = realColor(element, styleGetter);\r\n    if (!cssColor)\r\n        return null;\r\n    var rgba = cssColor.match(/^rgba?\\((\\d+),\\s*(\\d+),\\s*(\\d+)(?:,\\s*(\\d*\\.?\\d*))?\\)$/);\r\n    if (!rgba || !Array.isArray(rgba)) {\r\n        return null;\r\n    }\r\n    var color = [\r\n        parseInt(rgba[1]),\r\n        parseInt(rgba[2]),\r\n        parseInt(rgba[3]),\r\n    ];\r\n    var alpha = parseInt(rgba[4]);\r\n    if (alpha === 0 || isNaN(color[0]) || isNaN(color[1]) || isNaN(color[2])) {\r\n        return null;\r\n    }\r\n    return color;\r\n}\r\nfunction realColor(elem, styleGetter) {\r\n    var bg = styleGetter(elem);\r\n    if (bg === 'rgba(0, 0, 0, 0)' ||\r\n        bg === 'transparent' ||\r\n        bg === 'initial' ||\r\n        bg === 'inherit') {\r\n        if (elem.parentElement == null) {\r\n            return null;\r\n        }\r\n        return realColor(elem.parentElement, styleGetter);\r\n    }\r\n    else {\r\n        return bg;\r\n    }\r\n}\r\nfunction parsePadding(style, scaleFactor) {\r\n    var val = [\r\n        style.paddingTop,\r\n        style.paddingRight,\r\n        style.paddingBottom,\r\n        style.paddingLeft,\r\n    ];\r\n    var pxScaleFactor = 96 / (72 / scaleFactor);\r\n    var linePadding = (parseInt(style.lineHeight) - parseInt(style.fontSize)) / scaleFactor / 2;\r\n    var inputPadding = val.map(function (n) {\r\n        return parseInt(n || '0') / pxScaleFactor;\r\n    });\r\n    var padding = parseSpacing(inputPadding, 0);\r\n    if (linePadding > padding.top) {\r\n        padding.top = linePadding;\r\n    }\r\n    if (linePadding > padding.bottom) {\r\n        padding.bottom = linePadding;\r\n    }\r\n    return padding;\r\n}\r\n\r\nfunction parseHtml(doc, input, window, includeHiddenHtml, useCss) {\r\n    var _a, _b;\r\n    if (includeHiddenHtml === void 0) { includeHiddenHtml = false; }\r\n    if (useCss === void 0) { useCss = false; }\r\n    var tableElement;\r\n    if (typeof input === 'string') {\r\n        tableElement = window.document.querySelector(input);\r\n    }\r\n    else {\r\n        tableElement = input;\r\n    }\r\n    var supportedFonts = Object.keys(doc.getFontList());\r\n    var scaleFactor = doc.scaleFactor();\r\n    var head = [], body = [], foot = [];\r\n    if (!tableElement) {\r\n        console.error('Html table could not be found with input: ', input);\r\n        return { head: head, body: body, foot: foot };\r\n    }\r\n    for (var i = 0; i < tableElement.rows.length; i++) {\r\n        var element = tableElement.rows[i];\r\n        var tagName = (_b = (_a = element === null || element === void 0 ? void 0 : element.parentElement) === null || _a === void 0 ? void 0 : _a.tagName) === null || _b === void 0 ? void 0 : _b.toLowerCase();\r\n        var row = parseRowContent(supportedFonts, scaleFactor, window, element, includeHiddenHtml, useCss);\r\n        if (!row)\r\n            continue;\r\n        if (tagName === 'thead') {\r\n            head.push(row);\r\n        }\r\n        else if (tagName === 'tfoot') {\r\n            foot.push(row);\r\n        }\r\n        else {\r\n            // Add to body both if parent is tbody or table\r\n            body.push(row);\r\n        }\r\n    }\r\n    return { head: head, body: body, foot: foot };\r\n}\r\nfunction parseRowContent(supportedFonts, scaleFactor, window, row, includeHidden, useCss) {\r\n    var resultRow = new HtmlRowInput(row);\r\n    for (var i = 0; i < row.cells.length; i++) {\r\n        var cell = row.cells[i];\r\n        var style_1 = window.getComputedStyle(cell);\r\n        if (includeHidden || style_1.display !== 'none') {\r\n            var cellStyles = void 0;\r\n            if (useCss) {\r\n                cellStyles = parseCss(supportedFonts, cell, scaleFactor, style_1, window);\r\n            }\r\n            resultRow.push({\r\n                rowSpan: cell.rowSpan,\r\n                colSpan: cell.colSpan,\r\n                styles: cellStyles,\r\n                _element: cell,\r\n                content: parseCellContent(cell),\r\n            });\r\n        }\r\n    }\r\n    var style = window.getComputedStyle(row);\r\n    if (resultRow.length > 0 && (includeHidden || style.display !== 'none')) {\r\n        return resultRow;\r\n    }\r\n}\r\nfunction parseCellContent(orgCell) {\r\n    // Work on cloned node to make sure no changes are applied to html table\r\n    var cell = orgCell.cloneNode(true);\r\n    // Remove extra space and line breaks in markup to make it more similar to\r\n    // what would be shown in html\r\n    cell.innerHTML = cell.innerHTML.replace(/\\n/g, '').replace(/ +/g, ' ');\r\n    // Preserve <br> tags as line breaks in the pdf\r\n    cell.innerHTML = cell.innerHTML\r\n        .split(/<br.*?>/) //start with '<br' and ends with '>'.\r\n        .map(function (part) { return part.trim(); })\r\n        .join('\\n');\r\n    // innerText for ie\r\n    return cell.innerText || cell.textContent || '';\r\n}\r\n\r\nfunction validateInput(global, document, current) {\r\n    for (var _i = 0, _a = [global, document, current]; _i < _a.length; _i++) {\r\n        var options = _a[_i];\r\n        if (options && typeof options !== 'object') {\r\n            console.error('The options parameter should be of type object, is: ' + typeof options);\r\n        }\r\n        if (options.startY && typeof options.startY !== 'number') {\r\n            console.error('Invalid value for startY option', options.startY);\r\n            delete options.startY;\r\n        }\r\n    }\r\n}\r\n\r\n/* eslint-disable @typescript-eslint/no-unused-vars */\r\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/assign\r\nfunction assign(target, s, s1, s2, s3) {\r\n    if (target == null) {\r\n        throw new TypeError('Cannot convert undefined or null to object');\r\n    }\r\n    var to = Object(target);\r\n    for (var index = 1; index < arguments.length; index++) {\r\n        // eslint-disable-next-line prefer-rest-params\r\n        var nextSource = arguments[index];\r\n        if (nextSource != null) {\r\n            // Skip over if undefined or null\r\n            for (var nextKey in nextSource) {\r\n                // Avoid bugs when hasOwnProperty is shadowed\r\n                if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {\r\n                    to[nextKey] = nextSource[nextKey];\r\n                }\r\n            }\r\n        }\r\n    }\r\n    return to;\r\n}\r\n\r\nfunction parseInput(d, current) {\r\n    var doc = new DocHandler(d);\r\n    var document = doc.getDocumentOptions();\r\n    var global = doc.getGlobalOptions();\r\n    validateInput(global, document, current);\r\n    var options = assign({}, global, document, current);\r\n    var win;\r\n    if (typeof window !== 'undefined') {\r\n        win = window;\r\n    }\r\n    var styles = parseStyles(global, document, current);\r\n    var hooks = parseHooks(global, document, current);\r\n    var settings = parseSettings(doc, options);\r\n    var content = parseContent$1(doc, options, win);\r\n    return { id: current.tableId, content: content, hooks: hooks, styles: styles, settings: settings };\r\n}\r\nfunction parseStyles(gInput, dInput, cInput) {\r\n    var styleOptions = {\r\n        styles: {},\r\n        headStyles: {},\r\n        bodyStyles: {},\r\n        footStyles: {},\r\n        alternateRowStyles: {},\r\n        columnStyles: {},\r\n    };\r\n    var _loop_1 = function (prop) {\r\n        if (prop === 'columnStyles') {\r\n            var global_1 = gInput[prop];\r\n            var document_1 = dInput[prop];\r\n            var current = cInput[prop];\r\n            styleOptions.columnStyles = assign({}, global_1, document_1, current);\r\n        }\r\n        else {\r\n            var allOptions = [gInput, dInput, cInput];\r\n            var styles = allOptions.map(function (opts) { return opts[prop] || {}; });\r\n            styleOptions[prop] = assign({}, styles[0], styles[1], styles[2]);\r\n        }\r\n    };\r\n    for (var _i = 0, _a = Object.keys(styleOptions); _i < _a.length; _i++) {\r\n        var prop = _a[_i];\r\n        _loop_1(prop);\r\n    }\r\n    return styleOptions;\r\n}\r\nfunction parseHooks(global, document, current) {\r\n    var allOptions = [global, document, current];\r\n    var result = {\r\n        didParseCell: [],\r\n        willDrawCell: [],\r\n        didDrawCell: [],\r\n        willDrawPage: [],\r\n        didDrawPage: [],\r\n    };\r\n    for (var _i = 0, allOptions_1 = allOptions; _i < allOptions_1.length; _i++) {\r\n        var options = allOptions_1[_i];\r\n        if (options.didParseCell)\r\n            result.didParseCell.push(options.didParseCell);\r\n        if (options.willDrawCell)\r\n            result.willDrawCell.push(options.willDrawCell);\r\n        if (options.didDrawCell)\r\n            result.didDrawCell.push(options.didDrawCell);\r\n        if (options.willDrawPage)\r\n            result.willDrawPage.push(options.willDrawPage);\r\n        if (options.didDrawPage)\r\n            result.didDrawPage.push(options.didDrawPage);\r\n    }\r\n    return result;\r\n}\r\nfunction parseSettings(doc, options) {\r\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;\r\n    var margin = parseSpacing(options.margin, 40 / doc.scaleFactor());\r\n    var startY = (_a = getStartY(doc, options.startY)) !== null && _a !== void 0 ? _a : margin.top;\r\n    var showFoot;\r\n    if (options.showFoot === true) {\r\n        showFoot = 'everyPage';\r\n    }\r\n    else if (options.showFoot === false) {\r\n        showFoot = 'never';\r\n    }\r\n    else {\r\n        showFoot = (_b = options.showFoot) !== null && _b !== void 0 ? _b : 'everyPage';\r\n    }\r\n    var showHead;\r\n    if (options.showHead === true) {\r\n        showHead = 'everyPage';\r\n    }\r\n    else if (options.showHead === false) {\r\n        showHead = 'never';\r\n    }\r\n    else {\r\n        showHead = (_c = options.showHead) !== null && _c !== void 0 ? _c : 'everyPage';\r\n    }\r\n    var useCss = (_d = options.useCss) !== null && _d !== void 0 ? _d : false;\r\n    var theme = options.theme || (useCss ? 'plain' : 'striped');\r\n    var horizontalPageBreak = !!options.horizontalPageBreak;\r\n    var horizontalPageBreakRepeat = (_e = options.horizontalPageBreakRepeat) !== null && _e !== void 0 ? _e : null;\r\n    return {\r\n        includeHiddenHtml: (_f = options.includeHiddenHtml) !== null && _f !== void 0 ? _f : false,\r\n        useCss: useCss,\r\n        theme: theme,\r\n        startY: startY,\r\n        margin: margin,\r\n        pageBreak: (_g = options.pageBreak) !== null && _g !== void 0 ? _g : 'auto',\r\n        rowPageBreak: (_h = options.rowPageBreak) !== null && _h !== void 0 ? _h : 'auto',\r\n        tableWidth: (_j = options.tableWidth) !== null && _j !== void 0 ? _j : 'auto',\r\n        showHead: showHead,\r\n        showFoot: showFoot,\r\n        tableLineWidth: (_k = options.tableLineWidth) !== null && _k !== void 0 ? _k : 0,\r\n        tableLineColor: (_l = options.tableLineColor) !== null && _l !== void 0 ? _l : 200,\r\n        horizontalPageBreak: horizontalPageBreak,\r\n        horizontalPageBreakRepeat: horizontalPageBreakRepeat,\r\n        horizontalPageBreakBehaviour: (_m = options.horizontalPageBreakBehaviour) !== null && _m !== void 0 ? _m : 'afterAllRows',\r\n    };\r\n}\r\nfunction getStartY(doc, userStartY) {\r\n    var previous = doc.getLastAutoTable();\r\n    var sf = doc.scaleFactor();\r\n    var currentPage = doc.pageNumber();\r\n    var isSamePageAsPreviousTable = false;\r\n    if (previous && previous.startPageNumber) {\r\n        var endingPage = previous.startPageNumber + previous.pageNumber - 1;\r\n        isSamePageAsPreviousTable = endingPage === currentPage;\r\n    }\r\n    if (typeof userStartY === 'number') {\r\n        return userStartY;\r\n    }\r\n    else if (userStartY == null || userStartY === false) {\r\n        if (isSamePageAsPreviousTable && (previous === null || previous === void 0 ? void 0 : previous.finalY) != null) {\r\n            // Some users had issues with overlapping tables when they used multiple\r\n            // tables without setting startY so setting it here to a sensible default.\r\n            return previous.finalY + 20 / sf;\r\n        }\r\n    }\r\n    return null;\r\n}\r\nfunction parseContent$1(doc, options, window) {\r\n    var head = options.head || [];\r\n    var body = options.body || [];\r\n    var foot = options.foot || [];\r\n    if (options.html) {\r\n        var hidden = options.includeHiddenHtml;\r\n        if (window) {\r\n            var htmlContent = parseHtml(doc, options.html, window, hidden, options.useCss) || {};\r\n            head = htmlContent.head || head;\r\n            body = htmlContent.body || head;\r\n            foot = htmlContent.foot || head;\r\n        }\r\n        else {\r\n            console.error('Cannot parse html in non browser environment');\r\n        }\r\n    }\r\n    var columns = options.columns || parseColumns(head, body, foot);\r\n    return { columns: columns, head: head, body: body, foot: foot };\r\n}\r\nfunction parseColumns(head, body, foot) {\r\n    var firstRow = head[0] || body[0] || foot[0] || [];\r\n    var result = [];\r\n    Object.keys(firstRow)\r\n        .filter(function (key) { return key !== '_element'; })\r\n        .forEach(function (key) {\r\n        var colSpan = 1;\r\n        var input;\r\n        if (Array.isArray(firstRow)) {\r\n            input = firstRow[parseInt(key)];\r\n        }\r\n        else {\r\n            input = firstRow[key];\r\n        }\r\n        if (typeof input === 'object' && !Array.isArray(input)) {\r\n            colSpan = (input === null || input === void 0 ? void 0 : input.colSpan) || 1;\r\n        }\r\n        for (var i = 0; i < colSpan; i++) {\r\n            var id = void 0;\r\n            if (Array.isArray(firstRow)) {\r\n                id = result.length;\r\n            }\r\n            else {\r\n                id = key + (i > 0 ? \"_\".concat(i) : '');\r\n            }\r\n            var rowResult = { dataKey: id };\r\n            result.push(rowResult);\r\n        }\r\n    });\r\n    return result;\r\n}\r\n\r\nvar HookData = /** @class */ (function () {\r\n    function HookData(doc, table, cursor) {\r\n        this.table = table;\r\n        this.pageNumber = table.pageNumber;\r\n        this.settings = table.settings;\r\n        this.cursor = cursor;\r\n        this.doc = doc.getDocument();\r\n    }\r\n    return HookData;\r\n}());\r\nvar CellHookData = /** @class */ (function (_super) {\r\n    __extends(CellHookData, _super);\r\n    function CellHookData(doc, table, cell, row, column, cursor) {\r\n        var _this = _super.call(this, doc, table, cursor) || this;\r\n        _this.cell = cell;\r\n        _this.row = row;\r\n        _this.column = column;\r\n        _this.section = row.section;\r\n        return _this;\r\n    }\r\n    return CellHookData;\r\n}(HookData));\r\n\r\nvar Table = /** @class */ (function () {\r\n    function Table(input, content) {\r\n        this.pageNumber = 1;\r\n        this.id = input.id;\r\n        this.settings = input.settings;\r\n        this.styles = input.styles;\r\n        this.hooks = input.hooks;\r\n        this.columns = content.columns;\r\n        this.head = content.head;\r\n        this.body = content.body;\r\n        this.foot = content.foot;\r\n    }\r\n    Table.prototype.getHeadHeight = function (columns) {\r\n        return this.head.reduce(function (acc, row) { return acc + row.getMaxCellHeight(columns); }, 0);\r\n    };\r\n    Table.prototype.getFootHeight = function (columns) {\r\n        return this.foot.reduce(function (acc, row) { return acc + row.getMaxCellHeight(columns); }, 0);\r\n    };\r\n    Table.prototype.allRows = function () {\r\n        return this.head.concat(this.body).concat(this.foot);\r\n    };\r\n    Table.prototype.callCellHooks = function (doc, handlers, cell, row, column, cursor) {\r\n        for (var _i = 0, handlers_1 = handlers; _i < handlers_1.length; _i++) {\r\n            var handler = handlers_1[_i];\r\n            var data = new CellHookData(doc, this, cell, row, column, cursor);\r\n            var result = handler(data) === false;\r\n            // Make sure text is always string[] since user can assign string\r\n            cell.text = Array.isArray(cell.text) ? cell.text : [cell.text];\r\n            if (result) {\r\n                return false;\r\n            }\r\n        }\r\n        return true;\r\n    };\r\n    Table.prototype.callEndPageHooks = function (doc, cursor) {\r\n        doc.applyStyles(doc.userStyles);\r\n        for (var _i = 0, _a = this.hooks.didDrawPage; _i < _a.length; _i++) {\r\n            var handler = _a[_i];\r\n            handler(new HookData(doc, this, cursor));\r\n        }\r\n    };\r\n    Table.prototype.callWillDrawPageHooks = function (doc, cursor) {\r\n        for (var _i = 0, _a = this.hooks.willDrawPage; _i < _a.length; _i++) {\r\n            var handler = _a[_i];\r\n            handler(new HookData(doc, this, cursor));\r\n        }\r\n    };\r\n    Table.prototype.getWidth = function (pageWidth) {\r\n        if (typeof this.settings.tableWidth === 'number') {\r\n            return this.settings.tableWidth;\r\n        }\r\n        else if (this.settings.tableWidth === 'wrap') {\r\n            var wrappedWidth = this.columns.reduce(function (total, col) { return total + col.wrappedWidth; }, 0);\r\n            return wrappedWidth;\r\n        }\r\n        else {\r\n            var margin = this.settings.margin;\r\n            return pageWidth - margin.left - margin.right;\r\n        }\r\n    };\r\n    return Table;\r\n}());\r\nvar Row = /** @class */ (function () {\r\n    function Row(raw, index, section, cells, spansMultiplePages) {\r\n        if (spansMultiplePages === void 0) { spansMultiplePages = false; }\r\n        this.height = 0;\r\n        this.raw = raw;\r\n        if (raw instanceof HtmlRowInput) {\r\n            this.raw = raw._element;\r\n            this.element = raw._element;\r\n        }\r\n        this.index = index;\r\n        this.section = section;\r\n        this.cells = cells;\r\n        this.spansMultiplePages = spansMultiplePages;\r\n    }\r\n    Row.prototype.getMaxCellHeight = function (columns) {\r\n        var _this = this;\r\n        return columns.reduce(function (acc, column) { var _a; return Math.max(acc, ((_a = _this.cells[column.index]) === null || _a === void 0 ? void 0 : _a.height) || 0); }, 0);\r\n    };\r\n    Row.prototype.hasRowSpan = function (columns) {\r\n        var _this = this;\r\n        return (columns.filter(function (column) {\r\n            var cell = _this.cells[column.index];\r\n            if (!cell)\r\n                return false;\r\n            return cell.rowSpan > 1;\r\n        }).length > 0);\r\n    };\r\n    Row.prototype.canEntireRowFit = function (height, columns) {\r\n        return this.getMaxCellHeight(columns) <= height;\r\n    };\r\n    Row.prototype.getMinimumRowHeight = function (columns, doc) {\r\n        var _this = this;\r\n        return columns.reduce(function (acc, column) {\r\n            var cell = _this.cells[column.index];\r\n            if (!cell)\r\n                return 0;\r\n            var lineHeight = doc.getLineHeight(cell.styles.fontSize);\r\n            var vPadding = cell.padding('vertical');\r\n            var oneRowHeight = vPadding + lineHeight;\r\n            return oneRowHeight > acc ? oneRowHeight : acc;\r\n        }, 0);\r\n    };\r\n    return Row;\r\n}());\r\nvar Cell = /** @class */ (function () {\r\n    function Cell(raw, styles, section) {\r\n        var _a;\r\n        this.contentHeight = 0;\r\n        this.contentWidth = 0;\r\n        this.wrappedWidth = 0;\r\n        this.minReadableWidth = 0;\r\n        this.minWidth = 0;\r\n        this.width = 0;\r\n        this.height = 0;\r\n        this.x = 0;\r\n        this.y = 0;\r\n        this.styles = styles;\r\n        this.section = section;\r\n        this.raw = raw;\r\n        var content = raw;\r\n        if (raw != null && typeof raw === 'object' && !Array.isArray(raw)) {\r\n            this.rowSpan = raw.rowSpan || 1;\r\n            this.colSpan = raw.colSpan || 1;\r\n            content = (_a = raw.content) !== null && _a !== void 0 ? _a : raw;\r\n            if (raw._element) {\r\n                this.raw = raw._element;\r\n            }\r\n        }\r\n        else {\r\n            this.rowSpan = 1;\r\n            this.colSpan = 1;\r\n        }\r\n        // Stringify 0 and false, but not undefined or null\r\n        var text = content != null ? '' + content : '';\r\n        var splitRegex = /\\r\\n|\\r|\\n/g;\r\n        this.text = text.split(splitRegex);\r\n    }\r\n    Cell.prototype.getTextPos = function () {\r\n        var y;\r\n        if (this.styles.valign === 'top') {\r\n            y = this.y + this.padding('top');\r\n        }\r\n        else if (this.styles.valign === 'bottom') {\r\n            y = this.y + this.height - this.padding('bottom');\r\n        }\r\n        else {\r\n            var netHeight = this.height - this.padding('vertical');\r\n            y = this.y + netHeight / 2 + this.padding('top');\r\n        }\r\n        var x;\r\n        if (this.styles.halign === 'right') {\r\n            x = this.x + this.width - this.padding('right');\r\n        }\r\n        else if (this.styles.halign === 'center') {\r\n            var netWidth = this.width - this.padding('horizontal');\r\n            x = this.x + netWidth / 2 + this.padding('left');\r\n        }\r\n        else {\r\n            x = this.x + this.padding('left');\r\n        }\r\n        return { x: x, y: y };\r\n    };\r\n    // TODO (v4): replace parameters with only (lineHeight)\r\n    Cell.prototype.getContentHeight = function (scaleFactor, lineHeightFactor) {\r\n        if (lineHeightFactor === void 0) { lineHeightFactor = 1.15; }\r\n        var lineCount = Array.isArray(this.text) ? this.text.length : 1;\r\n        var lineHeight = (this.styles.fontSize / scaleFactor) * lineHeightFactor;\r\n        var height = lineCount * lineHeight + this.padding('vertical');\r\n        return Math.max(height, this.styles.minCellHeight);\r\n    };\r\n    Cell.prototype.padding = function (name) {\r\n        var padding = parseSpacing(this.styles.cellPadding, 0);\r\n        if (name === 'vertical') {\r\n            return padding.top + padding.bottom;\r\n        }\r\n        else if (name === 'horizontal') {\r\n            return padding.left + padding.right;\r\n        }\r\n        else {\r\n            return padding[name];\r\n        }\r\n    };\r\n    return Cell;\r\n}());\r\nvar Column = /** @class */ (function () {\r\n    function Column(dataKey, raw, index) {\r\n        this.wrappedWidth = 0;\r\n        this.minReadableWidth = 0;\r\n        this.minWidth = 0;\r\n        this.width = 0;\r\n        this.dataKey = dataKey;\r\n        this.raw = raw;\r\n        this.index = index;\r\n    }\r\n    Column.prototype.getMaxCustomCellWidth = function (table) {\r\n        var max = 0;\r\n        for (var _i = 0, _a = table.allRows(); _i < _a.length; _i++) {\r\n            var row = _a[_i];\r\n            var cell = row.cells[this.index];\r\n            if (cell && typeof cell.styles.cellWidth === 'number') {\r\n                max = Math.max(max, cell.styles.cellWidth);\r\n            }\r\n        }\r\n        return max;\r\n    };\r\n    return Column;\r\n}());\r\n\r\n/**\r\n * Calculate the column widths\r\n */\r\nfunction calculateWidths(doc, table) {\r\n    calculate(doc, table);\r\n    var resizableColumns = [];\r\n    var initialTableWidth = 0;\r\n    table.columns.forEach(function (column) {\r\n        var customWidth = column.getMaxCustomCellWidth(table);\r\n        if (customWidth) {\r\n            // final column width\r\n            column.width = customWidth;\r\n        }\r\n        else {\r\n            // initial column width (will be resized)\r\n            column.width = column.wrappedWidth;\r\n            resizableColumns.push(column);\r\n        }\r\n        initialTableWidth += column.width;\r\n    });\r\n    // width difference that needs to be distributed\r\n    var resizeWidth = table.getWidth(doc.pageSize().width) - initialTableWidth;\r\n    // first resize attempt: with respect to minReadableWidth and minWidth\r\n    if (resizeWidth) {\r\n        resizeWidth = resizeColumns(resizableColumns, resizeWidth, function (column) {\r\n            return Math.max(column.minReadableWidth, column.minWidth);\r\n        });\r\n    }\r\n    // second resize attempt: ignore minReadableWidth but respect minWidth\r\n    if (resizeWidth) {\r\n        resizeWidth = resizeColumns(resizableColumns, resizeWidth, function (column) { return column.minWidth; });\r\n    }\r\n    resizeWidth = Math.abs(resizeWidth);\r\n    if (!table.settings.horizontalPageBreak &&\r\n        resizeWidth > 0.1 / doc.scaleFactor()) {\r\n        // Table can't get smaller due to custom-width or minWidth restrictions\r\n        // We can't really do much here. Up to user to for example\r\n        // reduce font size, increase page size or remove custom cell widths\r\n        // to allow more columns to be reduced in size\r\n        resizeWidth = resizeWidth < 1 ? resizeWidth : Math.round(resizeWidth);\r\n        console.warn(\"Of the table content, \".concat(resizeWidth, \" units width could not fit page\"));\r\n    }\r\n    applyColSpans(table);\r\n    fitContent(table, doc);\r\n    applyRowSpans(table);\r\n}\r\nfunction calculate(doc, table) {\r\n    var sf = doc.scaleFactor();\r\n    var horizontalPageBreak = table.settings.horizontalPageBreak;\r\n    var availablePageWidth = getPageAvailableWidth(doc, table);\r\n    table.allRows().forEach(function (row) {\r\n        for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\r\n            var column = _a[_i];\r\n            var cell = row.cells[column.index];\r\n            if (!cell)\r\n                continue;\r\n            var hooks = table.hooks.didParseCell;\r\n            table.callCellHooks(doc, hooks, cell, row, column, null);\r\n            var padding = cell.padding('horizontal');\r\n            cell.contentWidth = getStringWidth(cell.text, cell.styles, doc) + padding;\r\n            // Using [^\\S\\u00A0] instead of \\s ensures that we split the text on all\r\n            // whitespace except non-breaking spaces (\\u00A0). We need to preserve\r\n            // them in the split process to ensure correct word separation and width\r\n            // calculation.\r\n            var longestWordWidth = getStringWidth(cell.text.join(' ').split(/[^\\S\\u00A0]+/), cell.styles, doc);\r\n            cell.minReadableWidth = longestWordWidth + cell.padding('horizontal');\r\n            if (typeof cell.styles.cellWidth === 'number') {\r\n                cell.minWidth = cell.styles.cellWidth;\r\n                cell.wrappedWidth = cell.styles.cellWidth;\r\n            }\r\n            else if (cell.styles.cellWidth === 'wrap' ||\r\n                horizontalPageBreak === true) {\r\n                // cell width should not be more than available page width\r\n                if (cell.contentWidth > availablePageWidth) {\r\n                    cell.minWidth = availablePageWidth;\r\n                    cell.wrappedWidth = availablePageWidth;\r\n                }\r\n                else {\r\n                    cell.minWidth = cell.contentWidth;\r\n                    cell.wrappedWidth = cell.contentWidth;\r\n                }\r\n            }\r\n            else {\r\n                // auto\r\n                var defaultMinWidth = 10 / sf;\r\n                cell.minWidth = cell.styles.minCellWidth || defaultMinWidth;\r\n                cell.wrappedWidth = cell.contentWidth;\r\n                if (cell.minWidth > cell.wrappedWidth) {\r\n                    cell.wrappedWidth = cell.minWidth;\r\n                }\r\n            }\r\n        }\r\n    });\r\n    table.allRows().forEach(function (row) {\r\n        for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\r\n            var column = _a[_i];\r\n            var cell = row.cells[column.index];\r\n            // For now we ignore the minWidth and wrappedWidth of colspan cells when calculating colspan widths.\r\n            // Could probably be improved upon however.\r\n            if (cell && cell.colSpan === 1) {\r\n                column.wrappedWidth = Math.max(column.wrappedWidth, cell.wrappedWidth);\r\n                column.minWidth = Math.max(column.minWidth, cell.minWidth);\r\n                column.minReadableWidth = Math.max(column.minReadableWidth, cell.minReadableWidth);\r\n            }\r\n            else {\r\n                // Respect cellWidth set in columnStyles even if there is no cells for this column\r\n                // or if the column only have colspan cells. Since the width of colspan cells\r\n                // does not affect the width of columns, setting columnStyles cellWidth enables the\r\n                // user to at least do it manually.\r\n                // Note that this is not perfect for now since for example row and table styles are\r\n                // not accounted for\r\n                var columnStyles = table.styles.columnStyles[column.dataKey] ||\r\n                    table.styles.columnStyles[column.index] ||\r\n                    {};\r\n                var cellWidth = columnStyles.cellWidth || columnStyles.minCellWidth;\r\n                if (cellWidth && typeof cellWidth === 'number') {\r\n                    column.minWidth = cellWidth;\r\n                    column.wrappedWidth = cellWidth;\r\n                }\r\n            }\r\n            if (cell) {\r\n                // Make sure all columns get at least min width even though width calculations are not based on them\r\n                if (cell.colSpan > 1 && !column.minWidth) {\r\n                    column.minWidth = cell.minWidth;\r\n                }\r\n                if (cell.colSpan > 1 && !column.wrappedWidth) {\r\n                    column.wrappedWidth = cell.minWidth;\r\n                }\r\n            }\r\n        }\r\n    });\r\n}\r\n/**\r\n * Distribute resizeWidth on passed resizable columns\r\n */\r\nfunction resizeColumns(columns, resizeWidth, getMinWidth) {\r\n    var initialResizeWidth = resizeWidth;\r\n    var sumWrappedWidth = columns.reduce(function (acc, column) { return acc + column.wrappedWidth; }, 0);\r\n    for (var i = 0; i < columns.length; i++) {\r\n        var column = columns[i];\r\n        var ratio = column.wrappedWidth / sumWrappedWidth;\r\n        var suggestedChange = initialResizeWidth * ratio;\r\n        var suggestedWidth = column.width + suggestedChange;\r\n        var minWidth = getMinWidth(column);\r\n        var newWidth = suggestedWidth < minWidth ? minWidth : suggestedWidth;\r\n        resizeWidth -= newWidth - column.width;\r\n        column.width = newWidth;\r\n    }\r\n    resizeWidth = Math.round(resizeWidth * 1e10) / 1e10;\r\n    // Run the resizer again if there's remaining width needs\r\n    // to be distributed and there're columns that can be resized\r\n    if (resizeWidth) {\r\n        var resizableColumns = columns.filter(function (column) {\r\n            return resizeWidth < 0\r\n                ? column.width > getMinWidth(column) // check if column can shrink\r\n                : true; // check if column can grow\r\n        });\r\n        if (resizableColumns.length) {\r\n            resizeWidth = resizeColumns(resizableColumns, resizeWidth, getMinWidth);\r\n        }\r\n    }\r\n    return resizeWidth;\r\n}\r\nfunction applyRowSpans(table) {\r\n    var rowSpanCells = {};\r\n    var colRowSpansLeft = 1;\r\n    var all = table.allRows();\r\n    for (var rowIndex = 0; rowIndex < all.length; rowIndex++) {\r\n        var row = all[rowIndex];\r\n        for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\r\n            var column = _a[_i];\r\n            var data = rowSpanCells[column.index];\r\n            if (colRowSpansLeft > 1) {\r\n                colRowSpansLeft--;\r\n                delete row.cells[column.index];\r\n            }\r\n            else if (data) {\r\n                data.cell.height += row.height;\r\n                colRowSpansLeft = data.cell.colSpan;\r\n                delete row.cells[column.index];\r\n                data.left--;\r\n                if (data.left <= 1) {\r\n                    delete rowSpanCells[column.index];\r\n                }\r\n            }\r\n            else {\r\n                var cell = row.cells[column.index];\r\n                if (!cell) {\r\n                    continue;\r\n                }\r\n                cell.height = row.height;\r\n                if (cell.rowSpan > 1) {\r\n                    var remaining = all.length - rowIndex;\r\n                    var left = cell.rowSpan > remaining ? remaining : cell.rowSpan;\r\n                    rowSpanCells[column.index] = { cell: cell, left: left, row: row };\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\nfunction applyColSpans(table) {\r\n    var all = table.allRows();\r\n    for (var rowIndex = 0; rowIndex < all.length; rowIndex++) {\r\n        var row = all[rowIndex];\r\n        var colSpanCell = null;\r\n        var combinedColSpanWidth = 0;\r\n        var colSpansLeft = 0;\r\n        for (var columnIndex = 0; columnIndex < table.columns.length; columnIndex++) {\r\n            var column = table.columns[columnIndex];\r\n            // Width and colspan\r\n            colSpansLeft -= 1;\r\n            if (colSpansLeft > 1 && table.columns[columnIndex + 1]) {\r\n                combinedColSpanWidth += column.width;\r\n                delete row.cells[column.index];\r\n            }\r\n            else if (colSpanCell) {\r\n                var cell = colSpanCell;\r\n                delete row.cells[column.index];\r\n                colSpanCell = null;\r\n                cell.width = column.width + combinedColSpanWidth;\r\n            }\r\n            else {\r\n                var cell = row.cells[column.index];\r\n                if (!cell)\r\n                    continue;\r\n                colSpansLeft = cell.colSpan;\r\n                combinedColSpanWidth = 0;\r\n                if (cell.colSpan > 1) {\r\n                    colSpanCell = cell;\r\n                    combinedColSpanWidth += column.width;\r\n                    continue;\r\n                }\r\n                cell.width = column.width + combinedColSpanWidth;\r\n            }\r\n        }\r\n    }\r\n}\r\nfunction fitContent(table, doc) {\r\n    var rowSpanHeight = { count: 0, height: 0 };\r\n    for (var _i = 0, _a = table.allRows(); _i < _a.length; _i++) {\r\n        var row = _a[_i];\r\n        for (var _b = 0, _c = table.columns; _b < _c.length; _b++) {\r\n            var column = _c[_b];\r\n            var cell = row.cells[column.index];\r\n            if (!cell)\r\n                continue;\r\n            doc.applyStyles(cell.styles, true);\r\n            var textSpace = cell.width - cell.padding('horizontal');\r\n            if (cell.styles.overflow === 'linebreak') {\r\n                // Add one pt to textSpace to fix rounding error\r\n                cell.text = doc.splitTextToSize(cell.text, textSpace + 1 / doc.scaleFactor(), { fontSize: cell.styles.fontSize });\r\n            }\r\n            else if (cell.styles.overflow === 'ellipsize') {\r\n                cell.text = ellipsize(cell.text, textSpace, cell.styles, doc, '...');\r\n            }\r\n            else if (cell.styles.overflow === 'hidden') {\r\n                cell.text = ellipsize(cell.text, textSpace, cell.styles, doc, '');\r\n            }\r\n            else if (typeof cell.styles.overflow === 'function') {\r\n                var result = cell.styles.overflow(cell.text, textSpace);\r\n                if (typeof result === 'string') {\r\n                    cell.text = [result];\r\n                }\r\n                else {\r\n                    cell.text = result;\r\n                }\r\n            }\r\n            cell.contentHeight = cell.getContentHeight(doc.scaleFactor(), doc.getLineHeightFactor());\r\n            var realContentHeight = cell.contentHeight / cell.rowSpan;\r\n            if (cell.rowSpan > 1 &&\r\n                rowSpanHeight.count * rowSpanHeight.height <\r\n                    realContentHeight * cell.rowSpan) {\r\n                rowSpanHeight = { height: realContentHeight, count: cell.rowSpan };\r\n            }\r\n            else if (rowSpanHeight && rowSpanHeight.count > 0) {\r\n                if (rowSpanHeight.height > realContentHeight) {\r\n                    realContentHeight = rowSpanHeight.height;\r\n                }\r\n            }\r\n            if (realContentHeight > row.height) {\r\n                row.height = realContentHeight;\r\n            }\r\n        }\r\n        rowSpanHeight.count--;\r\n    }\r\n}\r\nfunction ellipsize(text, width, styles, doc, overflow) {\r\n    return text.map(function (str) { return ellipsizeStr(str, width, styles, doc, overflow); });\r\n}\r\nfunction ellipsizeStr(text, width, styles, doc, overflow) {\r\n    var precision = 10000 * doc.scaleFactor();\r\n    width = Math.ceil(width * precision) / precision;\r\n    if (width >= getStringWidth(text, styles, doc)) {\r\n        return text;\r\n    }\r\n    while (width < getStringWidth(text + overflow, styles, doc)) {\r\n        if (text.length <= 1) {\r\n            break;\r\n        }\r\n        text = text.substring(0, text.length - 1);\r\n    }\r\n    return text.trim() + overflow;\r\n}\r\n\r\nfunction createTable(jsPDFDoc, input) {\r\n    var doc = new DocHandler(jsPDFDoc);\r\n    var content = parseContent(input, doc.scaleFactor());\r\n    var table = new Table(input, content);\r\n    calculateWidths(doc, table);\r\n    doc.applyStyles(doc.userStyles);\r\n    return table;\r\n}\r\nfunction parseContent(input, sf) {\r\n    var content = input.content;\r\n    var columns = createColumns(content.columns);\r\n    // If no head or foot is set, try generating it with content from columns\r\n    if (content.head.length === 0) {\r\n        var sectionRow = generateSectionRow(columns, 'head');\r\n        if (sectionRow)\r\n            content.head.push(sectionRow);\r\n    }\r\n    if (content.foot.length === 0) {\r\n        var sectionRow = generateSectionRow(columns, 'foot');\r\n        if (sectionRow)\r\n            content.foot.push(sectionRow);\r\n    }\r\n    var theme = input.settings.theme;\r\n    var styles = input.styles;\r\n    return {\r\n        columns: columns,\r\n        head: parseSection('head', content.head, columns, styles, theme, sf),\r\n        body: parseSection('body', content.body, columns, styles, theme, sf),\r\n        foot: parseSection('foot', content.foot, columns, styles, theme, sf),\r\n    };\r\n}\r\nfunction parseSection(sectionName, sectionRows, columns, styleProps, theme, scaleFactor) {\r\n    var rowSpansLeftForColumn = {};\r\n    var result = sectionRows.map(function (rawRow, rowIndex) {\r\n        var skippedRowForRowSpans = 0;\r\n        var cells = {};\r\n        var colSpansAdded = 0;\r\n        var columnSpansLeft = 0;\r\n        for (var _i = 0, columns_1 = columns; _i < columns_1.length; _i++) {\r\n            var column = columns_1[_i];\r\n            if (rowSpansLeftForColumn[column.index] == null ||\r\n                rowSpansLeftForColumn[column.index].left === 0) {\r\n                if (columnSpansLeft === 0) {\r\n                    var rawCell = void 0;\r\n                    if (Array.isArray(rawRow)) {\r\n                        rawCell =\r\n                            rawRow[column.index - colSpansAdded - skippedRowForRowSpans];\r\n                    }\r\n                    else {\r\n                        rawCell = rawRow[column.dataKey];\r\n                    }\r\n                    var cellInputStyles = {};\r\n                    if (typeof rawCell === 'object' && !Array.isArray(rawCell)) {\r\n                        cellInputStyles = (rawCell === null || rawCell === void 0 ? void 0 : rawCell.styles) || {};\r\n                    }\r\n                    var styles = cellStyles(sectionName, column, rowIndex, theme, styleProps, scaleFactor, cellInputStyles);\r\n                    var cell = new Cell(rawCell, styles, sectionName);\r\n                    // dataKey is not used internally no more but keep for\r\n                    // backwards compat in hooks\r\n                    cells[column.dataKey] = cell;\r\n                    cells[column.index] = cell;\r\n                    columnSpansLeft = cell.colSpan - 1;\r\n                    rowSpansLeftForColumn[column.index] = {\r\n                        left: cell.rowSpan - 1,\r\n                        times: columnSpansLeft,\r\n                    };\r\n                }\r\n                else {\r\n                    columnSpansLeft--;\r\n                    colSpansAdded++;\r\n                }\r\n            }\r\n            else {\r\n                rowSpansLeftForColumn[column.index].left--;\r\n                columnSpansLeft = rowSpansLeftForColumn[column.index].times;\r\n                skippedRowForRowSpans++;\r\n            }\r\n        }\r\n        return new Row(rawRow, rowIndex, sectionName, cells);\r\n    });\r\n    return result;\r\n}\r\nfunction generateSectionRow(columns, section) {\r\n    var sectionRow = {};\r\n    columns.forEach(function (col) {\r\n        if (col.raw != null) {\r\n            var title = getSectionTitle(section, col.raw);\r\n            if (title != null)\r\n                sectionRow[col.dataKey] = title;\r\n        }\r\n    });\r\n    return Object.keys(sectionRow).length > 0 ? sectionRow : null;\r\n}\r\nfunction getSectionTitle(section, column) {\r\n    if (section === 'head') {\r\n        if (typeof column === 'object') {\r\n            return column.header || null;\r\n        }\r\n        else if (typeof column === 'string' || typeof column === 'number') {\r\n            return column;\r\n        }\r\n    }\r\n    else if (section === 'foot' && typeof column === 'object') {\r\n        return column.footer;\r\n    }\r\n    return null;\r\n}\r\nfunction createColumns(columns) {\r\n    return columns.map(function (input, index) {\r\n        var _a;\r\n        var key;\r\n        if (typeof input === 'object') {\r\n            key = (_a = input.dataKey) !== null && _a !== void 0 ? _a : index;\r\n        }\r\n        else {\r\n            key = index;\r\n        }\r\n        return new Column(key, input, index);\r\n    });\r\n}\r\nfunction cellStyles(sectionName, column, rowIndex, themeName, styles, scaleFactor, cellInputStyles) {\r\n    var theme = getTheme(themeName);\r\n    var sectionStyles;\r\n    if (sectionName === 'head') {\r\n        sectionStyles = styles.headStyles;\r\n    }\r\n    else if (sectionName === 'body') {\r\n        sectionStyles = styles.bodyStyles;\r\n    }\r\n    else if (sectionName === 'foot') {\r\n        sectionStyles = styles.footStyles;\r\n    }\r\n    var otherStyles = assign({}, theme.table, theme[sectionName], styles.styles, sectionStyles);\r\n    var columnStyles = styles.columnStyles[column.dataKey] ||\r\n        styles.columnStyles[column.index] ||\r\n        {};\r\n    var colStyles = sectionName === 'body' ? columnStyles : {};\r\n    var rowStyles = sectionName === 'body' && rowIndex % 2 === 0\r\n        ? assign({}, theme.alternateRow, styles.alternateRowStyles)\r\n        : {};\r\n    var defaultStyle = defaultStyles(scaleFactor);\r\n    var themeStyles = assign({}, defaultStyle, otherStyles, rowStyles, colStyles);\r\n    return assign(themeStyles, cellInputStyles);\r\n}\r\n\r\n// get columns can be fit into page\r\nfunction getColumnsCanFitInPage(doc, table, config) {\r\n    var _a;\r\n    if (config === void 0) { config = {}; }\r\n    // Get page width\r\n    var remainingWidth = getPageAvailableWidth(doc, table);\r\n    // Get column data key to repeat\r\n    var repeatColumnsMap = new Map();\r\n    var colIndexes = [];\r\n    var columns = [];\r\n    var horizontalPageBreakRepeat = [];\r\n    if (Array.isArray(table.settings.horizontalPageBreakRepeat)) {\r\n        horizontalPageBreakRepeat = table.settings.horizontalPageBreakRepeat;\r\n        // It can be a single value of type string or number (even number: 0)\r\n    }\r\n    else if (typeof table.settings.horizontalPageBreakRepeat === 'string' ||\r\n        typeof table.settings.horizontalPageBreakRepeat === 'number') {\r\n        horizontalPageBreakRepeat = [table.settings.horizontalPageBreakRepeat];\r\n    }\r\n    // Code to repeat the given column in split pages\r\n    horizontalPageBreakRepeat.forEach(function (field) {\r\n        var col = table.columns.find(function (item) { return item.dataKey === field || item.index === field; });\r\n        if (col && !repeatColumnsMap.has(col.index)) {\r\n            repeatColumnsMap.set(col.index, true);\r\n            colIndexes.push(col.index);\r\n            columns.push(table.columns[col.index]);\r\n            remainingWidth -= col.wrappedWidth;\r\n        }\r\n    });\r\n    var first = true;\r\n    var i = (_a = config === null || config === void 0 ? void 0 : config.start) !== null && _a !== void 0 ? _a : 0; // make sure couter is initiated outside the loop\r\n    while (i < table.columns.length) {\r\n        // Prevent duplicates\r\n        if (repeatColumnsMap.has(i)) {\r\n            i++;\r\n            continue;\r\n        }\r\n        var colWidth = table.columns[i].wrappedWidth;\r\n        // Take at least one column even if it doesn't fit\r\n        if (first || remainingWidth >= colWidth) {\r\n            first = false;\r\n            colIndexes.push(i);\r\n            columns.push(table.columns[i]);\r\n            remainingWidth -= colWidth;\r\n        }\r\n        else {\r\n            break;\r\n        }\r\n        i++;\r\n    }\r\n    return { colIndexes: colIndexes, columns: columns, lastIndex: i - 1 };\r\n}\r\nfunction calculateAllColumnsCanFitInPage(doc, table) {\r\n    var allResults = [];\r\n    for (var i = 0; i < table.columns.length; i++) {\r\n        var result = getColumnsCanFitInPage(doc, table, { start: i });\r\n        if (result.columns.length) {\r\n            allResults.push(result);\r\n            i = result.lastIndex;\r\n        }\r\n    }\r\n    return allResults;\r\n}\r\n\r\nfunction drawTable(jsPDFDoc, table) {\r\n    var settings = table.settings;\r\n    var startY = settings.startY;\r\n    var margin = settings.margin;\r\n    var cursor = { x: margin.left, y: startY };\r\n    var sectionsHeight = table.getHeadHeight(table.columns) + table.getFootHeight(table.columns);\r\n    var minTableBottomPos = startY + margin.bottom + sectionsHeight;\r\n    if (settings.pageBreak === 'avoid') {\r\n        var rows = table.body;\r\n        var tableHeight = rows.reduce(function (acc, row) { return acc + row.height; }, 0);\r\n        minTableBottomPos += tableHeight;\r\n    }\r\n    var doc = new DocHandler(jsPDFDoc);\r\n    if (settings.pageBreak === 'always' ||\r\n        (settings.startY != null && minTableBottomPos > doc.pageSize().height)) {\r\n        nextPage(doc);\r\n        cursor.y = margin.top;\r\n    }\r\n    table.callWillDrawPageHooks(doc, cursor);\r\n    var startPos = assign({}, cursor);\r\n    table.startPageNumber = doc.pageNumber();\r\n    if (settings.horizontalPageBreak) {\r\n        // managed flow for split columns\r\n        printTableWithHorizontalPageBreak(doc, table, startPos, cursor);\r\n    }\r\n    else {\r\n        // normal flow\r\n        doc.applyStyles(doc.userStyles);\r\n        if (settings.showHead === 'firstPage' ||\r\n            settings.showHead === 'everyPage') {\r\n            table.head.forEach(function (row) {\r\n                return printRow(doc, table, row, cursor, table.columns);\r\n            });\r\n        }\r\n        doc.applyStyles(doc.userStyles);\r\n        table.body.forEach(function (row, index) {\r\n            var isLastRow = index === table.body.length - 1;\r\n            printFullRow(doc, table, row, isLastRow, startPos, cursor, table.columns);\r\n        });\r\n        doc.applyStyles(doc.userStyles);\r\n        if (settings.showFoot === 'lastPage' || settings.showFoot === 'everyPage') {\r\n            table.foot.forEach(function (row) {\r\n                return printRow(doc, table, row, cursor, table.columns);\r\n            });\r\n        }\r\n    }\r\n    addTableBorder(doc, table, startPos, cursor);\r\n    table.callEndPageHooks(doc, cursor);\r\n    table.finalY = cursor.y;\r\n    jsPDFDoc.lastAutoTable = table;\r\n    doc.applyStyles(doc.userStyles);\r\n}\r\nfunction printTableWithHorizontalPageBreak(doc, table, startPos, cursor) {\r\n    // calculate width of columns and render only those which can fit into page\r\n    var allColumnsCanFitResult = calculateAllColumnsCanFitInPage(doc, table);\r\n    var settings = table.settings;\r\n    if (settings.horizontalPageBreakBehaviour === 'afterAllRows') {\r\n        allColumnsCanFitResult.forEach(function (colsAndIndexes, index) {\r\n            doc.applyStyles(doc.userStyles);\r\n            // add page to print next columns in new page\r\n            if (index > 0) {\r\n                // When adding a page here, make sure not to print the footers\r\n                // because they were already printed before on this same loop\r\n                addPage(doc, table, startPos, cursor, colsAndIndexes.columns, true);\r\n            }\r\n            else {\r\n                // print head for selected columns\r\n                printHead(doc, table, cursor, colsAndIndexes.columns);\r\n            }\r\n            // print body & footer for selected columns\r\n            printBody(doc, table, startPos, cursor, colsAndIndexes.columns);\r\n            printFoot(doc, table, cursor, colsAndIndexes.columns);\r\n        });\r\n    }\r\n    else {\r\n        var lastRowIndexOfLastPage_1 = -1;\r\n        var firstColumnsToFitResult = allColumnsCanFitResult[0];\r\n        var _loop_1 = function () {\r\n            // Print the first columns, taking note of the last row printed\r\n            var lastPrintedRowIndex = lastRowIndexOfLastPage_1;\r\n            if (firstColumnsToFitResult) {\r\n                doc.applyStyles(doc.userStyles);\r\n                var firstColumnsToFit = firstColumnsToFitResult.columns;\r\n                if (lastRowIndexOfLastPage_1 >= 0) {\r\n                    // When adding a page here, make sure not to print the footers\r\n                    // because they were already printed before on this same loop\r\n                    addPage(doc, table, startPos, cursor, firstColumnsToFit, true);\r\n                }\r\n                else {\r\n                    printHead(doc, table, cursor, firstColumnsToFit);\r\n                }\r\n                lastPrintedRowIndex = printBodyWithoutPageBreaks(doc, table, lastRowIndexOfLastPage_1 + 1, cursor, firstColumnsToFit);\r\n                printFoot(doc, table, cursor, firstColumnsToFit);\r\n            }\r\n            // Check how many rows were printed, so that the next columns would not print more rows than that\r\n            var maxNumberOfRows = lastPrintedRowIndex - lastRowIndexOfLastPage_1;\r\n            // Print the next columns, never exceding maxNumberOfRows\r\n            allColumnsCanFitResult.slice(1).forEach(function (colsAndIndexes) {\r\n                doc.applyStyles(doc.userStyles);\r\n                // When adding a page here, make sure not to print the footers\r\n                // because they were already printed before on this same loop\r\n                addPage(doc, table, startPos, cursor, colsAndIndexes.columns, true);\r\n                printBodyWithoutPageBreaks(doc, table, lastRowIndexOfLastPage_1 + 1, cursor, colsAndIndexes.columns, maxNumberOfRows);\r\n                printFoot(doc, table, cursor, colsAndIndexes.columns);\r\n            });\r\n            lastRowIndexOfLastPage_1 = lastPrintedRowIndex;\r\n        };\r\n        while (lastRowIndexOfLastPage_1 < table.body.length - 1) {\r\n            _loop_1();\r\n        }\r\n    }\r\n}\r\nfunction printHead(doc, table, cursor, columns) {\r\n    var settings = table.settings;\r\n    doc.applyStyles(doc.userStyles);\r\n    if (settings.showHead === 'firstPage' || settings.showHead === 'everyPage') {\r\n        table.head.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\r\n    }\r\n}\r\nfunction printBody(doc, table, startPos, cursor, columns) {\r\n    doc.applyStyles(doc.userStyles);\r\n    table.body.forEach(function (row, index) {\r\n        var isLastRow = index === table.body.length - 1;\r\n        printFullRow(doc, table, row, isLastRow, startPos, cursor, columns);\r\n    });\r\n}\r\nfunction printBodyWithoutPageBreaks(doc, table, startRowIndex, cursor, columns, maxNumberOfRows) {\r\n    doc.applyStyles(doc.userStyles);\r\n    maxNumberOfRows = maxNumberOfRows !== null && maxNumberOfRows !== void 0 ? maxNumberOfRows : table.body.length;\r\n    var endRowIndex = Math.min(startRowIndex + maxNumberOfRows, table.body.length);\r\n    var lastPrintedRowIndex = -1;\r\n    table.body.slice(startRowIndex, endRowIndex).forEach(function (row, index) {\r\n        var isLastRow = startRowIndex + index === table.body.length - 1;\r\n        var remainingSpace = getRemainingPageSpace(doc, table, isLastRow, cursor);\r\n        if (row.canEntireRowFit(remainingSpace, columns)) {\r\n            printRow(doc, table, row, cursor, columns);\r\n            lastPrintedRowIndex = startRowIndex + index;\r\n        }\r\n    });\r\n    return lastPrintedRowIndex;\r\n}\r\nfunction printFoot(doc, table, cursor, columns) {\r\n    var settings = table.settings;\r\n    doc.applyStyles(doc.userStyles);\r\n    if (settings.showFoot === 'lastPage' || settings.showFoot === 'everyPage') {\r\n        table.foot.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\r\n    }\r\n}\r\nfunction getRemainingLineCount(cell, remainingPageSpace, doc) {\r\n    var lineHeight = doc.getLineHeight(cell.styles.fontSize);\r\n    var vPadding = cell.padding('vertical');\r\n    var remainingLines = Math.floor((remainingPageSpace - vPadding) / lineHeight);\r\n    return Math.max(0, remainingLines);\r\n}\r\nfunction modifyRowToFit(row, remainingPageSpace, table, doc) {\r\n    var cells = {};\r\n    row.spansMultiplePages = true;\r\n    row.height = 0;\r\n    var rowHeight = 0;\r\n    for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\r\n        var column = _a[_i];\r\n        var cell = row.cells[column.index];\r\n        if (!cell)\r\n            continue;\r\n        if (!Array.isArray(cell.text)) {\r\n            cell.text = [cell.text];\r\n        }\r\n        var remainderCell = new Cell(cell.raw, cell.styles, cell.section);\r\n        remainderCell = assign(remainderCell, cell);\r\n        remainderCell.text = [];\r\n        var remainingLineCount = getRemainingLineCount(cell, remainingPageSpace, doc);\r\n        if (cell.text.length > remainingLineCount) {\r\n            remainderCell.text = cell.text.splice(remainingLineCount, cell.text.length);\r\n        }\r\n        var scaleFactor = doc.scaleFactor();\r\n        var lineHeightFactor = doc.getLineHeightFactor();\r\n        cell.contentHeight = cell.getContentHeight(scaleFactor, lineHeightFactor);\r\n        if (cell.contentHeight >= remainingPageSpace) {\r\n            cell.contentHeight = remainingPageSpace;\r\n            remainderCell.styles.minCellHeight -= remainingPageSpace;\r\n        }\r\n        if (cell.contentHeight > row.height) {\r\n            row.height = cell.contentHeight;\r\n        }\r\n        remainderCell.contentHeight = remainderCell.getContentHeight(scaleFactor, lineHeightFactor);\r\n        if (remainderCell.contentHeight > rowHeight) {\r\n            rowHeight = remainderCell.contentHeight;\r\n        }\r\n        cells[column.index] = remainderCell;\r\n    }\r\n    var remainderRow = new Row(row.raw, -1, row.section, cells, true);\r\n    remainderRow.height = rowHeight;\r\n    for (var _b = 0, _c = table.columns; _b < _c.length; _b++) {\r\n        var column = _c[_b];\r\n        var remainderCell = remainderRow.cells[column.index];\r\n        if (remainderCell) {\r\n            remainderCell.height = remainderRow.height;\r\n        }\r\n        var cell = row.cells[column.index];\r\n        if (cell) {\r\n            cell.height = row.height;\r\n        }\r\n    }\r\n    return remainderRow;\r\n}\r\nfunction shouldPrintOnCurrentPage(doc, row, remainingPageSpace, table) {\r\n    var pageHeight = doc.pageSize().height;\r\n    var margin = table.settings.margin;\r\n    var marginHeight = margin.top + margin.bottom;\r\n    var maxRowHeight = pageHeight - marginHeight;\r\n    if (row.section === 'body') {\r\n        // Should also take into account that head and foot is not\r\n        // on every page with some settings\r\n        maxRowHeight -=\r\n            table.getHeadHeight(table.columns) + table.getFootHeight(table.columns);\r\n    }\r\n    var minRowHeight = row.getMinimumRowHeight(table.columns, doc);\r\n    var minRowFits = minRowHeight < remainingPageSpace;\r\n    if (minRowHeight > maxRowHeight) {\r\n        console.error(\"Will not be able to print row \".concat(row.index, \" correctly since it's minimum height is larger than page height\"));\r\n        return true;\r\n    }\r\n    if (!minRowFits) {\r\n        return false;\r\n    }\r\n    var rowHasRowSpanCell = row.hasRowSpan(table.columns);\r\n    var rowHigherThanPage = row.getMaxCellHeight(table.columns) > maxRowHeight;\r\n    if (rowHigherThanPage) {\r\n        if (rowHasRowSpanCell) {\r\n            console.error(\"The content of row \".concat(row.index, \" will not be drawn correctly since drawing rows with a height larger than the page height and has cells with rowspans is not supported.\"));\r\n        }\r\n        return true;\r\n    }\r\n    if (rowHasRowSpanCell) {\r\n        // Currently a new page is required whenever a rowspan row don't fit a page.\r\n        return false;\r\n    }\r\n    if (table.settings.rowPageBreak === 'avoid') {\r\n        return false;\r\n    }\r\n    // In all other cases print the row on current page\r\n    return true;\r\n}\r\nfunction printFullRow(doc, table, row, isLastRow, startPos, cursor, columns) {\r\n    var remainingSpace = getRemainingPageSpace(doc, table, isLastRow, cursor);\r\n    if (row.canEntireRowFit(remainingSpace, columns)) {\r\n        // The row fits in the current page\r\n        printRow(doc, table, row, cursor, columns);\r\n    }\r\n    else if (shouldPrintOnCurrentPage(doc, row, remainingSpace, table)) {\r\n        // The row gets split in two here, each piece in one page\r\n        var remainderRow = modifyRowToFit(row, remainingSpace, table, doc);\r\n        printRow(doc, table, row, cursor, columns);\r\n        addPage(doc, table, startPos, cursor, columns);\r\n        printFullRow(doc, table, remainderRow, isLastRow, startPos, cursor, columns);\r\n    }\r\n    else {\r\n        // The row get printed entirelly on the next page\r\n        addPage(doc, table, startPos, cursor, columns);\r\n        printFullRow(doc, table, row, isLastRow, startPos, cursor, columns);\r\n    }\r\n}\r\nfunction printRow(doc, table, row, cursor, columns) {\r\n    cursor.x = table.settings.margin.left;\r\n    for (var _i = 0, columns_1 = columns; _i < columns_1.length; _i++) {\r\n        var column = columns_1[_i];\r\n        var cell = row.cells[column.index];\r\n        if (!cell) {\r\n            cursor.x += column.width;\r\n            continue;\r\n        }\r\n        doc.applyStyles(cell.styles);\r\n        cell.x = cursor.x;\r\n        cell.y = cursor.y;\r\n        var result = table.callCellHooks(doc, table.hooks.willDrawCell, cell, row, column, cursor);\r\n        if (result === false) {\r\n            cursor.x += column.width;\r\n            continue;\r\n        }\r\n        drawCellRect(doc, cell, cursor);\r\n        var textPos = cell.getTextPos();\r\n        autoTableText(cell.text, textPos.x, textPos.y, {\r\n            halign: cell.styles.halign,\r\n            valign: cell.styles.valign,\r\n            maxWidth: Math.ceil(cell.width - cell.padding('left') - cell.padding('right')),\r\n        }, doc.getDocument());\r\n        table.callCellHooks(doc, table.hooks.didDrawCell, cell, row, column, cursor);\r\n        cursor.x += column.width;\r\n    }\r\n    cursor.y += row.height;\r\n}\r\nfunction drawCellRect(doc, cell, cursor) {\r\n    var cellStyles = cell.styles;\r\n    // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/774\r\n    // TODO (v4): better solution?\r\n    doc.getDocument().setFillColor(doc.getDocument().getFillColor());\r\n    if (typeof cellStyles.lineWidth === 'number') {\r\n        // Draw cell background with normal borders\r\n        var fillStyle = getFillStyle(cellStyles.lineWidth, cellStyles.fillColor);\r\n        if (fillStyle) {\r\n            doc.rect(cell.x, cursor.y, cell.width, cell.height, fillStyle);\r\n        }\r\n    }\r\n    else if (typeof cellStyles.lineWidth === 'object') {\r\n        // Draw cell background\r\n        if (cellStyles.fillColor) {\r\n            doc.rect(cell.x, cursor.y, cell.width, cell.height, 'F');\r\n        }\r\n        // Draw cell individual borders\r\n        drawCellBorders(doc, cell, cursor, cellStyles.lineWidth);\r\n    }\r\n}\r\n/**\r\n * Draw all specified borders. Borders are centered on cell's edge and lengthened\r\n * to overlap with neighbours to create sharp corners.\r\n * @param doc\r\n * @param cell\r\n * @param cursor\r\n * @param fillColor\r\n * @param lineWidth\r\n */\r\nfunction drawCellBorders(doc, cell, cursor, lineWidth) {\r\n    var x1, y1, x2, y2;\r\n    if (lineWidth.top) {\r\n        x1 = cursor.x;\r\n        y1 = cursor.y;\r\n        x2 = cursor.x + cell.width;\r\n        y2 = cursor.y;\r\n        if (lineWidth.right) {\r\n            x2 += 0.5 * lineWidth.right;\r\n        }\r\n        if (lineWidth.left) {\r\n            x1 -= 0.5 * lineWidth.left;\r\n        }\r\n        drawLine(lineWidth.top, x1, y1, x2, y2);\r\n    }\r\n    if (lineWidth.bottom) {\r\n        x1 = cursor.x;\r\n        y1 = cursor.y + cell.height;\r\n        x2 = cursor.x + cell.width;\r\n        y2 = cursor.y + cell.height;\r\n        if (lineWidth.right) {\r\n            x2 += 0.5 * lineWidth.right;\r\n        }\r\n        if (lineWidth.left) {\r\n            x1 -= 0.5 * lineWidth.left;\r\n        }\r\n        drawLine(lineWidth.bottom, x1, y1, x2, y2);\r\n    }\r\n    if (lineWidth.left) {\r\n        x1 = cursor.x;\r\n        y1 = cursor.y;\r\n        x2 = cursor.x;\r\n        y2 = cursor.y + cell.height;\r\n        if (lineWidth.top) {\r\n            y1 -= 0.5 * lineWidth.top;\r\n        }\r\n        if (lineWidth.bottom) {\r\n            y2 += 0.5 * lineWidth.bottom;\r\n        }\r\n        drawLine(lineWidth.left, x1, y1, x2, y2);\r\n    }\r\n    if (lineWidth.right) {\r\n        x1 = cursor.x + cell.width;\r\n        y1 = cursor.y;\r\n        x2 = cursor.x + cell.width;\r\n        y2 = cursor.y + cell.height;\r\n        if (lineWidth.top) {\r\n            y1 -= 0.5 * lineWidth.top;\r\n        }\r\n        if (lineWidth.bottom) {\r\n            y2 += 0.5 * lineWidth.bottom;\r\n        }\r\n        drawLine(lineWidth.right, x1, y1, x2, y2);\r\n    }\r\n    function drawLine(width, x1, y1, x2, y2) {\r\n        doc.getDocument().setLineWidth(width);\r\n        doc.getDocument().line(x1, y1, x2, y2, 'S');\r\n    }\r\n}\r\nfunction getRemainingPageSpace(doc, table, isLastRow, cursor) {\r\n    var bottomContentHeight = table.settings.margin.bottom;\r\n    var showFoot = table.settings.showFoot;\r\n    if (showFoot === 'everyPage' || (showFoot === 'lastPage' && isLastRow)) {\r\n        bottomContentHeight += table.getFootHeight(table.columns);\r\n    }\r\n    return doc.pageSize().height - cursor.y - bottomContentHeight;\r\n}\r\nfunction addPage(doc, table, startPos, cursor, columns, suppressFooter) {\r\n    if (columns === void 0) { columns = []; }\r\n    if (suppressFooter === void 0) { suppressFooter = false; }\r\n    doc.applyStyles(doc.userStyles);\r\n    if (table.settings.showFoot === 'everyPage' && !suppressFooter) {\r\n        table.foot.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\r\n    }\r\n    // Add user content just before adding new page ensure it will\r\n    // be drawn above other things on the page\r\n    table.callEndPageHooks(doc, cursor);\r\n    var margin = table.settings.margin;\r\n    addTableBorder(doc, table, startPos, cursor);\r\n    nextPage(doc);\r\n    table.pageNumber++;\r\n    cursor.x = margin.left;\r\n    cursor.y = margin.top;\r\n    startPos.y = margin.top;\r\n    // call didAddPage hooks before any content is added to the page\r\n    table.callWillDrawPageHooks(doc, cursor);\r\n    if (table.settings.showHead === 'everyPage') {\r\n        table.head.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\r\n        doc.applyStyles(doc.userStyles);\r\n    }\r\n}\r\nfunction nextPage(doc) {\r\n    var current = doc.pageNumber();\r\n    doc.setPage(current + 1);\r\n    var newCurrent = doc.pageNumber();\r\n    if (newCurrent === current) {\r\n        doc.addPage();\r\n        return true;\r\n    }\r\n    return false;\r\n}\r\n\r\nfunction applyPlugin(jsPDF) {\r\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n    jsPDF.API.autoTable = function () {\r\n        var args = [];\r\n        for (var _i = 0; _i < arguments.length; _i++) {\r\n            args[_i] = arguments[_i];\r\n        }\r\n        var options = args[0];\r\n        var input = parseInput(this, options);\r\n        var table = createTable(this, input);\r\n        drawTable(this, table);\r\n        return this;\r\n    };\r\n    // Assign false to enable `doc.lastAutoTable.finalY || 40` sugar\r\n    jsPDF.API.lastAutoTable = false;\r\n    jsPDF.API.autoTableText = function (text, x, y, styles) {\r\n        autoTableText(text, x, y, styles, this);\r\n    };\r\n    jsPDF.API.autoTableSetDefaults = function (defaults) {\r\n        DocHandler.setDefaults(defaults, this);\r\n        return this;\r\n    };\r\n    jsPDF.autoTableSetDefaults = function (defaults, doc) {\r\n        DocHandler.setDefaults(defaults, doc);\r\n    };\r\n    jsPDF.API.autoTableHtmlToJson = function (tableElem, includeHiddenElements) {\r\n        var _a;\r\n        if (includeHiddenElements === void 0) { includeHiddenElements = false; }\r\n        if (typeof window === 'undefined') {\r\n            console.error('Cannot run autoTableHtmlToJson in non browser environment');\r\n            return null;\r\n        }\r\n        var doc = new DocHandler(this);\r\n        var _b = parseHtml(doc, tableElem, window, includeHiddenElements, false), head = _b.head, body = _b.body;\r\n        var columns = ((_a = head[0]) === null || _a === void 0 ? void 0 : _a.map(function (c) { return c.content; })) || [];\r\n        return { columns: columns, rows: body, data: body };\r\n    };\r\n}\r\n\r\nvar _a;\r\nfunction autoTable(d, options) {\r\n    var input = parseInput(d, options);\r\n    var table = createTable(d, input);\r\n    drawTable(d, table);\r\n}\r\n// Experimental export\r\nfunction __createTable(d, options) {\r\n    var input = parseInput(d, options);\r\n    return createTable(d, input);\r\n}\r\nfunction __drawTable(d, table) {\r\n    drawTable(d, table);\r\n}\r\ntry {\r\n    if (typeof window !== 'undefined' && window) {\r\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n        var anyWindow = window;\r\n        var jsPDF = anyWindow.jsPDF || ((_a = anyWindow.jspdf) === null || _a === void 0 ? void 0 : _a.jsPDF);\r\n        if (jsPDF) {\r\n            applyPlugin(jsPDF);\r\n        }\r\n    }\r\n}\r\ncatch (error) {\r\n    console.error('Could not apply autoTable plugin', error);\r\n}\r\n\r\nexport { Cell, CellHookData, Column, HookData, Row, Table, __createTable, __drawTable, applyPlugin, autoTable, autoTable as default };\r\n"], "mappings": ";;;AAIA,SAAS,cAAe,MAAM,GAAG,GAAG,QAAQ,KAAK;AAC7C,WAAS,UAAU,CAAC;AACpB,MAAI,uBAAuB;AAC3B,MAAI,IAAI,IAAI,SAAS;AACrB,MAAI,WAAW,IAAI,SAAS,YAAY,IAAI;AAC5C,MAAI,mBAAmB,IAAI,sBACrB,IAAI,oBAAoB,IACxB;AACN,MAAI,aAAa,WAAW;AAC5B,MAAI,aAAa;AACjB,MAAI,YAAY;AAChB,MAAI,YAAY;AAChB,MAAI,OAAO,WAAW,YAClB,OAAO,WAAW,YAClB,OAAO,WAAW,YAClB,OAAO,WAAW,SAAS;AAC3B,gBAAY,OAAO,SAAS,WAAW,KAAK,MAAM,UAAU,IAAI;AAChE,gBAAY,UAAU,UAAU;AAAA,EACpC;AAEA,OAAK,YAAY,IAAI;AACrB,MAAI,OAAO,WAAW;AAClB,SAAM,YAAY,IAAK;AAAA,WAClB,OAAO,WAAW;AACvB,SAAK,YAAY;AACrB,MAAI,OAAO,WAAW,YAAY,OAAO,WAAW,SAAS;AACzD,QAAI,YAAY;AAChB,QAAI,OAAO,WAAW;AAClB,mBAAa;AACjB,QAAI,aAAa,aAAa,GAAG;AAC7B,eAAS,QAAQ,GAAG,QAAQ,UAAU,QAAQ,SAAS;AACnD,YAAI,KAAK,UAAU,KAAK,GAAG,IAAI,IAAI,mBAAmB,UAAU,KAAK,CAAC,IAAI,WAAW,CAAC;AACtF,aAAK;AAAA,MACT;AACA,aAAO;AAAA,IACX;AACA,SAAK,IAAI,mBAAmB,IAAI,IAAI;AAAA,EACxC;AACA,MAAI,OAAO,WAAW,WAAW;AAC7B,QAAI,KAAK,MAAM,GAAG,GAAG,EAAE,UAAU,OAAO,YAAY,KAAK,OAAO,UAAU,CAAC;AAAA,EAC/E,OACK;AACD,QAAI,KAAK,MAAM,GAAG,CAAC;AAAA,EACvB;AACA,SAAO;AACX;AAEA,IAAI,iBAAiB,CAAC;AACtB,IAAI;AAAA;AAAA,EAA4B,WAAY;AACxC,aAASA,YAAW,eAAe;AAC/B,WAAK,gBAAgB;AACrB,WAAK,aAAa;AAAA;AAAA,QAEd,WAAW,cAAc,eACnB,KAAK,cAAc,aAAa,IAChC;AAAA,QACN,UAAU,cAAc,SAAS,YAAY;AAAA,QAC7C,WAAW,cAAc,SAAS,QAAQ,EAAE;AAAA,QAC5C,MAAM,cAAc,SAAS,QAAQ,EAAE;AAAA;AAAA,QAEvC,WAAW,cAAc,eACnB,KAAK,cAAc,aAAa,IAChC;AAAA;AAAA,QAEN,WAAW,cAAc,eACnB,KAAK,cAAc,aAAa,IAChC;AAAA,MACV;AAAA,IACJ;AACA,IAAAA,YAAW,cAAc,SAAU,UAAU,KAAK;AAC9C,UAAI,QAAQ,QAAQ;AAAE,cAAM;AAAA,MAAM;AAClC,UAAI,KAAK;AACL,YAAI,8BAA8B;AAAA,MACtC,OACK;AACD,yBAAiB;AAAA,MACrB;AAAA,IACJ;AACA,IAAAA,YAAW,aAAa,SAAU,GAAG;AACjC,UAAI,MAAM,QAAQ,CAAC,GAAG;AAClB,eAAO;AAAA,MACX,WACS,OAAO,MAAM,UAAU;AAC5B,eAAO,CAAC,GAAG,GAAG,CAAC;AAAA,MACnB,WACS,OAAO,MAAM,UAAU;AAC5B,eAAO,CAAC,CAAC;AAAA,MACb,OACK;AACD,eAAO;AAAA,MACX;AAAA,IACJ;AACA,IAAAA,YAAW,UAAU,cAAc,SAAU,QAAQ,UAAU;AAG3D,UAAIC,KAAI,IAAI;AACZ,UAAI,aAAa,QAAQ;AAAE,mBAAW;AAAA,MAAO;AAC7C,UAAI,OAAO,aAAa,KAAK,cAAc,cAAc;AACrD,aAAK,cAAc,aAAa,OAAO,SAAS;AAAA,MACpD;AACA,UAAI,KAAK,KAAK,cAAc,SAAS,QAAQ,GAAG,YAAY,GAAG,WAAW,WAAW,GAAG;AACxF,UAAI,OAAO;AACP,mBAAW,OAAO;AACtB,UAAI,OAAO,WAAW;AAClB,oBAAY,OAAO;AACnB,YAAI,sBAAsB,KAAK,YAAY,EAAE,QAAQ;AACrD,YAAI,uBACA,oBAAoB,QAAQ,SAAS,MAAM,MAC3C,KAAK,cAAc,cAAc;AAIjC,eAAK,cAAc,aAAa,oBAAoB,CAAC,CAAC;AACtD,sBAAY,oBAAoB,CAAC;AAAA,QACrC;AAAA,MACJ;AACA,WAAK,cAAc,QAAQ,UAAU,SAAS;AAC9C,UAAI,OAAO;AACP,aAAK,cAAc,YAAY,OAAO,QAAQ;AAClD,UAAI,UAAU;AACV;AAAA,MACJ;AACA,UAAI,QAAQD,YAAW,WAAW,OAAO,SAAS;AAClD,UAAI;AACA,SAACC,MAAK,KAAK,eAAe,aAAa,MAAMA,KAAI,KAAK;AAC1D,cAAQD,YAAW,WAAW,OAAO,SAAS;AAC9C,UAAI;AACA,SAAC,KAAK,KAAK,eAAe,aAAa,MAAM,IAAI,KAAK;AAC1D,cAAQA,YAAW,WAAW,OAAO,SAAS;AAC9C,UAAI;AACA,SAAC,KAAK,KAAK,eAAe,aAAa,MAAM,IAAI,KAAK;AAC1D,UAAI,OAAO,OAAO,cAAc,UAAU;AACtC,aAAK,cAAc,aAAa,OAAO,SAAS;AAAA,MACpD;AAAA,IACJ;AACA,IAAAA,YAAW,UAAU,kBAAkB,SAAU,MAAM,MAAM,MAAM;AAC/D,aAAO,KAAK,cAAc,gBAAgB,MAAM,MAAM,IAAI;AAAA,IAC9D;AASA,IAAAA,YAAW,UAAU,OAAO,SAAU,GAAG,GAAG,OAAO,QAAQ,WAAW;AAIlE,aAAO,KAAK,cAAc,KAAK,GAAG,GAAG,OAAO,QAAQ,SAAS;AAAA,IACjE;AACA,IAAAA,YAAW,UAAU,mBAAmB,WAAY;AAChD,aAAO,KAAK,cAAc,iBAAiB;AAAA,IAC/C;AACA,IAAAA,YAAW,UAAU,eAAe,SAAU,MAAM;AAChD,aAAO,KAAK,cAAc,aAAa,IAAI;AAAA,IAC/C;AACA,IAAAA,YAAW,UAAU,cAAc,WAAY;AAC3C,aAAO,KAAK;AAAA,IAChB;AACA,IAAAA,YAAW,UAAU,UAAU,SAAU,MAAM;AAC3C,WAAK,cAAc,QAAQ,IAAI;AAAA,IACnC;AACA,IAAAA,YAAW,UAAU,UAAU,WAAY;AACvC,aAAO,KAAK,cAAc,QAAQ;AAAA,IACtC;AACA,IAAAA,YAAW,UAAU,cAAc,WAAY;AAC3C,aAAO,KAAK,cAAc,YAAY;AAAA,IAC1C;AACA,IAAAA,YAAW,UAAU,mBAAmB,WAAY;AAChD,aAAO,kBAAkB,CAAC;AAAA,IAC9B;AACA,IAAAA,YAAW,UAAU,qBAAqB,WAAY;AAClD,aAAO,KAAK,cAAc,+BAA+B,CAAC;AAAA,IAC9D;AACA,IAAAA,YAAW,UAAU,WAAW,WAAY;AACxC,UAAI,WAAW,KAAK,cAAc,SAAS;AAE3C,UAAI,SAAS,SAAS,MAAM;AACxB,mBAAW,EAAE,OAAO,SAAS,SAAS,GAAG,QAAQ,SAAS,UAAU,EAAE;AAAA,MAC1E;AACA,aAAO;AAAA,IACX;AACA,IAAAA,YAAW,UAAU,cAAc,WAAY;AAC3C,aAAO,KAAK,cAAc,SAAS;AAAA,IACvC;AACA,IAAAA,YAAW,UAAU,sBAAsB,WAAY;AACnD,UAAI,MAAM,KAAK;AACf,aAAO,IAAI,sBAAsB,IAAI,oBAAoB,IAAI;AAAA,IACjE;AACA,IAAAA,YAAW,UAAU,gBAAgB,SAAU,UAAU;AACrD,aAAQ,WAAW,KAAK,YAAY,IAAK,KAAK,oBAAoB;AAAA,IACtE;AACA,IAAAA,YAAW,UAAU,aAAa,WAAY;AAC1C,UAAI,WAAW,KAAK,cAAc,SAAS,mBAAmB;AAC9D,UAAI,CAAC,UAAU;AAEX,eAAO,KAAK,cAAc,SAAS,iBAAiB;AAAA,MACxD;AACA,aAAO,SAAS;AAAA,IACpB;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAkBF,IAAI,gBAAgB,SAAS,GAAG,GAAG;AAC/B,kBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUE,IAAGC,IAAG;AAAE,IAAAD,GAAE,YAAYC;AAAA,EAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,aAAS,KAAKA,GAAG,KAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC,EAAG,CAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,EAAG;AACpG,SAAO,cAAc,GAAG,CAAC;AAC7B;AAEA,SAAS,UAAU,GAAG,GAAG;AACrB,MAAI,OAAO,MAAM,cAAc,MAAM;AACjC,UAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AAC5F,gBAAc,GAAG,CAAC;AAClB,WAAS,KAAK;AAAE,SAAK,cAAc;AAAA,EAAG;AACtC,IAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AACtF;AAOA,IAAI;AAAA;AAAA,EAA8B,SAAU,QAAQ;AAChD,cAAUC,eAAc,MAAM;AAC9B,aAASA,cAAa,SAAS;AAC3B,UAAI,QAAQ,OAAO,KAAK,IAAI,KAAK;AACjC,YAAM,WAAW;AACjB,aAAO;AAAA,IACX;AACA,WAAOA;AAAA,EACX,EAAE,KAAK;AAAA;AAEP,SAAS,cAAc,aAAa;AAChC,SAAO;AAAA,IACH,MAAM;AAAA;AAAA,IACN,WAAW;AAAA;AAAA,IACX,UAAU;AAAA;AAAA,IACV,WAAW;AAAA;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,UAAU;AAAA,IACV,aAAa,IAAI;AAAA;AAAA,IACjB,WAAW;AAAA,IACX,WAAW;AAAA,IACX,WAAW;AAAA;AAAA,IACX,eAAe;AAAA,IACf,cAAc;AAAA,EAClB;AACJ;AACA,SAAS,SAAS,MAAM;AACpB,MAAI,SAAS;AAAA,IACT,SAAS;AAAA,MACL,OAAO,EAAE,WAAW,KAAK,WAAW,IAAI,WAAW,SAAS;AAAA,MAC5D,MAAM,EAAE,WAAW,KAAK,WAAW,CAAC,IAAI,KAAK,GAAG,GAAG,WAAW,OAAO;AAAA,MACrE,MAAM,CAAC;AAAA,MACP,MAAM,EAAE,WAAW,KAAK,WAAW,CAAC,IAAI,KAAK,GAAG,GAAG,WAAW,OAAO;AAAA,MACrE,cAAc,EAAE,WAAW,IAAI;AAAA,IACnC;AAAA,IACA,MAAM;AAAA,MACF,OAAO;AAAA,QACH,WAAW;AAAA,QACX,WAAW;AAAA,QACX,WAAW;AAAA,QACX,WAAW;AAAA,MACf;AAAA,MACA,MAAM;AAAA,QACF,WAAW;AAAA,QACX,WAAW,CAAC,IAAI,KAAK,GAAG;AAAA,QACxB,WAAW;AAAA,QACX,WAAW;AAAA,MACf;AAAA,MACA,MAAM,CAAC;AAAA,MACP,MAAM;AAAA,QACF,WAAW;AAAA,QACX,WAAW,CAAC,IAAI,KAAK,GAAG;AAAA,QACxB,WAAW;AAAA,QACX,WAAW;AAAA,MACf;AAAA,MACA,cAAc,CAAC;AAAA,IACnB;AAAA,IACA,OAAO,EAAE,MAAM,EAAE,WAAW,OAAO,GAAG,MAAM,EAAE,WAAW,OAAO,EAAE;AAAA,EACtE;AACA,SAAO,OAAO,IAAI;AACtB;AAEA,SAAS,eAAe,MAAM,QAAQ,KAAK;AACvC,MAAI,YAAY,QAAQ,IAAI;AAC5B,MAAI,UAAU,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAC,IAAI;AAChD,MAAI,kBAAkB,QACjB,IAAI,SAAUC,OAAM;AAAE,WAAO,IAAI,aAAaA,KAAI;AAAA,EAAG,CAAC,EACtD,OAAO,SAAU,GAAG,GAAG;AAAE,WAAO,KAAK,IAAI,GAAG,CAAC;AAAA,EAAG,GAAG,CAAC;AACzD,SAAO;AACX;AACA,SAAS,eAAe,KAAK,OAAO,UAAU,QAAQ;AAClD,MAAI,YAAY,MAAM,SAAS;AAC/B,MAAI,YAAY,MAAM,SAAS;AAC/B,MAAI,YAAY,EAAE,WAAsB,UAAqB,CAAC;AAC9D,MAAI,YAAY,aAAa,WAAW,KAAK;AAC7C,MAAI,WAAW;AACX,QAAI,KAAK,SAAS,GAAG,SAAS,GAAG,MAAM,SAAS,IAAI,SAAS,EAAE,KAAK,GAAG,OAAO,IAAI,SAAS,GAAG,SAAS;AAAA,EAC3G;AACJ;AACA,SAAS,aAAa,WAAW,WAAW;AACxC,MAAI,WAAW,YAAY;AAC3B,MAAI,iBAAiB,aAAa,cAAc;AAChD,MAAI,YAAY,gBAAgB;AAC5B,WAAO;AAAA,EACX,WACS,UAAU;AACf,WAAO;AAAA,EACX,WACS,gBAAgB;AACrB,WAAO;AAAA,EACX,OACK;AACD,WAAO;AAAA,EACX;AACJ;AACA,SAAS,aAAa,OAAO,cAAc;AACvC,MAAIC,KAAI,IAAI,IAAI;AAChB,UAAQ,SAAS;AACjB,MAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,QAAI,MAAM,UAAU,GAAG;AACnB,aAAO;AAAA,QACH,KAAK,MAAM,CAAC;AAAA,QACZ,OAAO,MAAM,CAAC;AAAA,QACd,QAAQ,MAAM,CAAC;AAAA,QACf,MAAM,MAAM,CAAC;AAAA,MACjB;AAAA,IACJ,WACS,MAAM,WAAW,GAAG;AACzB,aAAO;AAAA,QACH,KAAK,MAAM,CAAC;AAAA,QACZ,OAAO,MAAM,CAAC;AAAA,QACd,QAAQ,MAAM,CAAC;AAAA,QACf,MAAM,MAAM,CAAC;AAAA,MACjB;AAAA,IACJ,WACS,MAAM,WAAW,GAAG;AACzB,aAAO;AAAA,QACH,KAAK,MAAM,CAAC;AAAA,QACZ,OAAO,MAAM,CAAC;AAAA,QACd,QAAQ,MAAM,CAAC;AAAA,QACf,MAAM,MAAM,CAAC;AAAA,MACjB;AAAA,IACJ,WACS,MAAM,WAAW,GAAG;AACzB,cAAQ,MAAM,CAAC;AAAA,IACnB,OACK;AACD,cAAQ;AAAA,IACZ;AAAA,EACJ;AACA,MAAI,OAAO,UAAU,UAAU;AAC3B,QAAI,OAAO,MAAM,aAAa,UAAU;AACpC,YAAM,MAAM,MAAM;AAClB,YAAM,SAAS,MAAM;AAAA,IACzB;AACA,QAAI,OAAO,MAAM,eAAe,UAAU;AACtC,YAAM,QAAQ,MAAM;AACpB,YAAM,OAAO,MAAM;AAAA,IACvB;AACA,WAAO;AAAA,MACH,OAAOA,MAAK,MAAM,UAAU,QAAQA,QAAO,SAASA,MAAK;AAAA,MACzD,MAAM,KAAK,MAAM,SAAS,QAAQ,OAAO,SAAS,KAAK;AAAA,MACvD,QAAQ,KAAK,MAAM,WAAW,QAAQ,OAAO,SAAS,KAAK;AAAA,MAC3D,SAAS,KAAK,MAAM,YAAY,QAAQ,OAAO,SAAS,KAAK;AAAA,IACjE;AAAA,EACJ;AACA,MAAI,OAAO,UAAU,UAAU;AAC3B,YAAQ;AAAA,EACZ;AACA,SAAO,EAAE,KAAK,OAAO,OAAO,OAAO,QAAQ,OAAO,MAAM,MAAM;AAClE;AACA,SAAS,sBAAsB,KAAK,OAAO;AACvC,MAAI,UAAU,aAAa,MAAM,SAAS,QAAQ,CAAC;AACnD,SAAO,IAAI,SAAS,EAAE,SAAS,QAAQ,OAAO,QAAQ;AAC1D;AAKA,SAAS,SAAS,gBAAgB,SAAS,aAAa,OAAOC,SAAQ;AACnE,MAAI,SAAS,CAAC;AACd,MAAI,gBAAgB,KAAK;AACzB,MAAI,kBAAkB,WAAW,SAAS,SAAU,MAAM;AACtD,WAAOA,QAAO,iBAAiB,IAAI,EAAE,iBAAiB;AAAA,EAC1D,CAAC;AACD,MAAI,mBAAmB;AACnB,WAAO,YAAY;AACvB,MAAI,YAAY,WAAW,SAAS,SAAU,MAAM;AAChD,WAAOA,QAAO,iBAAiB,IAAI,EAAE,OAAO;AAAA,EAChD,CAAC;AACD,MAAI,aAAa;AACb,WAAO,YAAY;AACvB,MAAI,UAAU,aAAa,OAAO,WAAW;AAC7C,MAAI;AACA,WAAO,cAAc;AACzB,MAAI,kBAAkB;AACtB,MAAI,mBAAmB,gBAAgB;AACvC,MAAI,MAAM,MAAM;AAChB,MAAI,MAAM,sBAAsB,OAC5B,MAAM,qBAAqB,OAC3B,MAAM,oBAAoB,KAAK;AAC/B,QAAI,eAAe,WAAW,GAAG,KAAK,KAAK;AAC3C,QAAI;AACA,aAAO,YAAY;AAAA,EAC3B,OACK;AACD,WAAO,YAAY;AAAA,MACf,MAAM,WAAW,MAAM,cAAc,KAAK,KAAK;AAAA,MAC/C,QAAQ,WAAW,MAAM,gBAAgB,KAAK,KAAK;AAAA,MACnD,SAAS,WAAW,MAAM,iBAAiB,KAAK,KAAK;AAAA,MACrD,OAAO,WAAW,MAAM,eAAe,KAAK,KAAK;AAAA,IACrD;AAGA,QAAI,CAAC,OAAO,UAAU,KAAK;AACvB,UAAI,OAAO,UAAU,OAAO;AACxB,0BAAkB;AAAA,MACtB,WACS,OAAO,UAAU,QAAQ;AAC9B,0BAAkB;AAAA,MACtB,WACS,OAAO,UAAU,MAAM;AAC5B,0BAAkB;AAAA,MACtB;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,cAAc,WAAW,SAAS,SAAU,MAAM;AAClD,WAAOA,QAAO,iBAAiB,IAAI,EAAE,eAAe;AAAA,EACxD,CAAC;AACD,MAAI,eAAe;AACf,WAAO,YAAY;AACvB,MAAI,WAAW,CAAC,QAAQ,SAAS,UAAU,SAAS;AACpD,MAAI,SAAS,QAAQ,MAAM,SAAS,MAAM,IAAI;AAC1C,WAAO,SAAS,MAAM;AAAA,EAC1B;AACA,aAAW,CAAC,UAAU,UAAU,KAAK;AACrC,MAAI,SAAS,QAAQ,MAAM,aAAa,MAAM,IAAI;AAC9C,WAAO,SAAS,MAAM;AAAA,EAC1B;AACA,MAAI,MAAM,SAAS,MAAM,YAAY,EAAE;AACvC,MAAI,CAAC,MAAM,GAAG;AACV,WAAO,WAAW,MAAM;AAC5B,MAAI,YAAY,eAAe,KAAK;AACpC,MAAI;AACA,WAAO,YAAY;AACvB,MAAI,QAAQ,MAAM,cAAc,IAAI,YAAY;AAChD,MAAI,eAAe,QAAQ,IAAI,MAAM,IAAI;AACrC,WAAO,OAAO;AAAA,EAClB;AACA,SAAO;AACX;AACA,SAAS,eAAe,OAAO;AAC3B,MAAI,MAAM;AACV,MAAI,MAAM,eAAe,UACrB,MAAM,eAAe,YACrB,SAAS,MAAM,UAAU,KAAK,KAAK;AACnC,UAAM;AAAA,EACV;AACA,MAAI,MAAM,cAAc,YAAY,MAAM,cAAc,WAAW;AAC/D,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,WAAW,SAAS,aAAa;AACtC,MAAI,WAAW,UAAU,SAAS,WAAW;AAC7C,MAAI,CAAC;AACD,WAAO;AACX,MAAI,OAAO,SAAS,MAAM,wDAAwD;AAClF,MAAI,CAAC,QAAQ,CAAC,MAAM,QAAQ,IAAI,GAAG;AAC/B,WAAO;AAAA,EACX;AACA,MAAI,QAAQ;AAAA,IACR,SAAS,KAAK,CAAC,CAAC;AAAA,IAChB,SAAS,KAAK,CAAC,CAAC;AAAA,IAChB,SAAS,KAAK,CAAC,CAAC;AAAA,EACpB;AACA,MAAI,QAAQ,SAAS,KAAK,CAAC,CAAC;AAC5B,MAAI,UAAU,KAAK,MAAM,MAAM,CAAC,CAAC,KAAK,MAAM,MAAM,CAAC,CAAC,KAAK,MAAM,MAAM,CAAC,CAAC,GAAG;AACtE,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,UAAU,MAAM,aAAa;AAClC,MAAI,KAAK,YAAY,IAAI;AACzB,MAAI,OAAO,sBACP,OAAO,iBACP,OAAO,aACP,OAAO,WAAW;AAClB,QAAI,KAAK,iBAAiB,MAAM;AAC5B,aAAO;AAAA,IACX;AACA,WAAO,UAAU,KAAK,eAAe,WAAW;AAAA,EACpD,OACK;AACD,WAAO;AAAA,EACX;AACJ;AACA,SAAS,aAAa,OAAO,aAAa;AACtC,MAAI,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,EACV;AACA,MAAI,gBAAgB,MAAM,KAAK;AAC/B,MAAI,eAAe,SAAS,MAAM,UAAU,IAAI,SAAS,MAAM,QAAQ,KAAK,cAAc;AAC1F,MAAI,eAAe,IAAI,IAAI,SAAU,GAAG;AACpC,WAAO,SAAS,KAAK,GAAG,IAAI;AAAA,EAChC,CAAC;AACD,MAAI,UAAU,aAAa,cAAc,CAAC;AAC1C,MAAI,cAAc,QAAQ,KAAK;AAC3B,YAAQ,MAAM;AAAA,EAClB;AACA,MAAI,cAAc,QAAQ,QAAQ;AAC9B,YAAQ,SAAS;AAAA,EACrB;AACA,SAAO;AACX;AAEA,SAAS,UAAU,KAAK,OAAOA,SAAQ,mBAAmB,QAAQ;AAC9D,MAAID,KAAI;AACR,MAAI,sBAAsB,QAAQ;AAAE,wBAAoB;AAAA,EAAO;AAC/D,MAAI,WAAW,QAAQ;AAAE,aAAS;AAAA,EAAO;AACzC,MAAI;AACJ,MAAI,OAAO,UAAU,UAAU;AAC3B,mBAAeC,QAAO,SAAS,cAAc,KAAK;AAAA,EACtD,OACK;AACD,mBAAe;AAAA,EACnB;AACA,MAAI,iBAAiB,OAAO,KAAK,IAAI,YAAY,CAAC;AAClD,MAAI,cAAc,IAAI,YAAY;AAClC,MAAI,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC;AAClC,MAAI,CAAC,cAAc;AACf,YAAQ,MAAM,8CAA8C,KAAK;AACjE,WAAO,EAAE,MAAY,MAAY,KAAW;AAAA,EAChD;AACA,WAAS,IAAI,GAAG,IAAI,aAAa,KAAK,QAAQ,KAAK;AAC/C,QAAI,UAAU,aAAa,KAAK,CAAC;AACjC,QAAI,WAAW,MAAMD,MAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,mBAAmB,QAAQA,QAAO,SAAS,SAASA,IAAG,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY;AACxM,QAAI,MAAM,gBAAgB,gBAAgB,aAAaC,SAAQ,SAAS,mBAAmB,MAAM;AACjG,QAAI,CAAC;AACD;AACJ,QAAI,YAAY,SAAS;AACrB,WAAK,KAAK,GAAG;AAAA,IACjB,WACS,YAAY,SAAS;AAC1B,WAAK,KAAK,GAAG;AAAA,IACjB,OACK;AAED,WAAK,KAAK,GAAG;AAAA,IACjB;AAAA,EACJ;AACA,SAAO,EAAE,MAAY,MAAY,KAAW;AAChD;AACA,SAAS,gBAAgB,gBAAgB,aAAaA,SAAQ,KAAK,eAAe,QAAQ;AACtF,MAAI,YAAY,IAAI,aAAa,GAAG;AACpC,WAAS,IAAI,GAAG,IAAI,IAAI,MAAM,QAAQ,KAAK;AACvC,QAAI,OAAO,IAAI,MAAM,CAAC;AACtB,QAAI,UAAUA,QAAO,iBAAiB,IAAI;AAC1C,QAAI,iBAAiB,QAAQ,YAAY,QAAQ;AAC7C,UAAIC,cAAa;AACjB,UAAI,QAAQ;AACR,QAAAA,cAAa,SAAS,gBAAgB,MAAM,aAAa,SAASD,OAAM;AAAA,MAC5E;AACA,gBAAU,KAAK;AAAA,QACX,SAAS,KAAK;AAAA,QACd,SAAS,KAAK;AAAA,QACd,QAAQC;AAAA,QACR,UAAU;AAAA,QACV,SAAS,iBAAiB,IAAI;AAAA,MAClC,CAAC;AAAA,IACL;AAAA,EACJ;AACA,MAAI,QAAQD,QAAO,iBAAiB,GAAG;AACvC,MAAI,UAAU,SAAS,MAAM,iBAAiB,MAAM,YAAY,SAAS;AACrE,WAAO;AAAA,EACX;AACJ;AACA,SAAS,iBAAiB,SAAS;AAE/B,MAAI,OAAO,QAAQ,UAAU,IAAI;AAGjC,OAAK,YAAY,KAAK,UAAU,QAAQ,OAAO,EAAE,EAAE,QAAQ,OAAO,GAAG;AAErE,OAAK,YAAY,KAAK,UACjB,MAAM,SAAS,EACf,IAAI,SAAU,MAAM;AAAE,WAAO,KAAK,KAAK;AAAA,EAAG,CAAC,EAC3C,KAAK,IAAI;AAEd,SAAO,KAAK,aAAa,KAAK,eAAe;AACjD;AAEA,SAAS,cAAc,QAAQ,UAAU,SAAS;AAC9C,WAAS,KAAK,GAAGD,MAAK,CAAC,QAAQ,UAAU,OAAO,GAAG,KAAKA,IAAG,QAAQ,MAAM;AACrE,QAAI,UAAUA,IAAG,EAAE;AACnB,QAAI,WAAW,OAAO,YAAY,UAAU;AACxC,cAAQ,MAAM,yDAAyD,OAAO,OAAO;AAAA,IACzF;AACA,QAAI,QAAQ,UAAU,OAAO,QAAQ,WAAW,UAAU;AACtD,cAAQ,MAAM,mCAAmC,QAAQ,MAAM;AAC/D,aAAO,QAAQ;AAAA,IACnB;AAAA,EACJ;AACJ;AAIA,SAAS,OAAO,QAAQ,GAAG,IAAI,IAAI,IAAI;AACnC,MAAI,UAAU,MAAM;AAChB,UAAM,IAAI,UAAU,4CAA4C;AAAA,EACpE;AACA,MAAI,KAAK,OAAO,MAAM;AACtB,WAAS,QAAQ,GAAG,QAAQ,UAAU,QAAQ,SAAS;AAEnD,QAAI,aAAa,UAAU,KAAK;AAChC,QAAI,cAAc,MAAM;AAEpB,eAAS,WAAW,YAAY;AAE5B,YAAI,OAAO,UAAU,eAAe,KAAK,YAAY,OAAO,GAAG;AAC3D,aAAG,OAAO,IAAI,WAAW,OAAO;AAAA,QACpC;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,WAAW,GAAG,SAAS;AAC5B,MAAI,MAAM,IAAI,WAAW,CAAC;AAC1B,MAAI,WAAW,IAAI,mBAAmB;AACtC,MAAI,SAAS,IAAI,iBAAiB;AAClC,gBAAc,QAAQ,UAAU,OAAO;AACvC,MAAI,UAAU,OAAO,CAAC,GAAG,QAAQ,UAAU,OAAO;AAClD,MAAI;AACJ,MAAI,OAAO,WAAW,aAAa;AAC/B,UAAM;AAAA,EACV;AACA,MAAI,SAAS,YAAY,QAAQ,UAAU,OAAO;AAClD,MAAI,QAAQ,WAAW,QAAQ,UAAU,OAAO;AAChD,MAAI,WAAW,cAAc,KAAK,OAAO;AACzC,MAAI,UAAU,eAAe,KAAK,SAAS,GAAG;AAC9C,SAAO,EAAE,IAAI,QAAQ,SAAS,SAAkB,OAAc,QAAgB,SAAmB;AACrG;AACA,SAAS,YAAY,QAAQ,QAAQ,QAAQ;AACzC,MAAI,eAAe;AAAA,IACf,QAAQ,CAAC;AAAA,IACT,YAAY,CAAC;AAAA,IACb,YAAY,CAAC;AAAA,IACb,YAAY,CAAC;AAAA,IACb,oBAAoB,CAAC;AAAA,IACrB,cAAc,CAAC;AAAA,EACnB;AACA,MAAI,UAAU,SAAUG,OAAM;AAC1B,QAAIA,UAAS,gBAAgB;AACzB,UAAI,WAAW,OAAOA,KAAI;AAC1B,UAAI,aAAa,OAAOA,KAAI;AAC5B,UAAI,UAAU,OAAOA,KAAI;AACzB,mBAAa,eAAe,OAAO,CAAC,GAAG,UAAU,YAAY,OAAO;AAAA,IACxE,OACK;AACD,UAAI,aAAa,CAAC,QAAQ,QAAQ,MAAM;AACxC,UAAI,SAAS,WAAW,IAAI,SAAU,MAAM;AAAE,eAAO,KAAKA,KAAI,KAAK,CAAC;AAAA,MAAG,CAAC;AACxE,mBAAaA,KAAI,IAAI,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,IACnE;AAAA,EACJ;AACA,WAAS,KAAK,GAAGH,MAAK,OAAO,KAAK,YAAY,GAAG,KAAKA,IAAG,QAAQ,MAAM;AACnE,QAAI,OAAOA,IAAG,EAAE;AAChB,YAAQ,IAAI;AAAA,EAChB;AACA,SAAO;AACX;AACA,SAAS,WAAW,QAAQ,UAAU,SAAS;AAC3C,MAAI,aAAa,CAAC,QAAQ,UAAU,OAAO;AAC3C,MAAI,SAAS;AAAA,IACT,cAAc,CAAC;AAAA,IACf,cAAc,CAAC;AAAA,IACf,aAAa,CAAC;AAAA,IACd,cAAc,CAAC;AAAA,IACf,aAAa,CAAC;AAAA,EAClB;AACA,WAAS,KAAK,GAAG,eAAe,YAAY,KAAK,aAAa,QAAQ,MAAM;AACxE,QAAI,UAAU,aAAa,EAAE;AAC7B,QAAI,QAAQ;AACR,aAAO,aAAa,KAAK,QAAQ,YAAY;AACjD,QAAI,QAAQ;AACR,aAAO,aAAa,KAAK,QAAQ,YAAY;AACjD,QAAI,QAAQ;AACR,aAAO,YAAY,KAAK,QAAQ,WAAW;AAC/C,QAAI,QAAQ;AACR,aAAO,aAAa,KAAK,QAAQ,YAAY;AACjD,QAAI,QAAQ;AACR,aAAO,YAAY,KAAK,QAAQ,WAAW;AAAA,EACnD;AACA,SAAO;AACX;AACA,SAAS,cAAc,KAAK,SAAS;AACjC,MAAIA,KAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAChD,MAAI,SAAS,aAAa,QAAQ,QAAQ,KAAK,IAAI,YAAY,CAAC;AAChE,MAAI,UAAUA,MAAK,UAAU,KAAK,QAAQ,MAAM,OAAO,QAAQA,QAAO,SAASA,MAAK,OAAO;AAC3F,MAAI;AACJ,MAAI,QAAQ,aAAa,MAAM;AAC3B,eAAW;AAAA,EACf,WACS,QAAQ,aAAa,OAAO;AACjC,eAAW;AAAA,EACf,OACK;AACD,gBAAY,KAAK,QAAQ,cAAc,QAAQ,OAAO,SAAS,KAAK;AAAA,EACxE;AACA,MAAI;AACJ,MAAI,QAAQ,aAAa,MAAM;AAC3B,eAAW;AAAA,EACf,WACS,QAAQ,aAAa,OAAO;AACjC,eAAW;AAAA,EACf,OACK;AACD,gBAAY,KAAK,QAAQ,cAAc,QAAQ,OAAO,SAAS,KAAK;AAAA,EACxE;AACA,MAAI,UAAU,KAAK,QAAQ,YAAY,QAAQ,OAAO,SAAS,KAAK;AACpE,MAAI,QAAQ,QAAQ,UAAU,SAAS,UAAU;AACjD,MAAI,sBAAsB,CAAC,CAAC,QAAQ;AACpC,MAAI,6BAA6B,KAAK,QAAQ,+BAA+B,QAAQ,OAAO,SAAS,KAAK;AAC1G,SAAO;AAAA,IACH,oBAAoB,KAAK,QAAQ,uBAAuB,QAAQ,OAAO,SAAS,KAAK;AAAA,IACrF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAY,KAAK,QAAQ,eAAe,QAAQ,OAAO,SAAS,KAAK;AAAA,IACrE,eAAe,KAAK,QAAQ,kBAAkB,QAAQ,OAAO,SAAS,KAAK;AAAA,IAC3E,aAAa,KAAK,QAAQ,gBAAgB,QAAQ,OAAO,SAAS,KAAK;AAAA,IACvE;AAAA,IACA;AAAA,IACA,iBAAiB,KAAK,QAAQ,oBAAoB,QAAQ,OAAO,SAAS,KAAK;AAAA,IAC/E,iBAAiB,KAAK,QAAQ,oBAAoB,QAAQ,OAAO,SAAS,KAAK;AAAA,IAC/E;AAAA,IACA;AAAA,IACA,+BAA+B,KAAK,QAAQ,kCAAkC,QAAQ,OAAO,SAAS,KAAK;AAAA,EAC/G;AACJ;AACA,SAAS,UAAU,KAAK,YAAY;AAChC,MAAI,WAAW,IAAI,iBAAiB;AACpC,MAAI,KAAK,IAAI,YAAY;AACzB,MAAI,cAAc,IAAI,WAAW;AACjC,MAAI,4BAA4B;AAChC,MAAI,YAAY,SAAS,iBAAiB;AACtC,QAAI,aAAa,SAAS,kBAAkB,SAAS,aAAa;AAClE,gCAA4B,eAAe;AAAA,EAC/C;AACA,MAAI,OAAO,eAAe,UAAU;AAChC,WAAO;AAAA,EACX,WACS,cAAc,QAAQ,eAAe,OAAO;AACjD,QAAI,8BAA8B,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,WAAW,MAAM;AAG5G,aAAO,SAAS,SAAS,KAAK;AAAA,IAClC;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,eAAe,KAAK,SAASC,SAAQ;AAC1C,MAAI,OAAO,QAAQ,QAAQ,CAAC;AAC5B,MAAI,OAAO,QAAQ,QAAQ,CAAC;AAC5B,MAAI,OAAO,QAAQ,QAAQ,CAAC;AAC5B,MAAI,QAAQ,MAAM;AACd,QAAI,SAAS,QAAQ;AACrB,QAAIA,SAAQ;AACR,UAAI,cAAc,UAAU,KAAK,QAAQ,MAAMA,SAAQ,QAAQ,QAAQ,MAAM,KAAK,CAAC;AACnF,aAAO,YAAY,QAAQ;AAC3B,aAAO,YAAY,QAAQ;AAC3B,aAAO,YAAY,QAAQ;AAAA,IAC/B,OACK;AACD,cAAQ,MAAM,8CAA8C;AAAA,IAChE;AAAA,EACJ;AACA,MAAI,UAAU,QAAQ,WAAW,aAAa,MAAM,MAAM,IAAI;AAC9D,SAAO,EAAE,SAAkB,MAAY,MAAY,KAAW;AAClE;AACA,SAAS,aAAa,MAAM,MAAM,MAAM;AACpC,MAAI,WAAW,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,CAAC;AACjD,MAAI,SAAS,CAAC;AACd,SAAO,KAAK,QAAQ,EACf,OAAO,SAAU,KAAK;AAAE,WAAO,QAAQ;AAAA,EAAY,CAAC,EACpD,QAAQ,SAAU,KAAK;AACxB,QAAI,UAAU;AACd,QAAI;AACJ,QAAI,MAAM,QAAQ,QAAQ,GAAG;AACzB,cAAQ,SAAS,SAAS,GAAG,CAAC;AAAA,IAClC,OACK;AACD,cAAQ,SAAS,GAAG;AAAA,IACxB;AACA,QAAI,OAAO,UAAU,YAAY,CAAC,MAAM,QAAQ,KAAK,GAAG;AACpD,iBAAW,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,YAAY;AAAA,IAC/E;AACA,aAAS,IAAI,GAAG,IAAI,SAAS,KAAK;AAC9B,UAAI,KAAK;AACT,UAAI,MAAM,QAAQ,QAAQ,GAAG;AACzB,aAAK,OAAO;AAAA,MAChB,OACK;AACD,aAAK,OAAO,IAAI,IAAI,IAAI,OAAO,CAAC,IAAI;AAAA,MACxC;AACA,UAAI,YAAY,EAAE,SAAS,GAAG;AAC9B,aAAO,KAAK,SAAS;AAAA,IACzB;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AAEA,IAAI;AAAA;AAAA,EAA0B,2BAAY;AACtC,aAASG,UAAS,KAAK,OAAO,QAAQ;AAClC,WAAK,QAAQ;AACb,WAAK,aAAa,MAAM;AACxB,WAAK,WAAW,MAAM;AACtB,WAAK,SAAS;AACd,WAAK,MAAM,IAAI,YAAY;AAAA,IAC/B;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AACF,IAAI;AAAA;AAAA,EAA8B,SAAU,QAAQ;AAChD,cAAUC,eAAc,MAAM;AAC9B,aAASA,cAAa,KAAK,OAAO,MAAM,KAAK,QAAQ,QAAQ;AACzD,UAAI,QAAQ,OAAO,KAAK,MAAM,KAAK,OAAO,MAAM,KAAK;AACrD,YAAM,OAAO;AACb,YAAM,MAAM;AACZ,YAAM,SAAS;AACf,YAAM,UAAU,IAAI;AACpB,aAAO;AAAA,IACX;AACA,WAAOA;AAAA,EACX,EAAE,QAAQ;AAAA;AAEV,IAAI;AAAA;AAAA,EAAuB,WAAY;AACnC,aAASC,OAAM,OAAO,SAAS;AAC3B,WAAK,aAAa;AAClB,WAAK,KAAK,MAAM;AAChB,WAAK,WAAW,MAAM;AACtB,WAAK,SAAS,MAAM;AACpB,WAAK,QAAQ,MAAM;AACnB,WAAK,UAAU,QAAQ;AACvB,WAAK,OAAO,QAAQ;AACpB,WAAK,OAAO,QAAQ;AACpB,WAAK,OAAO,QAAQ;AAAA,IACxB;AACA,IAAAA,OAAM,UAAU,gBAAgB,SAAU,SAAS;AAC/C,aAAO,KAAK,KAAK,OAAO,SAAU,KAAK,KAAK;AAAE,eAAO,MAAM,IAAI,iBAAiB,OAAO;AAAA,MAAG,GAAG,CAAC;AAAA,IAClG;AACA,IAAAA,OAAM,UAAU,gBAAgB,SAAU,SAAS;AAC/C,aAAO,KAAK,KAAK,OAAO,SAAU,KAAK,KAAK;AAAE,eAAO,MAAM,IAAI,iBAAiB,OAAO;AAAA,MAAG,GAAG,CAAC;AAAA,IAClG;AACA,IAAAA,OAAM,UAAU,UAAU,WAAY;AAClC,aAAO,KAAK,KAAK,OAAO,KAAK,IAAI,EAAE,OAAO,KAAK,IAAI;AAAA,IACvD;AACA,IAAAA,OAAM,UAAU,gBAAgB,SAAU,KAAK,UAAU,MAAM,KAAK,QAAQ,QAAQ;AAChF,eAAS,KAAK,GAAG,aAAa,UAAU,KAAK,WAAW,QAAQ,MAAM;AAClE,YAAI,UAAU,WAAW,EAAE;AAC3B,YAAI,OAAO,IAAI,aAAa,KAAK,MAAM,MAAM,KAAK,QAAQ,MAAM;AAChE,YAAI,SAAS,QAAQ,IAAI,MAAM;AAE/B,aAAK,OAAO,MAAM,QAAQ,KAAK,IAAI,IAAI,KAAK,OAAO,CAAC,KAAK,IAAI;AAC7D,YAAI,QAAQ;AACR,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,IAAAA,OAAM,UAAU,mBAAmB,SAAU,KAAK,QAAQ;AACtD,UAAI,YAAY,IAAI,UAAU;AAC9B,eAAS,KAAK,GAAGN,MAAK,KAAK,MAAM,aAAa,KAAKA,IAAG,QAAQ,MAAM;AAChE,YAAI,UAAUA,IAAG,EAAE;AACnB,gBAAQ,IAAI,SAAS,KAAK,MAAM,MAAM,CAAC;AAAA,MAC3C;AAAA,IACJ;AACA,IAAAM,OAAM,UAAU,wBAAwB,SAAU,KAAK,QAAQ;AAC3D,eAAS,KAAK,GAAGN,MAAK,KAAK,MAAM,cAAc,KAAKA,IAAG,QAAQ,MAAM;AACjE,YAAI,UAAUA,IAAG,EAAE;AACnB,gBAAQ,IAAI,SAAS,KAAK,MAAM,MAAM,CAAC;AAAA,MAC3C;AAAA,IACJ;AACA,IAAAM,OAAM,UAAU,WAAW,SAAU,WAAW;AAC5C,UAAI,OAAO,KAAK,SAAS,eAAe,UAAU;AAC9C,eAAO,KAAK,SAAS;AAAA,MACzB,WACS,KAAK,SAAS,eAAe,QAAQ;AAC1C,YAAI,eAAe,KAAK,QAAQ,OAAO,SAAU,OAAO,KAAK;AAAE,iBAAO,QAAQ,IAAI;AAAA,QAAc,GAAG,CAAC;AACpG,eAAO;AAAA,MACX,OACK;AACD,YAAI,SAAS,KAAK,SAAS;AAC3B,eAAO,YAAY,OAAO,OAAO,OAAO;AAAA,MAC5C;AAAA,IACJ;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AACF,IAAI;AAAA;AAAA,EAAqB,WAAY;AACjC,aAASC,KAAI,KAAK,OAAO,SAAS,OAAO,oBAAoB;AACzD,UAAI,uBAAuB,QAAQ;AAAE,6BAAqB;AAAA,MAAO;AACjE,WAAK,SAAS;AACd,WAAK,MAAM;AACX,UAAI,eAAe,cAAc;AAC7B,aAAK,MAAM,IAAI;AACf,aAAK,UAAU,IAAI;AAAA,MACvB;AACA,WAAK,QAAQ;AACb,WAAK,UAAU;AACf,WAAK,QAAQ;AACb,WAAK,qBAAqB;AAAA,IAC9B;AACA,IAAAA,KAAI,UAAU,mBAAmB,SAAU,SAAS;AAChD,UAAI,QAAQ;AACZ,aAAO,QAAQ,OAAO,SAAU,KAAK,QAAQ;AAAE,YAAIP;AAAI,eAAO,KAAK,IAAI,OAAOA,MAAK,MAAM,MAAM,OAAO,KAAK,OAAO,QAAQA,QAAO,SAAS,SAASA,IAAG,WAAW,CAAC;AAAA,MAAG,GAAG,CAAC;AAAA,IAC7K;AACA,IAAAO,KAAI,UAAU,aAAa,SAAU,SAAS;AAC1C,UAAI,QAAQ;AACZ,aAAQ,QAAQ,OAAO,SAAU,QAAQ;AACrC,YAAI,OAAO,MAAM,MAAM,OAAO,KAAK;AACnC,YAAI,CAAC;AACD,iBAAO;AACX,eAAO,KAAK,UAAU;AAAA,MAC1B,CAAC,EAAE,SAAS;AAAA,IAChB;AACA,IAAAA,KAAI,UAAU,kBAAkB,SAAU,QAAQ,SAAS;AACvD,aAAO,KAAK,iBAAiB,OAAO,KAAK;AAAA,IAC7C;AACA,IAAAA,KAAI,UAAU,sBAAsB,SAAU,SAAS,KAAK;AACxD,UAAI,QAAQ;AACZ,aAAO,QAAQ,OAAO,SAAU,KAAK,QAAQ;AACzC,YAAI,OAAO,MAAM,MAAM,OAAO,KAAK;AACnC,YAAI,CAAC;AACD,iBAAO;AACX,YAAI,aAAa,IAAI,cAAc,KAAK,OAAO,QAAQ;AACvD,YAAI,WAAW,KAAK,QAAQ,UAAU;AACtC,YAAI,eAAe,WAAW;AAC9B,eAAO,eAAe,MAAM,eAAe;AAAA,MAC/C,GAAG,CAAC;AAAA,IACR;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AACF,IAAI;AAAA;AAAA,EAAsB,WAAY;AAClC,aAASC,MAAK,KAAK,QAAQ,SAAS;AAChC,UAAIR;AACJ,WAAK,gBAAgB;AACrB,WAAK,eAAe;AACpB,WAAK,eAAe;AACpB,WAAK,mBAAmB;AACxB,WAAK,WAAW;AAChB,WAAK,QAAQ;AACb,WAAK,SAAS;AACd,WAAK,IAAI;AACT,WAAK,IAAI;AACT,WAAK,SAAS;AACd,WAAK,UAAU;AACf,WAAK,MAAM;AACX,UAAI,UAAU;AACd,UAAI,OAAO,QAAQ,OAAO,QAAQ,YAAY,CAAC,MAAM,QAAQ,GAAG,GAAG;AAC/D,aAAK,UAAU,IAAI,WAAW;AAC9B,aAAK,UAAU,IAAI,WAAW;AAC9B,mBAAWA,MAAK,IAAI,aAAa,QAAQA,QAAO,SAASA,MAAK;AAC9D,YAAI,IAAI,UAAU;AACd,eAAK,MAAM,IAAI;AAAA,QACnB;AAAA,MACJ,OACK;AACD,aAAK,UAAU;AACf,aAAK,UAAU;AAAA,MACnB;AAEA,UAAI,OAAO,WAAW,OAAO,KAAK,UAAU;AAC5C,UAAI,aAAa;AACjB,WAAK,OAAO,KAAK,MAAM,UAAU;AAAA,IACrC;AACA,IAAAQ,MAAK,UAAU,aAAa,WAAY;AACpC,UAAI;AACJ,UAAI,KAAK,OAAO,WAAW,OAAO;AAC9B,YAAI,KAAK,IAAI,KAAK,QAAQ,KAAK;AAAA,MACnC,WACS,KAAK,OAAO,WAAW,UAAU;AACtC,YAAI,KAAK,IAAI,KAAK,SAAS,KAAK,QAAQ,QAAQ;AAAA,MACpD,OACK;AACD,YAAI,YAAY,KAAK,SAAS,KAAK,QAAQ,UAAU;AACrD,YAAI,KAAK,IAAI,YAAY,IAAI,KAAK,QAAQ,KAAK;AAAA,MACnD;AACA,UAAI;AACJ,UAAI,KAAK,OAAO,WAAW,SAAS;AAChC,YAAI,KAAK,IAAI,KAAK,QAAQ,KAAK,QAAQ,OAAO;AAAA,MAClD,WACS,KAAK,OAAO,WAAW,UAAU;AACtC,YAAI,WAAW,KAAK,QAAQ,KAAK,QAAQ,YAAY;AACrD,YAAI,KAAK,IAAI,WAAW,IAAI,KAAK,QAAQ,MAAM;AAAA,MACnD,OACK;AACD,YAAI,KAAK,IAAI,KAAK,QAAQ,MAAM;AAAA,MACpC;AACA,aAAO,EAAE,GAAM,EAAK;AAAA,IACxB;AAEA,IAAAA,MAAK,UAAU,mBAAmB,SAAU,aAAa,kBAAkB;AACvE,UAAI,qBAAqB,QAAQ;AAAE,2BAAmB;AAAA,MAAM;AAC5D,UAAI,YAAY,MAAM,QAAQ,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;AAC9D,UAAI,aAAc,KAAK,OAAO,WAAW,cAAe;AACxD,UAAI,SAAS,YAAY,aAAa,KAAK,QAAQ,UAAU;AAC7D,aAAO,KAAK,IAAI,QAAQ,KAAK,OAAO,aAAa;AAAA,IACrD;AACA,IAAAA,MAAK,UAAU,UAAU,SAAU,MAAM;AACrC,UAAI,UAAU,aAAa,KAAK,OAAO,aAAa,CAAC;AACrD,UAAI,SAAS,YAAY;AACrB,eAAO,QAAQ,MAAM,QAAQ;AAAA,MACjC,WACS,SAAS,cAAc;AAC5B,eAAO,QAAQ,OAAO,QAAQ;AAAA,MAClC,OACK;AACD,eAAO,QAAQ,IAAI;AAAA,MACvB;AAAA,IACJ;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AACF,IAAI;AAAA;AAAA,EAAwB,WAAY;AACpC,aAASC,QAAO,SAAS,KAAK,OAAO;AACjC,WAAK,eAAe;AACpB,WAAK,mBAAmB;AACxB,WAAK,WAAW;AAChB,WAAK,QAAQ;AACb,WAAK,UAAU;AACf,WAAK,MAAM;AACX,WAAK,QAAQ;AAAA,IACjB;AACA,IAAAA,QAAO,UAAU,wBAAwB,SAAU,OAAO;AACtD,UAAI,MAAM;AACV,eAAS,KAAK,GAAGT,MAAK,MAAM,QAAQ,GAAG,KAAKA,IAAG,QAAQ,MAAM;AACzD,YAAI,MAAMA,IAAG,EAAE;AACf,YAAI,OAAO,IAAI,MAAM,KAAK,KAAK;AAC/B,YAAI,QAAQ,OAAO,KAAK,OAAO,cAAc,UAAU;AACnD,gBAAM,KAAK,IAAI,KAAK,KAAK,OAAO,SAAS;AAAA,QAC7C;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,WAAOS;AAAA,EACX,EAAE;AAAA;AAKF,SAAS,gBAAgB,KAAK,OAAO;AACjC,YAAU,KAAK,KAAK;AACpB,MAAI,mBAAmB,CAAC;AACxB,MAAI,oBAAoB;AACxB,QAAM,QAAQ,QAAQ,SAAU,QAAQ;AACpC,QAAI,cAAc,OAAO,sBAAsB,KAAK;AACpD,QAAI,aAAa;AAEb,aAAO,QAAQ;AAAA,IACnB,OACK;AAED,aAAO,QAAQ,OAAO;AACtB,uBAAiB,KAAK,MAAM;AAAA,IAChC;AACA,yBAAqB,OAAO;AAAA,EAChC,CAAC;AAED,MAAI,cAAc,MAAM,SAAS,IAAI,SAAS,EAAE,KAAK,IAAI;AAEzD,MAAI,aAAa;AACb,kBAAc,cAAc,kBAAkB,aAAa,SAAU,QAAQ;AACzE,aAAO,KAAK,IAAI,OAAO,kBAAkB,OAAO,QAAQ;AAAA,IAC5D,CAAC;AAAA,EACL;AAEA,MAAI,aAAa;AACb,kBAAc,cAAc,kBAAkB,aAAa,SAAU,QAAQ;AAAE,aAAO,OAAO;AAAA,IAAU,CAAC;AAAA,EAC5G;AACA,gBAAc,KAAK,IAAI,WAAW;AAClC,MAAI,CAAC,MAAM,SAAS,uBAChB,cAAc,MAAM,IAAI,YAAY,GAAG;AAKvC,kBAAc,cAAc,IAAI,cAAc,KAAK,MAAM,WAAW;AACpE,YAAQ,KAAK,yBAAyB,OAAO,aAAa,iCAAiC,CAAC;AAAA,EAChG;AACA,gBAAc,KAAK;AACnB,aAAW,OAAO,GAAG;AACrB,gBAAc,KAAK;AACvB;AACA,SAAS,UAAU,KAAK,OAAO;AAC3B,MAAI,KAAK,IAAI,YAAY;AACzB,MAAI,sBAAsB,MAAM,SAAS;AACzC,MAAI,qBAAqB,sBAAsB,KAAK,KAAK;AACzD,QAAM,QAAQ,EAAE,QAAQ,SAAU,KAAK;AACnC,aAAS,KAAK,GAAGT,MAAK,MAAM,SAAS,KAAKA,IAAG,QAAQ,MAAM;AACvD,UAAI,SAASA,IAAG,EAAE;AAClB,UAAI,OAAO,IAAI,MAAM,OAAO,KAAK;AACjC,UAAI,CAAC;AACD;AACJ,UAAI,QAAQ,MAAM,MAAM;AACxB,YAAM,cAAc,KAAK,OAAO,MAAM,KAAK,QAAQ,IAAI;AACvD,UAAI,UAAU,KAAK,QAAQ,YAAY;AACvC,WAAK,eAAe,eAAe,KAAK,MAAM,KAAK,QAAQ,GAAG,IAAI;AAKlE,UAAI,mBAAmB,eAAe,KAAK,KAAK,KAAK,GAAG,EAAE,MAAM,cAAc,GAAG,KAAK,QAAQ,GAAG;AACjG,WAAK,mBAAmB,mBAAmB,KAAK,QAAQ,YAAY;AACpE,UAAI,OAAO,KAAK,OAAO,cAAc,UAAU;AAC3C,aAAK,WAAW,KAAK,OAAO;AAC5B,aAAK,eAAe,KAAK,OAAO;AAAA,MACpC,WACS,KAAK,OAAO,cAAc,UAC/B,wBAAwB,MAAM;AAE9B,YAAI,KAAK,eAAe,oBAAoB;AACxC,eAAK,WAAW;AAChB,eAAK,eAAe;AAAA,QACxB,OACK;AACD,eAAK,WAAW,KAAK;AACrB,eAAK,eAAe,KAAK;AAAA,QAC7B;AAAA,MACJ,OACK;AAED,YAAI,kBAAkB,KAAK;AAC3B,aAAK,WAAW,KAAK,OAAO,gBAAgB;AAC5C,aAAK,eAAe,KAAK;AACzB,YAAI,KAAK,WAAW,KAAK,cAAc;AACnC,eAAK,eAAe,KAAK;AAAA,QAC7B;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,QAAM,QAAQ,EAAE,QAAQ,SAAU,KAAK;AACnC,aAAS,KAAK,GAAGA,MAAK,MAAM,SAAS,KAAKA,IAAG,QAAQ,MAAM;AACvD,UAAI,SAASA,IAAG,EAAE;AAClB,UAAI,OAAO,IAAI,MAAM,OAAO,KAAK;AAGjC,UAAI,QAAQ,KAAK,YAAY,GAAG;AAC5B,eAAO,eAAe,KAAK,IAAI,OAAO,cAAc,KAAK,YAAY;AACrE,eAAO,WAAW,KAAK,IAAI,OAAO,UAAU,KAAK,QAAQ;AACzD,eAAO,mBAAmB,KAAK,IAAI,OAAO,kBAAkB,KAAK,gBAAgB;AAAA,MACrF,OACK;AAOD,YAAI,eAAe,MAAM,OAAO,aAAa,OAAO,OAAO,KACvD,MAAM,OAAO,aAAa,OAAO,KAAK,KACtC,CAAC;AACL,YAAI,YAAY,aAAa,aAAa,aAAa;AACvD,YAAI,aAAa,OAAO,cAAc,UAAU;AAC5C,iBAAO,WAAW;AAClB,iBAAO,eAAe;AAAA,QAC1B;AAAA,MACJ;AACA,UAAI,MAAM;AAEN,YAAI,KAAK,UAAU,KAAK,CAAC,OAAO,UAAU;AACtC,iBAAO,WAAW,KAAK;AAAA,QAC3B;AACA,YAAI,KAAK,UAAU,KAAK,CAAC,OAAO,cAAc;AAC1C,iBAAO,eAAe,KAAK;AAAA,QAC/B;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AAIA,SAAS,cAAc,SAAS,aAAa,aAAa;AACtD,MAAI,qBAAqB;AACzB,MAAI,kBAAkB,QAAQ,OAAO,SAAU,KAAKU,SAAQ;AAAE,WAAO,MAAMA,QAAO;AAAA,EAAc,GAAG,CAAC;AACpG,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,QAAI,SAAS,QAAQ,CAAC;AACtB,QAAI,QAAQ,OAAO,eAAe;AAClC,QAAI,kBAAkB,qBAAqB;AAC3C,QAAI,iBAAiB,OAAO,QAAQ;AACpC,QAAI,WAAW,YAAY,MAAM;AACjC,QAAI,WAAW,iBAAiB,WAAW,WAAW;AACtD,mBAAe,WAAW,OAAO;AACjC,WAAO,QAAQ;AAAA,EACnB;AACA,gBAAc,KAAK,MAAM,cAAc,IAAI,IAAI;AAG/C,MAAI,aAAa;AACb,QAAI,mBAAmB,QAAQ,OAAO,SAAUA,SAAQ;AACpD,aAAO,cAAc,IACfA,QAAO,QAAQ,YAAYA,OAAM,IACjC;AAAA,IACV,CAAC;AACD,QAAI,iBAAiB,QAAQ;AACzB,oBAAc,cAAc,kBAAkB,aAAa,WAAW;AAAA,IAC1E;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,cAAc,OAAO;AAC1B,MAAI,eAAe,CAAC;AACpB,MAAI,kBAAkB;AACtB,MAAI,MAAM,MAAM,QAAQ;AACxB,WAAS,WAAW,GAAG,WAAW,IAAI,QAAQ,YAAY;AACtD,QAAI,MAAM,IAAI,QAAQ;AACtB,aAAS,KAAK,GAAGV,MAAK,MAAM,SAAS,KAAKA,IAAG,QAAQ,MAAM;AACvD,UAAI,SAASA,IAAG,EAAE;AAClB,UAAI,OAAO,aAAa,OAAO,KAAK;AACpC,UAAI,kBAAkB,GAAG;AACrB;AACA,eAAO,IAAI,MAAM,OAAO,KAAK;AAAA,MACjC,WACS,MAAM;AACX,aAAK,KAAK,UAAU,IAAI;AACxB,0BAAkB,KAAK,KAAK;AAC5B,eAAO,IAAI,MAAM,OAAO,KAAK;AAC7B,aAAK;AACL,YAAI,KAAK,QAAQ,GAAG;AAChB,iBAAO,aAAa,OAAO,KAAK;AAAA,QACpC;AAAA,MACJ,OACK;AACD,YAAI,OAAO,IAAI,MAAM,OAAO,KAAK;AACjC,YAAI,CAAC,MAAM;AACP;AAAA,QACJ;AACA,aAAK,SAAS,IAAI;AAClB,YAAI,KAAK,UAAU,GAAG;AAClB,cAAI,YAAY,IAAI,SAAS;AAC7B,cAAI,OAAO,KAAK,UAAU,YAAY,YAAY,KAAK;AACvD,uBAAa,OAAO,KAAK,IAAI,EAAE,MAAY,MAAY,IAAS;AAAA,QACpE;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,SAAS,cAAc,OAAO;AAC1B,MAAI,MAAM,MAAM,QAAQ;AACxB,WAAS,WAAW,GAAG,WAAW,IAAI,QAAQ,YAAY;AACtD,QAAI,MAAM,IAAI,QAAQ;AACtB,QAAI,cAAc;AAClB,QAAI,uBAAuB;AAC3B,QAAI,eAAe;AACnB,aAAS,cAAc,GAAG,cAAc,MAAM,QAAQ,QAAQ,eAAe;AACzE,UAAI,SAAS,MAAM,QAAQ,WAAW;AAEtC,sBAAgB;AAChB,UAAI,eAAe,KAAK,MAAM,QAAQ,cAAc,CAAC,GAAG;AACpD,gCAAwB,OAAO;AAC/B,eAAO,IAAI,MAAM,OAAO,KAAK;AAAA,MACjC,WACS,aAAa;AAClB,YAAI,OAAO;AACX,eAAO,IAAI,MAAM,OAAO,KAAK;AAC7B,sBAAc;AACd,aAAK,QAAQ,OAAO,QAAQ;AAAA,MAChC,OACK;AACD,YAAI,OAAO,IAAI,MAAM,OAAO,KAAK;AACjC,YAAI,CAAC;AACD;AACJ,uBAAe,KAAK;AACpB,+BAAuB;AACvB,YAAI,KAAK,UAAU,GAAG;AAClB,wBAAc;AACd,kCAAwB,OAAO;AAC/B;AAAA,QACJ;AACA,aAAK,QAAQ,OAAO,QAAQ;AAAA,MAChC;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,SAAS,WAAW,OAAO,KAAK;AAC5B,MAAI,gBAAgB,EAAE,OAAO,GAAG,QAAQ,EAAE;AAC1C,WAAS,KAAK,GAAGA,MAAK,MAAM,QAAQ,GAAG,KAAKA,IAAG,QAAQ,MAAM;AACzD,QAAI,MAAMA,IAAG,EAAE;AACf,aAAS,KAAK,GAAG,KAAK,MAAM,SAAS,KAAK,GAAG,QAAQ,MAAM;AACvD,UAAI,SAAS,GAAG,EAAE;AAClB,UAAI,OAAO,IAAI,MAAM,OAAO,KAAK;AACjC,UAAI,CAAC;AACD;AACJ,UAAI,YAAY,KAAK,QAAQ,IAAI;AACjC,UAAI,YAAY,KAAK,QAAQ,KAAK,QAAQ,YAAY;AACtD,UAAI,KAAK,OAAO,aAAa,aAAa;AAEtC,aAAK,OAAO,IAAI,gBAAgB,KAAK,MAAM,YAAY,IAAI,IAAI,YAAY,GAAG,EAAE,UAAU,KAAK,OAAO,SAAS,CAAC;AAAA,MACpH,WACS,KAAK,OAAO,aAAa,aAAa;AAC3C,aAAK,OAAO,UAAU,KAAK,MAAM,WAAW,KAAK,QAAQ,KAAK,KAAK;AAAA,MACvE,WACS,KAAK,OAAO,aAAa,UAAU;AACxC,aAAK,OAAO,UAAU,KAAK,MAAM,WAAW,KAAK,QAAQ,KAAK,EAAE;AAAA,MACpE,WACS,OAAO,KAAK,OAAO,aAAa,YAAY;AACjD,YAAI,SAAS,KAAK,OAAO,SAAS,KAAK,MAAM,SAAS;AACtD,YAAI,OAAO,WAAW,UAAU;AAC5B,eAAK,OAAO,CAAC,MAAM;AAAA,QACvB,OACK;AACD,eAAK,OAAO;AAAA,QAChB;AAAA,MACJ;AACA,WAAK,gBAAgB,KAAK,iBAAiB,IAAI,YAAY,GAAG,IAAI,oBAAoB,CAAC;AACvF,UAAI,oBAAoB,KAAK,gBAAgB,KAAK;AAClD,UAAI,KAAK,UAAU,KACf,cAAc,QAAQ,cAAc,SAChC,oBAAoB,KAAK,SAAS;AACtC,wBAAgB,EAAE,QAAQ,mBAAmB,OAAO,KAAK,QAAQ;AAAA,MACrE,WACS,iBAAiB,cAAc,QAAQ,GAAG;AAC/C,YAAI,cAAc,SAAS,mBAAmB;AAC1C,8BAAoB,cAAc;AAAA,QACtC;AAAA,MACJ;AACA,UAAI,oBAAoB,IAAI,QAAQ;AAChC,YAAI,SAAS;AAAA,MACjB;AAAA,IACJ;AACA,kBAAc;AAAA,EAClB;AACJ;AACA,SAAS,UAAU,MAAM,OAAO,QAAQ,KAAK,UAAU;AACnD,SAAO,KAAK,IAAI,SAAU,KAAK;AAAE,WAAO,aAAa,KAAK,OAAO,QAAQ,KAAK,QAAQ;AAAA,EAAG,CAAC;AAC9F;AACA,SAAS,aAAa,MAAM,OAAO,QAAQ,KAAK,UAAU;AACtD,MAAI,YAAY,MAAQ,IAAI,YAAY;AACxC,UAAQ,KAAK,KAAK,QAAQ,SAAS,IAAI;AACvC,MAAI,SAAS,eAAe,MAAM,QAAQ,GAAG,GAAG;AAC5C,WAAO;AAAA,EACX;AACA,SAAO,QAAQ,eAAe,OAAO,UAAU,QAAQ,GAAG,GAAG;AACzD,QAAI,KAAK,UAAU,GAAG;AAClB;AAAA,IACJ;AACA,WAAO,KAAK,UAAU,GAAG,KAAK,SAAS,CAAC;AAAA,EAC5C;AACA,SAAO,KAAK,KAAK,IAAI;AACzB;AAEA,SAAS,YAAY,UAAU,OAAO;AAClC,MAAI,MAAM,IAAI,WAAW,QAAQ;AACjC,MAAI,UAAU,aAAa,OAAO,IAAI,YAAY,CAAC;AACnD,MAAI,QAAQ,IAAI,MAAM,OAAO,OAAO;AACpC,kBAAgB,KAAK,KAAK;AAC1B,MAAI,YAAY,IAAI,UAAU;AAC9B,SAAO;AACX;AACA,SAAS,aAAa,OAAO,IAAI;AAC7B,MAAI,UAAU,MAAM;AACpB,MAAI,UAAU,cAAc,QAAQ,OAAO;AAE3C,MAAI,QAAQ,KAAK,WAAW,GAAG;AAC3B,QAAI,aAAa,mBAAmB,SAAS,MAAM;AACnD,QAAI;AACA,cAAQ,KAAK,KAAK,UAAU;AAAA,EACpC;AACA,MAAI,QAAQ,KAAK,WAAW,GAAG;AAC3B,QAAI,aAAa,mBAAmB,SAAS,MAAM;AACnD,QAAI;AACA,cAAQ,KAAK,KAAK,UAAU;AAAA,EACpC;AACA,MAAI,QAAQ,MAAM,SAAS;AAC3B,MAAI,SAAS,MAAM;AACnB,SAAO;AAAA,IACH;AAAA,IACA,MAAM,aAAa,QAAQ,QAAQ,MAAM,SAAS,QAAQ,OAAO,EAAE;AAAA,IACnE,MAAM,aAAa,QAAQ,QAAQ,MAAM,SAAS,QAAQ,OAAO,EAAE;AAAA,IACnE,MAAM,aAAa,QAAQ,QAAQ,MAAM,SAAS,QAAQ,OAAO,EAAE;AAAA,EACvE;AACJ;AACA,SAAS,aAAa,aAAa,aAAa,SAAS,YAAY,OAAO,aAAa;AACrF,MAAI,wBAAwB,CAAC;AAC7B,MAAI,SAAS,YAAY,IAAI,SAAU,QAAQ,UAAU;AACrD,QAAI,wBAAwB;AAC5B,QAAI,QAAQ,CAAC;AACb,QAAI,gBAAgB;AACpB,QAAI,kBAAkB;AACtB,aAAS,KAAK,GAAG,YAAY,SAAS,KAAK,UAAU,QAAQ,MAAM;AAC/D,UAAI,SAAS,UAAU,EAAE;AACzB,UAAI,sBAAsB,OAAO,KAAK,KAAK,QACvC,sBAAsB,OAAO,KAAK,EAAE,SAAS,GAAG;AAChD,YAAI,oBAAoB,GAAG;AACvB,cAAI,UAAU;AACd,cAAI,MAAM,QAAQ,MAAM,GAAG;AACvB,sBACI,OAAO,OAAO,QAAQ,gBAAgB,qBAAqB;AAAA,UACnE,OACK;AACD,sBAAU,OAAO,OAAO,OAAO;AAAA,UACnC;AACA,cAAI,kBAAkB,CAAC;AACvB,cAAI,OAAO,YAAY,YAAY,CAAC,MAAM,QAAQ,OAAO,GAAG;AACxD,+BAAmB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,WAAW,CAAC;AAAA,UAC7F;AACA,cAAI,SAAS,WAAW,aAAa,QAAQ,UAAU,OAAO,YAAY,aAAa,eAAe;AACtG,cAAI,OAAO,IAAI,KAAK,SAAS,QAAQ,WAAW;AAGhD,gBAAM,OAAO,OAAO,IAAI;AACxB,gBAAM,OAAO,KAAK,IAAI;AACtB,4BAAkB,KAAK,UAAU;AACjC,gCAAsB,OAAO,KAAK,IAAI;AAAA,YAClC,MAAM,KAAK,UAAU;AAAA,YACrB,OAAO;AAAA,UACX;AAAA,QACJ,OACK;AACD;AACA;AAAA,QACJ;AAAA,MACJ,OACK;AACD,8BAAsB,OAAO,KAAK,EAAE;AACpC,0BAAkB,sBAAsB,OAAO,KAAK,EAAE;AACtD;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,IAAI,IAAI,QAAQ,UAAU,aAAa,KAAK;AAAA,EACvD,CAAC;AACD,SAAO;AACX;AACA,SAAS,mBAAmB,SAAS,SAAS;AAC1C,MAAI,aAAa,CAAC;AAClB,UAAQ,QAAQ,SAAU,KAAK;AAC3B,QAAI,IAAI,OAAO,MAAM;AACjB,UAAI,QAAQ,gBAAgB,SAAS,IAAI,GAAG;AAC5C,UAAI,SAAS;AACT,mBAAW,IAAI,OAAO,IAAI;AAAA,IAClC;AAAA,EACJ,CAAC;AACD,SAAO,OAAO,KAAK,UAAU,EAAE,SAAS,IAAI,aAAa;AAC7D;AACA,SAAS,gBAAgB,SAAS,QAAQ;AACtC,MAAI,YAAY,QAAQ;AACpB,QAAI,OAAO,WAAW,UAAU;AAC5B,aAAO,OAAO,UAAU;AAAA,IAC5B,WACS,OAAO,WAAW,YAAY,OAAO,WAAW,UAAU;AAC/D,aAAO;AAAA,IACX;AAAA,EACJ,WACS,YAAY,UAAU,OAAO,WAAW,UAAU;AACvD,WAAO,OAAO;AAAA,EAClB;AACA,SAAO;AACX;AACA,SAAS,cAAc,SAAS;AAC5B,SAAO,QAAQ,IAAI,SAAU,OAAO,OAAO;AACvC,QAAIA;AACJ,QAAI;AACJ,QAAI,OAAO,UAAU,UAAU;AAC3B,aAAOA,MAAK,MAAM,aAAa,QAAQA,QAAO,SAASA,MAAK;AAAA,IAChE,OACK;AACD,YAAM;AAAA,IACV;AACA,WAAO,IAAI,OAAO,KAAK,OAAO,KAAK;AAAA,EACvC,CAAC;AACL;AACA,SAAS,WAAW,aAAa,QAAQ,UAAU,WAAW,QAAQ,aAAa,iBAAiB;AAChG,MAAI,QAAQ,SAAS,SAAS;AAC9B,MAAI;AACJ,MAAI,gBAAgB,QAAQ;AACxB,oBAAgB,OAAO;AAAA,EAC3B,WACS,gBAAgB,QAAQ;AAC7B,oBAAgB,OAAO;AAAA,EAC3B,WACS,gBAAgB,QAAQ;AAC7B,oBAAgB,OAAO;AAAA,EAC3B;AACA,MAAI,cAAc,OAAO,CAAC,GAAG,MAAM,OAAO,MAAM,WAAW,GAAG,OAAO,QAAQ,aAAa;AAC1F,MAAI,eAAe,OAAO,aAAa,OAAO,OAAO,KACjD,OAAO,aAAa,OAAO,KAAK,KAChC,CAAC;AACL,MAAI,YAAY,gBAAgB,SAAS,eAAe,CAAC;AACzD,MAAI,YAAY,gBAAgB,UAAU,WAAW,MAAM,IACrD,OAAO,CAAC,GAAG,MAAM,cAAc,OAAO,kBAAkB,IACxD,CAAC;AACP,MAAI,eAAe,cAAc,WAAW;AAC5C,MAAI,cAAc,OAAO,CAAC,GAAG,cAAc,aAAa,WAAW,SAAS;AAC5E,SAAO,OAAO,aAAa,eAAe;AAC9C;AAGA,SAAS,uBAAuB,KAAK,OAAO,QAAQ;AAChD,MAAIA;AACJ,MAAI,WAAW,QAAQ;AAAE,aAAS,CAAC;AAAA,EAAG;AAEtC,MAAI,iBAAiB,sBAAsB,KAAK,KAAK;AAErD,MAAI,mBAAmB,oBAAI,IAAI;AAC/B,MAAI,aAAa,CAAC;AAClB,MAAI,UAAU,CAAC;AACf,MAAI,4BAA4B,CAAC;AACjC,MAAI,MAAM,QAAQ,MAAM,SAAS,yBAAyB,GAAG;AACzD,gCAA4B,MAAM,SAAS;AAAA,EAE/C,WACS,OAAO,MAAM,SAAS,8BAA8B,YACzD,OAAO,MAAM,SAAS,8BAA8B,UAAU;AAC9D,gCAA4B,CAAC,MAAM,SAAS,yBAAyB;AAAA,EACzE;AAEA,4BAA0B,QAAQ,SAAU,OAAO;AAC/C,QAAI,MAAM,MAAM,QAAQ,KAAK,SAAU,MAAM;AAAE,aAAO,KAAK,YAAY,SAAS,KAAK,UAAU;AAAA,IAAO,CAAC;AACvG,QAAI,OAAO,CAAC,iBAAiB,IAAI,IAAI,KAAK,GAAG;AACzC,uBAAiB,IAAI,IAAI,OAAO,IAAI;AACpC,iBAAW,KAAK,IAAI,KAAK;AACzB,cAAQ,KAAK,MAAM,QAAQ,IAAI,KAAK,CAAC;AACrC,wBAAkB,IAAI;AAAA,IAC1B;AAAA,EACJ,CAAC;AACD,MAAI,QAAQ;AACZ,MAAI,KAAKA,MAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,WAAW,QAAQA,QAAO,SAASA,MAAK;AAC7G,SAAO,IAAI,MAAM,QAAQ,QAAQ;AAE7B,QAAI,iBAAiB,IAAI,CAAC,GAAG;AACzB;AACA;AAAA,IACJ;AACA,QAAI,WAAW,MAAM,QAAQ,CAAC,EAAE;AAEhC,QAAI,SAAS,kBAAkB,UAAU;AACrC,cAAQ;AACR,iBAAW,KAAK,CAAC;AACjB,cAAQ,KAAK,MAAM,QAAQ,CAAC,CAAC;AAC7B,wBAAkB;AAAA,IACtB,OACK;AACD;AAAA,IACJ;AACA;AAAA,EACJ;AACA,SAAO,EAAE,YAAwB,SAAkB,WAAW,IAAI,EAAE;AACxE;AACA,SAAS,gCAAgC,KAAK,OAAO;AACjD,MAAI,aAAa,CAAC;AAClB,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,QAAQ,KAAK;AAC3C,QAAI,SAAS,uBAAuB,KAAK,OAAO,EAAE,OAAO,EAAE,CAAC;AAC5D,QAAI,OAAO,QAAQ,QAAQ;AACvB,iBAAW,KAAK,MAAM;AACtB,UAAI,OAAO;AAAA,IACf;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,UAAU,UAAU,OAAO;AAChC,MAAI,WAAW,MAAM;AACrB,MAAI,SAAS,SAAS;AACtB,MAAI,SAAS,SAAS;AACtB,MAAI,SAAS,EAAE,GAAG,OAAO,MAAM,GAAG,OAAO;AACzC,MAAI,iBAAiB,MAAM,cAAc,MAAM,OAAO,IAAI,MAAM,cAAc,MAAM,OAAO;AAC3F,MAAI,oBAAoB,SAAS,OAAO,SAAS;AACjD,MAAI,SAAS,cAAc,SAAS;AAChC,QAAI,OAAO,MAAM;AACjB,QAAI,cAAc,KAAK,OAAO,SAAU,KAAK,KAAK;AAAE,aAAO,MAAM,IAAI;AAAA,IAAQ,GAAG,CAAC;AACjF,yBAAqB;AAAA,EACzB;AACA,MAAI,MAAM,IAAI,WAAW,QAAQ;AACjC,MAAI,SAAS,cAAc,YACtB,SAAS,UAAU,QAAQ,oBAAoB,IAAI,SAAS,EAAE,QAAS;AACxE,aAAS,GAAG;AACZ,WAAO,IAAI,OAAO;AAAA,EACtB;AACA,QAAM,sBAAsB,KAAK,MAAM;AACvC,MAAI,WAAW,OAAO,CAAC,GAAG,MAAM;AAChC,QAAM,kBAAkB,IAAI,WAAW;AACvC,MAAI,SAAS,qBAAqB;AAE9B,sCAAkC,KAAK,OAAO,UAAU,MAAM;AAAA,EAClE,OACK;AAED,QAAI,YAAY,IAAI,UAAU;AAC9B,QAAI,SAAS,aAAa,eACtB,SAAS,aAAa,aAAa;AACnC,YAAM,KAAK,QAAQ,SAAU,KAAK;AAC9B,eAAO,SAAS,KAAK,OAAO,KAAK,QAAQ,MAAM,OAAO;AAAA,MAC1D,CAAC;AAAA,IACL;AACA,QAAI,YAAY,IAAI,UAAU;AAC9B,UAAM,KAAK,QAAQ,SAAU,KAAK,OAAO;AACrC,UAAI,YAAY,UAAU,MAAM,KAAK,SAAS;AAC9C,mBAAa,KAAK,OAAO,KAAK,WAAW,UAAU,QAAQ,MAAM,OAAO;AAAA,IAC5E,CAAC;AACD,QAAI,YAAY,IAAI,UAAU;AAC9B,QAAI,SAAS,aAAa,cAAc,SAAS,aAAa,aAAa;AACvE,YAAM,KAAK,QAAQ,SAAU,KAAK;AAC9B,eAAO,SAAS,KAAK,OAAO,KAAK,QAAQ,MAAM,OAAO;AAAA,MAC1D,CAAC;AAAA,IACL;AAAA,EACJ;AACA,iBAAe,KAAK,OAAO,UAAU,MAAM;AAC3C,QAAM,iBAAiB,KAAK,MAAM;AAClC,QAAM,SAAS,OAAO;AACtB,WAAS,gBAAgB;AACzB,MAAI,YAAY,IAAI,UAAU;AAClC;AACA,SAAS,kCAAkC,KAAK,OAAO,UAAU,QAAQ;AAErE,MAAI,yBAAyB,gCAAgC,KAAK,KAAK;AACvE,MAAI,WAAW,MAAM;AACrB,MAAI,SAAS,iCAAiC,gBAAgB;AAC1D,2BAAuB,QAAQ,SAAU,gBAAgB,OAAO;AAC5D,UAAI,YAAY,IAAI,UAAU;AAE9B,UAAI,QAAQ,GAAG;AAGX,gBAAQ,KAAK,OAAO,UAAU,QAAQ,eAAe,SAAS,IAAI;AAAA,MACtE,OACK;AAED,kBAAU,KAAK,OAAO,QAAQ,eAAe,OAAO;AAAA,MACxD;AAEA,gBAAU,KAAK,OAAO,UAAU,QAAQ,eAAe,OAAO;AAC9D,gBAAU,KAAK,OAAO,QAAQ,eAAe,OAAO;AAAA,IACxD,CAAC;AAAA,EACL,OACK;AACD,QAAI,2BAA2B;AAC/B,QAAI,0BAA0B,uBAAuB,CAAC;AACtD,QAAI,UAAU,WAAY;AAEtB,UAAI,sBAAsB;AAC1B,UAAI,yBAAyB;AACzB,YAAI,YAAY,IAAI,UAAU;AAC9B,YAAI,oBAAoB,wBAAwB;AAChD,YAAI,4BAA4B,GAAG;AAG/B,kBAAQ,KAAK,OAAO,UAAU,QAAQ,mBAAmB,IAAI;AAAA,QACjE,OACK;AACD,oBAAU,KAAK,OAAO,QAAQ,iBAAiB;AAAA,QACnD;AACA,8BAAsB,2BAA2B,KAAK,OAAO,2BAA2B,GAAG,QAAQ,iBAAiB;AACpH,kBAAU,KAAK,OAAO,QAAQ,iBAAiB;AAAA,MACnD;AAEA,UAAI,kBAAkB,sBAAsB;AAE5C,6BAAuB,MAAM,CAAC,EAAE,QAAQ,SAAU,gBAAgB;AAC9D,YAAI,YAAY,IAAI,UAAU;AAG9B,gBAAQ,KAAK,OAAO,UAAU,QAAQ,eAAe,SAAS,IAAI;AAClE,mCAA2B,KAAK,OAAO,2BAA2B,GAAG,QAAQ,eAAe,SAAS,eAAe;AACpH,kBAAU,KAAK,OAAO,QAAQ,eAAe,OAAO;AAAA,MACxD,CAAC;AACD,iCAA2B;AAAA,IAC/B;AACA,WAAO,2BAA2B,MAAM,KAAK,SAAS,GAAG;AACrD,cAAQ;AAAA,IACZ;AAAA,EACJ;AACJ;AACA,SAAS,UAAU,KAAK,OAAO,QAAQ,SAAS;AAC5C,MAAI,WAAW,MAAM;AACrB,MAAI,YAAY,IAAI,UAAU;AAC9B,MAAI,SAAS,aAAa,eAAe,SAAS,aAAa,aAAa;AACxE,UAAM,KAAK,QAAQ,SAAU,KAAK;AAAE,aAAO,SAAS,KAAK,OAAO,KAAK,QAAQ,OAAO;AAAA,IAAG,CAAC;AAAA,EAC5F;AACJ;AACA,SAAS,UAAU,KAAK,OAAO,UAAU,QAAQ,SAAS;AACtD,MAAI,YAAY,IAAI,UAAU;AAC9B,QAAM,KAAK,QAAQ,SAAU,KAAK,OAAO;AACrC,QAAI,YAAY,UAAU,MAAM,KAAK,SAAS;AAC9C,iBAAa,KAAK,OAAO,KAAK,WAAW,UAAU,QAAQ,OAAO;AAAA,EACtE,CAAC;AACL;AACA,SAAS,2BAA2B,KAAK,OAAO,eAAe,QAAQ,SAAS,iBAAiB;AAC7F,MAAI,YAAY,IAAI,UAAU;AAC9B,oBAAkB,oBAAoB,QAAQ,oBAAoB,SAAS,kBAAkB,MAAM,KAAK;AACxG,MAAI,cAAc,KAAK,IAAI,gBAAgB,iBAAiB,MAAM,KAAK,MAAM;AAC7E,MAAI,sBAAsB;AAC1B,QAAM,KAAK,MAAM,eAAe,WAAW,EAAE,QAAQ,SAAU,KAAK,OAAO;AACvE,QAAI,YAAY,gBAAgB,UAAU,MAAM,KAAK,SAAS;AAC9D,QAAI,iBAAiB,sBAAsB,KAAK,OAAO,WAAW,MAAM;AACxE,QAAI,IAAI,gBAAgB,gBAAgB,OAAO,GAAG;AAC9C,eAAS,KAAK,OAAO,KAAK,QAAQ,OAAO;AACzC,4BAAsB,gBAAgB;AAAA,IAC1C;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AACA,SAAS,UAAU,KAAK,OAAO,QAAQ,SAAS;AAC5C,MAAI,WAAW,MAAM;AACrB,MAAI,YAAY,IAAI,UAAU;AAC9B,MAAI,SAAS,aAAa,cAAc,SAAS,aAAa,aAAa;AACvE,UAAM,KAAK,QAAQ,SAAU,KAAK;AAAE,aAAO,SAAS,KAAK,OAAO,KAAK,QAAQ,OAAO;AAAA,IAAG,CAAC;AAAA,EAC5F;AACJ;AACA,SAAS,sBAAsB,MAAM,oBAAoB,KAAK;AAC1D,MAAI,aAAa,IAAI,cAAc,KAAK,OAAO,QAAQ;AACvD,MAAI,WAAW,KAAK,QAAQ,UAAU;AACtC,MAAI,iBAAiB,KAAK,OAAO,qBAAqB,YAAY,UAAU;AAC5E,SAAO,KAAK,IAAI,GAAG,cAAc;AACrC;AACA,SAAS,eAAe,KAAK,oBAAoB,OAAO,KAAK;AACzD,MAAI,QAAQ,CAAC;AACb,MAAI,qBAAqB;AACzB,MAAI,SAAS;AACb,MAAI,YAAY;AAChB,WAAS,KAAK,GAAGA,MAAK,MAAM,SAAS,KAAKA,IAAG,QAAQ,MAAM;AACvD,QAAI,SAASA,IAAG,EAAE;AAClB,QAAI,OAAO,IAAI,MAAM,OAAO,KAAK;AACjC,QAAI,CAAC;AACD;AACJ,QAAI,CAAC,MAAM,QAAQ,KAAK,IAAI,GAAG;AAC3B,WAAK,OAAO,CAAC,KAAK,IAAI;AAAA,IAC1B;AACA,QAAI,gBAAgB,IAAI,KAAK,KAAK,KAAK,KAAK,QAAQ,KAAK,OAAO;AAChE,oBAAgB,OAAO,eAAe,IAAI;AAC1C,kBAAc,OAAO,CAAC;AACtB,QAAI,qBAAqB,sBAAsB,MAAM,oBAAoB,GAAG;AAC5E,QAAI,KAAK,KAAK,SAAS,oBAAoB;AACvC,oBAAc,OAAO,KAAK,KAAK,OAAO,oBAAoB,KAAK,KAAK,MAAM;AAAA,IAC9E;AACA,QAAI,cAAc,IAAI,YAAY;AAClC,QAAI,mBAAmB,IAAI,oBAAoB;AAC/C,SAAK,gBAAgB,KAAK,iBAAiB,aAAa,gBAAgB;AACxE,QAAI,KAAK,iBAAiB,oBAAoB;AAC1C,WAAK,gBAAgB;AACrB,oBAAc,OAAO,iBAAiB;AAAA,IAC1C;AACA,QAAI,KAAK,gBAAgB,IAAI,QAAQ;AACjC,UAAI,SAAS,KAAK;AAAA,IACtB;AACA,kBAAc,gBAAgB,cAAc,iBAAiB,aAAa,gBAAgB;AAC1F,QAAI,cAAc,gBAAgB,WAAW;AACzC,kBAAY,cAAc;AAAA,IAC9B;AACA,UAAM,OAAO,KAAK,IAAI;AAAA,EAC1B;AACA,MAAI,eAAe,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,SAAS,OAAO,IAAI;AAChE,eAAa,SAAS;AACtB,WAAS,KAAK,GAAG,KAAK,MAAM,SAAS,KAAK,GAAG,QAAQ,MAAM;AACvD,QAAI,SAAS,GAAG,EAAE;AAClB,QAAI,gBAAgB,aAAa,MAAM,OAAO,KAAK;AACnD,QAAI,eAAe;AACf,oBAAc,SAAS,aAAa;AAAA,IACxC;AACA,QAAI,OAAO,IAAI,MAAM,OAAO,KAAK;AACjC,QAAI,MAAM;AACN,WAAK,SAAS,IAAI;AAAA,IACtB;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,yBAAyB,KAAK,KAAK,oBAAoB,OAAO;AACnE,MAAI,aAAa,IAAI,SAAS,EAAE;AAChC,MAAI,SAAS,MAAM,SAAS;AAC5B,MAAI,eAAe,OAAO,MAAM,OAAO;AACvC,MAAI,eAAe,aAAa;AAChC,MAAI,IAAI,YAAY,QAAQ;AAGxB,oBACI,MAAM,cAAc,MAAM,OAAO,IAAI,MAAM,cAAc,MAAM,OAAO;AAAA,EAC9E;AACA,MAAI,eAAe,IAAI,oBAAoB,MAAM,SAAS,GAAG;AAC7D,MAAI,aAAa,eAAe;AAChC,MAAI,eAAe,cAAc;AAC7B,YAAQ,MAAM,iCAAiC,OAAO,IAAI,OAAO,iEAAiE,CAAC;AACnI,WAAO;AAAA,EACX;AACA,MAAI,CAAC,YAAY;AACb,WAAO;AAAA,EACX;AACA,MAAI,oBAAoB,IAAI,WAAW,MAAM,OAAO;AACpD,MAAI,oBAAoB,IAAI,iBAAiB,MAAM,OAAO,IAAI;AAC9D,MAAI,mBAAmB;AACnB,QAAI,mBAAmB;AACnB,cAAQ,MAAM,sBAAsB,OAAO,IAAI,OAAO,yIAAyI,CAAC;AAAA,IACpM;AACA,WAAO;AAAA,EACX;AACA,MAAI,mBAAmB;AAEnB,WAAO;AAAA,EACX;AACA,MAAI,MAAM,SAAS,iBAAiB,SAAS;AACzC,WAAO;AAAA,EACX;AAEA,SAAO;AACX;AACA,SAAS,aAAa,KAAK,OAAO,KAAK,WAAW,UAAU,QAAQ,SAAS;AACzE,MAAI,iBAAiB,sBAAsB,KAAK,OAAO,WAAW,MAAM;AACxE,MAAI,IAAI,gBAAgB,gBAAgB,OAAO,GAAG;AAE9C,aAAS,KAAK,OAAO,KAAK,QAAQ,OAAO;AAAA,EAC7C,WACS,yBAAyB,KAAK,KAAK,gBAAgB,KAAK,GAAG;AAEhE,QAAI,eAAe,eAAe,KAAK,gBAAgB,OAAO,GAAG;AACjE,aAAS,KAAK,OAAO,KAAK,QAAQ,OAAO;AACzC,YAAQ,KAAK,OAAO,UAAU,QAAQ,OAAO;AAC7C,iBAAa,KAAK,OAAO,cAAc,WAAW,UAAU,QAAQ,OAAO;AAAA,EAC/E,OACK;AAED,YAAQ,KAAK,OAAO,UAAU,QAAQ,OAAO;AAC7C,iBAAa,KAAK,OAAO,KAAK,WAAW,UAAU,QAAQ,OAAO;AAAA,EACtE;AACJ;AACA,SAAS,SAAS,KAAK,OAAO,KAAK,QAAQ,SAAS;AAChD,SAAO,IAAI,MAAM,SAAS,OAAO;AACjC,WAAS,KAAK,GAAG,YAAY,SAAS,KAAK,UAAU,QAAQ,MAAM;AAC/D,QAAI,SAAS,UAAU,EAAE;AACzB,QAAI,OAAO,IAAI,MAAM,OAAO,KAAK;AACjC,QAAI,CAAC,MAAM;AACP,aAAO,KAAK,OAAO;AACnB;AAAA,IACJ;AACA,QAAI,YAAY,KAAK,MAAM;AAC3B,SAAK,IAAI,OAAO;AAChB,SAAK,IAAI,OAAO;AAChB,QAAI,SAAS,MAAM,cAAc,KAAK,MAAM,MAAM,cAAc,MAAM,KAAK,QAAQ,MAAM;AACzF,QAAI,WAAW,OAAO;AAClB,aAAO,KAAK,OAAO;AACnB;AAAA,IACJ;AACA,iBAAa,KAAK,MAAM,MAAM;AAC9B,QAAI,UAAU,KAAK,WAAW;AAC9B,kBAAc,KAAK,MAAM,QAAQ,GAAG,QAAQ,GAAG;AAAA,MAC3C,QAAQ,KAAK,OAAO;AAAA,MACpB,QAAQ,KAAK,OAAO;AAAA,MACpB,UAAU,KAAK,KAAK,KAAK,QAAQ,KAAK,QAAQ,MAAM,IAAI,KAAK,QAAQ,OAAO,CAAC;AAAA,IACjF,GAAG,IAAI,YAAY,CAAC;AACpB,UAAM,cAAc,KAAK,MAAM,MAAM,aAAa,MAAM,KAAK,QAAQ,MAAM;AAC3E,WAAO,KAAK,OAAO;AAAA,EACvB;AACA,SAAO,KAAK,IAAI;AACpB;AACA,SAAS,aAAa,KAAK,MAAM,QAAQ;AACrC,MAAIE,cAAa,KAAK;AAGtB,MAAI,YAAY,EAAE,aAAa,IAAI,YAAY,EAAE,aAAa,CAAC;AAC/D,MAAI,OAAOA,YAAW,cAAc,UAAU;AAE1C,QAAI,YAAY,aAAaA,YAAW,WAAWA,YAAW,SAAS;AACvE,QAAI,WAAW;AACX,UAAI,KAAK,KAAK,GAAG,OAAO,GAAG,KAAK,OAAO,KAAK,QAAQ,SAAS;AAAA,IACjE;AAAA,EACJ,WACS,OAAOA,YAAW,cAAc,UAAU;AAE/C,QAAIA,YAAW,WAAW;AACtB,UAAI,KAAK,KAAK,GAAG,OAAO,GAAG,KAAK,OAAO,KAAK,QAAQ,GAAG;AAAA,IAC3D;AAEA,oBAAgB,KAAK,MAAM,QAAQA,YAAW,SAAS;AAAA,EAC3D;AACJ;AAUA,SAAS,gBAAgB,KAAK,MAAM,QAAQ,WAAW;AACnD,MAAI,IAAI,IAAI,IAAI;AAChB,MAAI,UAAU,KAAK;AACf,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,OAAO,IAAI,KAAK;AACrB,SAAK,OAAO;AACZ,QAAI,UAAU,OAAO;AACjB,YAAM,MAAM,UAAU;AAAA,IAC1B;AACA,QAAI,UAAU,MAAM;AAChB,YAAM,MAAM,UAAU;AAAA,IAC1B;AACA,aAAS,UAAU,KAAK,IAAI,IAAI,IAAI,EAAE;AAAA,EAC1C;AACA,MAAI,UAAU,QAAQ;AAClB,SAAK,OAAO;AACZ,SAAK,OAAO,IAAI,KAAK;AACrB,SAAK,OAAO,IAAI,KAAK;AACrB,SAAK,OAAO,IAAI,KAAK;AACrB,QAAI,UAAU,OAAO;AACjB,YAAM,MAAM,UAAU;AAAA,IAC1B;AACA,QAAI,UAAU,MAAM;AAChB,YAAM,MAAM,UAAU;AAAA,IAC1B;AACA,aAAS,UAAU,QAAQ,IAAI,IAAI,IAAI,EAAE;AAAA,EAC7C;AACA,MAAI,UAAU,MAAM;AAChB,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,OAAO,IAAI,KAAK;AACrB,QAAI,UAAU,KAAK;AACf,YAAM,MAAM,UAAU;AAAA,IAC1B;AACA,QAAI,UAAU,QAAQ;AAClB,YAAM,MAAM,UAAU;AAAA,IAC1B;AACA,aAAS,UAAU,MAAM,IAAI,IAAI,IAAI,EAAE;AAAA,EAC3C;AACA,MAAI,UAAU,OAAO;AACjB,SAAK,OAAO,IAAI,KAAK;AACrB,SAAK,OAAO;AACZ,SAAK,OAAO,IAAI,KAAK;AACrB,SAAK,OAAO,IAAI,KAAK;AACrB,QAAI,UAAU,KAAK;AACf,YAAM,MAAM,UAAU;AAAA,IAC1B;AACA,QAAI,UAAU,QAAQ;AAClB,YAAM,MAAM,UAAU;AAAA,IAC1B;AACA,aAAS,UAAU,OAAO,IAAI,IAAI,IAAI,EAAE;AAAA,EAC5C;AACA,WAAS,SAAS,OAAOS,KAAIC,KAAIC,KAAIC,KAAI;AACrC,QAAI,YAAY,EAAE,aAAa,KAAK;AACpC,QAAI,YAAY,EAAE,KAAKH,KAAIC,KAAIC,KAAIC,KAAI,GAAG;AAAA,EAC9C;AACJ;AACA,SAAS,sBAAsB,KAAK,OAAO,WAAW,QAAQ;AAC1D,MAAI,sBAAsB,MAAM,SAAS,OAAO;AAChD,MAAI,WAAW,MAAM,SAAS;AAC9B,MAAI,aAAa,eAAgB,aAAa,cAAc,WAAY;AACpE,2BAAuB,MAAM,cAAc,MAAM,OAAO;AAAA,EAC5D;AACA,SAAO,IAAI,SAAS,EAAE,SAAS,OAAO,IAAI;AAC9C;AACA,SAAS,QAAQ,KAAK,OAAO,UAAU,QAAQ,SAAS,gBAAgB;AACpE,MAAI,YAAY,QAAQ;AAAE,cAAU,CAAC;AAAA,EAAG;AACxC,MAAI,mBAAmB,QAAQ;AAAE,qBAAiB;AAAA,EAAO;AACzD,MAAI,YAAY,IAAI,UAAU;AAC9B,MAAI,MAAM,SAAS,aAAa,eAAe,CAAC,gBAAgB;AAC5D,UAAM,KAAK,QAAQ,SAAU,KAAK;AAAE,aAAO,SAAS,KAAK,OAAO,KAAK,QAAQ,OAAO;AAAA,IAAG,CAAC;AAAA,EAC5F;AAGA,QAAM,iBAAiB,KAAK,MAAM;AAClC,MAAI,SAAS,MAAM,SAAS;AAC5B,iBAAe,KAAK,OAAO,UAAU,MAAM;AAC3C,WAAS,GAAG;AACZ,QAAM;AACN,SAAO,IAAI,OAAO;AAClB,SAAO,IAAI,OAAO;AAClB,WAAS,IAAI,OAAO;AAEpB,QAAM,sBAAsB,KAAK,MAAM;AACvC,MAAI,MAAM,SAAS,aAAa,aAAa;AACzC,UAAM,KAAK,QAAQ,SAAU,KAAK;AAAE,aAAO,SAAS,KAAK,OAAO,KAAK,QAAQ,OAAO;AAAA,IAAG,CAAC;AACxF,QAAI,YAAY,IAAI,UAAU;AAAA,EAClC;AACJ;AACA,SAAS,SAAS,KAAK;AACnB,MAAI,UAAU,IAAI,WAAW;AAC7B,MAAI,QAAQ,UAAU,CAAC;AACvB,MAAI,aAAa,IAAI,WAAW;AAChC,MAAI,eAAe,SAAS;AACxB,QAAI,QAAQ;AACZ,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAEA,SAAS,YAAY,OAAO;AAExB,QAAM,IAAI,YAAY,WAAY;AAC9B,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,WAAK,EAAE,IAAI,UAAU,EAAE;AAAA,IAC3B;AACA,QAAI,UAAU,KAAK,CAAC;AACpB,QAAI,QAAQ,WAAW,MAAM,OAAO;AACpC,QAAI,QAAQ,YAAY,MAAM,KAAK;AACnC,cAAU,MAAM,KAAK;AACrB,WAAO;AAAA,EACX;AAEA,QAAM,IAAI,gBAAgB;AAC1B,QAAM,IAAI,gBAAgB,SAAU,MAAM,GAAG,GAAG,QAAQ;AACpD,kBAAc,MAAM,GAAG,GAAG,QAAQ,IAAI;AAAA,EAC1C;AACA,QAAM,IAAI,uBAAuB,SAAU,UAAU;AACjD,eAAW,YAAY,UAAU,IAAI;AACrC,WAAO;AAAA,EACX;AACA,QAAM,uBAAuB,SAAU,UAAU,KAAK;AAClD,eAAW,YAAY,UAAU,GAAG;AAAA,EACxC;AACA,QAAM,IAAI,sBAAsB,SAAU,WAAW,uBAAuB;AACxE,QAAId;AACJ,QAAI,0BAA0B,QAAQ;AAAE,8BAAwB;AAAA,IAAO;AACvE,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,MAAM,2DAA2D;AACzE,aAAO;AAAA,IACX;AACA,QAAI,MAAM,IAAI,WAAW,IAAI;AAC7B,QAAI,KAAK,UAAU,KAAK,WAAW,QAAQ,uBAAuB,KAAK,GAAG,OAAO,GAAG,MAAM,OAAO,GAAG;AACpG,QAAI,YAAYA,MAAK,KAAK,CAAC,OAAO,QAAQA,QAAO,SAAS,SAASA,IAAG,IAAI,SAAU,GAAG;AAAE,aAAO,EAAE;AAAA,IAAS,CAAC,MAAM,CAAC;AACnH,WAAO,EAAE,SAAkB,MAAM,MAAM,MAAM,KAAK;AAAA,EACtD;AACJ;AAEA,IAAI;AACJ,SAAS,UAAU,GAAG,SAAS;AAC3B,MAAI,QAAQ,WAAW,GAAG,OAAO;AACjC,MAAI,QAAQ,YAAY,GAAG,KAAK;AAChC,YAAU,GAAG,KAAK;AACtB;AAEA,SAAS,cAAc,GAAG,SAAS;AAC/B,MAAI,QAAQ,WAAW,GAAG,OAAO;AACjC,SAAO,YAAY,GAAG,KAAK;AAC/B;AACA,SAAS,YAAY,GAAG,OAAO;AAC3B,YAAU,GAAG,KAAK;AACtB;AACA,IAAI;AACA,MAAI,OAAO,WAAW,eAAe,QAAQ;AAErC,gBAAY;AACZ,YAAQ,UAAU,WAAW,KAAK,UAAU,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG;AAC/F,QAAI,OAAO;AACP,kBAAY,KAAK;AAAA,IACrB;AAAA,EACJ;AACJ,SACO,OAAO;AACV,UAAQ,MAAM,oCAAoC,KAAK;AAC3D;AATY;AACA;", "names": ["<PERSON><PERSON><PERSON><PERSON>", "_a", "d", "b", "HtmlRowInput", "text", "_a", "window", "cellStyles", "prop", "HookData", "CellHookData", "Table", "Row", "Cell", "Column", "column", "x1", "y1", "x2", "y2"]}