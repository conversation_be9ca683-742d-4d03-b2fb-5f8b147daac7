<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentMethod extends Model
{
    use HasFactory;

    protected $fillable = [
        'type',
        'reference_number',
        'refer_type',
        'refer_id',
        'refer_name',
        'total',
        'payment_type',
        'cheque_no',
        'bank_name',
        'bank',
        'issue_date',
        'settled_amount',
        'balance_amount',
        'date',
    ];

    protected $casts = [
        'total' => 'decimal:2',
        'settled_amount' => 'decimal:2',
        'balance_amount' => 'decimal:2',
        'issue_date' => 'date',
        'date' => 'date',
    ];

    /**
     * Get the customer that owns the payment method.
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class, 'refer_id')->where('refer_type', 'Customer');
    }

    /**
     * Get the supplier that owns the payment method.
     */
    public function supplier()
    {
        return $this->belongsTo(Supplier::class, 'refer_id')->where('refer_type', 'Supplier');
    }

    /**
     * Get the bank account details based on the bank field format (type-id)
     */
    public function getBankAccountAttribute()
    {
        if (!$this->bank) {
            return null;
        }

        $bankParts = explode('-', $this->bank);
        if (count($bankParts) !== 2) {
            return null;
        }

        $bankType = $bankParts[0]; // 'staff_ledger' or 'sub_group'
        $bankId = $bankParts[1];

        if ($bankType === 'staff_ledger') {
            return StaffLedger::find($bankId);
        } elseif ($bankType === 'sub_group') {
            return AccountSubGroup::find($bankId);
        }

        return null;
    }

    /**
     * Get the bank account name
     */
    public function getBankAccountNameAttribute()
    {
        $bankAccount = $this->bank_account;
        if (!$bankAccount) {
            return null;
        }

        if ($bankAccount instanceof StaffLedger) {
            return $bankAccount->name;
        } elseif ($bankAccount instanceof AccountSubGroup) {
            return $bankAccount->sub_group_name;
        }

        return null;
    }
}
