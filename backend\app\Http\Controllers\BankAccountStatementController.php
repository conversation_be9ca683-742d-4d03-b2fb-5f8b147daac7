<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\StaffLedger;
use App\Models\PaymentMethod;
use App\Models\Payment;
use App\Models\AccountSubGroup;
use App\Models\ChequeStatement;
use App\Models\SalesReturn;
use App\Models\SalesReturnItem;
use App\Models\PurchaseReturn;
use App\Models\PurchaseReturnItem;
use Carbon\Carbon;

class BankAccountStatementController extends Controller
{
    public function getStatement(Request $request)
    {
        $fromDate = $request->input('from_date') ? Carbon::parse($request->input('from_date'))->startOfDay() : Carbon::now()->startOfYear();
        $toDate = $request->input('to_date') ? Carbon::parse($request->input('to_date'))->endOfDay() : Carbon::now()->endOfDay();

        // Opening balance for all bank accounts and its subgroups
        $subGroups = AccountSubGroup::where('main_group', 'Bank Accounts')->pluck('sub_group_name')->toArray();
        $groups = array_merge(['Bank Accounts'], $subGroups);
        $openingBalance = StaffLedger::whereIn('account_group', $groups)->sum('opening_balance');

        // INCOME SECTION
        $income = collect();

        

        // 2. Sales/Invoice (cheque, online, card)
        $incomeSales = PaymentMethod::whereIn('type', ['sales', 'invoice'])
            ->whereIn('payment_type', ['cheque', 'online', 'card'])
            ->whereBetween('date', [$fromDate, $toDate])
            ->get()
            ->map(function($t) {
                return [
                    'date' => $t->date ? $t->date->format('Y-m-d') : null,
                    'type' => ucfirst($t->type),
                    'description' => $t->type . ' #' . ($t->reference_number ?? ''),
                    'amount' => (float) $t->settled_amount,
                ];
            });
        $income = $income->concat($incomeSales);

        // 3. Receive Voucher (cheque, online, card)
        $incomeReceiveVouchers = Payment::whereIn('payment_method', ['cheque', 'online', 'card'])
            ->where('voucher_no', 'like', 'REC-%')
            ->whereBetween('payment_date', [$fromDate, $toDate])
            ->get()
            ->map(function($t) {
                return [
                    'date' => $t->payment_date ? $t->payment_date->format('Y-m-d') : null,
                    'type' => 'Receive Voucher',
                    'description' => 'Receive Voucher #' . ($t->voucher_no ?? ''),
                    'amount' => (float) $t->amount,
                ];
            });
        $income = $income->concat($incomeReceiveVouchers);

        // PURCHASE RETURN (bank, approved) as INCOME
        $purchaseReturns = PurchaseReturn::where('refund_method', 'bank')
            ->where('status', 'approved')
            ->whereBetween('created_at', [$fromDate, $toDate])
            ->get();
        foreach ($purchaseReturns as $return) {
            $amount = PurchaseReturnItem::where('purchase_return_id', $return->id)
                ->get()
                ->sum(function($item) { return $item->quantity * $item->buying_cost; });
            if ($amount > 0) {
                $income->push([
                    'date' => $return->created_at ? $return->created_at->format('Y-m-d') : null,
                    'type' => 'Purchase Return',
                    'description' => 'Purchase Return #' . ($return->invoice_number ?? ''),
                    'amount' => (float) $amount,
                ]);
            }
        }

        // EXPENSE SECTION
        $expense = collect();

        // 1. Purchase (cheque, online, card)
        $expensePurchases = PaymentMethod::where('type', 'purchase')
            ->whereIn('payment_type', ['cheque', 'online', 'card'])
            ->whereBetween('date', [$fromDate, $toDate])
            ->get()
            ->map(function($t) {
                return [
                    'date' => $t->date ? $t->date->format('Y-m-d') : null,
                    'type' => 'Purchase',
                    'description' => 'Purchase #' . ($t->reference_number ?? ''),
                    'amount' => (float) $t->settled_amount,
                ];
            });
        $expense = $expense->concat($expensePurchases);

        // 2. Payment Voucher (card)
        $expensePaymentVouchers = Payment::where('payment_method', ['cheque', 'online', 'card'])
            ->where('voucher_no', 'like', 'PAY-%')
            ->whereBetween('payment_date', [$fromDate, $toDate])
            ->get()
            ->map(function($t) {
                return [
                    'date' => $t->payment_date ? $t->payment_date->format('Y-m-d') : null,
                    'type' => 'Payment Voucher',
                    'description' => 'Payment Voucher #' . ($t->voucher_no ?? ''),
                    'amount' => (float) $t->amount,
                ];
            });
        $expense = $expense->concat($expensePaymentVouchers);

        // SALES RETURN (card, approved) as EXPENSE
        $salesReturns = SalesReturn::where('refund_method', 'card')
            ->where('status', 'approved')
            ->whereBetween('created_at', [$fromDate, $toDate])
            ->get();
        foreach ($salesReturns as $return) {
            $amount = SalesReturnItem::where('sales_return_id', $return->id)
                ->get()
                ->sum(function($item) { return $item->quantity * $item->selling_cost; });
            if ($amount > 0) {
                $expense->push([
                    'date' => $return->created_at ? $return->created_at->format('Y-m-d') : null,
                    'type' => 'Sales Return',
                    'description' => 'Sales Return #' . ($return->sales_return_number ?? ''),
                    'amount' => (float) $amount,
                ]);
            }
        }

        // DECLINED CHEQUES
        $declinedCheques = ChequeStatement::where('status', 'declined')
            ->whereBetween('payment_date', [$fromDate, $toDate])
            ->get();
        foreach ($declinedCheques as $cheque) {
            $entry = [
                'date' => $cheque->payment_date ? $cheque->payment_date->format('Y-m-d') : null,
                'type' => 'Declined Cheque',
                'description' => 'Declined Cheque #' . ($cheque->cheque_no ?? ''),
                'amount' => (float) $cheque->amount,
            ];
            // Logic for which section to add
            if (in_array($cheque->transaction_type, ['sales', 'invoice'])) {
                // Declined cheque from sales/invoice is expense
                $expense->push($entry);
            } elseif ($cheque->transaction_type === 'purchase') {
                // Declined cheque from purchase is income
                $income->push($entry);
            } elseif ($cheque->transaction_type === 'ledger') {
                // For payment voucher (ledger, cheque declined): income
                if (strpos(strtolower($cheque->voucher_no), 'pay-') === 0) {
                    $income->push($entry);
                }
                // For receive voucher (ledger, cheque declined): expense
                if (strpos(strtolower($cheque->voucher_no), 'rec-') === 0) {
                    $expense->push($entry);
                }
            }
        }

        $income = $income->sortBy('date')->values();
        $expense = $expense->sortBy('date')->values();

        $totalIncome = $income->sum('amount');
        $totalExpense = $expense->sum('amount');
        $balance = $openingBalance + $totalIncome - $totalExpense;

        return response()->json([
            'success' => true,
            'opening_balance' => $openingBalance,
            'income' => $income,
            'expense' => $expense,
            'total_income' => $totalIncome,
            'total_expense' => $totalExpense,
            'balance' => $balance,
        ]);
    }
} 