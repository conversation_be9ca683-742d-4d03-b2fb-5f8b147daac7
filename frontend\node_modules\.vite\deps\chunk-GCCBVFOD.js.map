{"version": 3, "sources": ["../../@floating-ui/utils/dist/floating-ui.utils.mjs", "../../@floating-ui/core/dist/floating-ui.core.mjs", "../../@floating-ui/utils/dist/floating-ui.utils.dom.mjs", "../../@floating-ui/dom/dist/floating-ui.dom.mjs"], "sourcesContent": ["/**\r\n * Custom positioning reference element.\r\n * @see https://floating-ui.com/docs/virtual-elements\r\n */\r\n\r\nconst sides = ['top', 'right', 'bottom', 'left'];\r\nconst alignments = ['start', 'end'];\r\nconst placements = /*#__PURE__*/sides.reduce((acc, side) => acc.concat(side, side + \"-\" + alignments[0], side + \"-\" + alignments[1]), []);\r\nconst min = Math.min;\r\nconst max = Math.max;\r\nconst round = Math.round;\r\nconst floor = Math.floor;\r\nconst createCoords = v => ({\r\n  x: v,\r\n  y: v\r\n});\r\nconst oppositeSideMap = {\r\n  left: 'right',\r\n  right: 'left',\r\n  bottom: 'top',\r\n  top: 'bottom'\r\n};\r\nconst oppositeAlignmentMap = {\r\n  start: 'end',\r\n  end: 'start'\r\n};\r\nfunction clamp(start, value, end) {\r\n  return max(start, min(value, end));\r\n}\r\nfunction evaluate(value, param) {\r\n  return typeof value === 'function' ? value(param) : value;\r\n}\r\nfunction getSide(placement) {\r\n  return placement.split('-')[0];\r\n}\r\nfunction getAlignment(placement) {\r\n  return placement.split('-')[1];\r\n}\r\nfunction getOppositeAxis(axis) {\r\n  return axis === 'x' ? 'y' : 'x';\r\n}\r\nfunction getAxisLength(axis) {\r\n  return axis === 'y' ? 'height' : 'width';\r\n}\r\nfunction getSideAxis(placement) {\r\n  return ['top', 'bottom'].includes(getSide(placement)) ? 'y' : 'x';\r\n}\r\nfunction getAlignmentAxis(placement) {\r\n  return getOppositeAxis(getSideAxis(placement));\r\n}\r\nfunction getAlignmentSides(placement, rects, rtl) {\r\n  if (rtl === void 0) {\r\n    rtl = false;\r\n  }\r\n  const alignment = getAlignment(placement);\r\n  const alignmentAxis = getAlignmentAxis(placement);\r\n  const length = getAxisLength(alignmentAxis);\r\n  let mainAlignmentSide = alignmentAxis === 'x' ? alignment === (rtl ? 'end' : 'start') ? 'right' : 'left' : alignment === 'start' ? 'bottom' : 'top';\r\n  if (rects.reference[length] > rects.floating[length]) {\r\n    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);\r\n  }\r\n  return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];\r\n}\r\nfunction getExpandedPlacements(placement) {\r\n  const oppositePlacement = getOppositePlacement(placement);\r\n  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];\r\n}\r\nfunction getOppositeAlignmentPlacement(placement) {\r\n  return placement.replace(/start|end/g, alignment => oppositeAlignmentMap[alignment]);\r\n}\r\nfunction getSideList(side, isStart, rtl) {\r\n  const lr = ['left', 'right'];\r\n  const rl = ['right', 'left'];\r\n  const tb = ['top', 'bottom'];\r\n  const bt = ['bottom', 'top'];\r\n  switch (side) {\r\n    case 'top':\r\n    case 'bottom':\r\n      if (rtl) return isStart ? rl : lr;\r\n      return isStart ? lr : rl;\r\n    case 'left':\r\n    case 'right':\r\n      return isStart ? tb : bt;\r\n    default:\r\n      return [];\r\n  }\r\n}\r\nfunction getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {\r\n  const alignment = getAlignment(placement);\r\n  let list = getSideList(getSide(placement), direction === 'start', rtl);\r\n  if (alignment) {\r\n    list = list.map(side => side + \"-\" + alignment);\r\n    if (flipAlignment) {\r\n      list = list.concat(list.map(getOppositeAlignmentPlacement));\r\n    }\r\n  }\r\n  return list;\r\n}\r\nfunction getOppositePlacement(placement) {\r\n  return placement.replace(/left|right|bottom|top/g, side => oppositeSideMap[side]);\r\n}\r\nfunction expandPaddingObject(padding) {\r\n  return {\r\n    top: 0,\r\n    right: 0,\r\n    bottom: 0,\r\n    left: 0,\r\n    ...padding\r\n  };\r\n}\r\nfunction getPaddingObject(padding) {\r\n  return typeof padding !== 'number' ? expandPaddingObject(padding) : {\r\n    top: padding,\r\n    right: padding,\r\n    bottom: padding,\r\n    left: padding\r\n  };\r\n}\r\nfunction rectToClientRect(rect) {\r\n  const {\r\n    x,\r\n    y,\r\n    width,\r\n    height\r\n  } = rect;\r\n  return {\r\n    width,\r\n    height,\r\n    top: y,\r\n    left: x,\r\n    right: x + width,\r\n    bottom: y + height,\r\n    x,\r\n    y\r\n  };\r\n}\r\n\r\nexport { alignments, clamp, createCoords, evaluate, expandPaddingObject, floor, getAlignment, getAlignmentAxis, getAlignmentSides, getAxisLength, getExpandedPlacements, getOppositeAlignmentPlacement, getOppositeAxis, getOppositeAxisPlacements, getOppositePlacement, getPaddingObject, getSide, getSideAxis, max, min, placements, rectToClientRect, round, sides };\r\n", "import { getSideAxis, getAlignmentAxis, getAxisLength, getSide, getAlignment, evaluate, getPaddingObject, rectToClientRect, min, clamp, placements, getAlignmentSides, getOppositeAlignmentPlacement, getOppositePlacement, getExpandedPlacements, getOppositeAxisPlacements, sides, max, getOppositeAxis } from '@floating-ui/utils';\r\nexport { rectToClientRect } from '@floating-ui/utils';\r\n\r\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\r\n  let {\r\n    reference,\r\n    floating\r\n  } = _ref;\r\n  const sideAxis = getSideAxis(placement);\r\n  const alignmentAxis = getAlignmentAxis(placement);\r\n  const alignLength = getAxisLength(alignmentAxis);\r\n  const side = getSide(placement);\r\n  const isVertical = sideAxis === 'y';\r\n  const commonX = reference.x + reference.width / 2 - floating.width / 2;\r\n  const commonY = reference.y + reference.height / 2 - floating.height / 2;\r\n  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\r\n  let coords;\r\n  switch (side) {\r\n    case 'top':\r\n      coords = {\r\n        x: commonX,\r\n        y: reference.y - floating.height\r\n      };\r\n      break;\r\n    case 'bottom':\r\n      coords = {\r\n        x: commonX,\r\n        y: reference.y + reference.height\r\n      };\r\n      break;\r\n    case 'right':\r\n      coords = {\r\n        x: reference.x + reference.width,\r\n        y: commonY\r\n      };\r\n      break;\r\n    case 'left':\r\n      coords = {\r\n        x: reference.x - floating.width,\r\n        y: commonY\r\n      };\r\n      break;\r\n    default:\r\n      coords = {\r\n        x: reference.x,\r\n        y: reference.y\r\n      };\r\n  }\r\n  switch (getAlignment(placement)) {\r\n    case 'start':\r\n      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\r\n      break;\r\n    case 'end':\r\n      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\r\n      break;\r\n  }\r\n  return coords;\r\n}\r\n\r\n/**\r\n * Computes the `x` and `y` coordinates that will place the floating element\r\n * next to a given reference element.\r\n *\r\n * This export does not have any `platform` interface logic. You will need to\r\n * write one for the platform you are using Floating UI with.\r\n */\r\nconst computePosition = async (reference, floating, config) => {\r\n  const {\r\n    placement = 'bottom',\r\n    strategy = 'absolute',\r\n    middleware = [],\r\n    platform\r\n  } = config;\r\n  const validMiddleware = middleware.filter(Boolean);\r\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));\r\n  let rects = await platform.getElementRects({\r\n    reference,\r\n    floating,\r\n    strategy\r\n  });\r\n  let {\r\n    x,\r\n    y\r\n  } = computeCoordsFromPlacement(rects, placement, rtl);\r\n  let statefulPlacement = placement;\r\n  let middlewareData = {};\r\n  let resetCount = 0;\r\n  for (let i = 0; i < validMiddleware.length; i++) {\r\n    const {\r\n      name,\r\n      fn\r\n    } = validMiddleware[i];\r\n    const {\r\n      x: nextX,\r\n      y: nextY,\r\n      data,\r\n      reset\r\n    } = await fn({\r\n      x,\r\n      y,\r\n      initialPlacement: placement,\r\n      placement: statefulPlacement,\r\n      strategy,\r\n      middlewareData,\r\n      rects,\r\n      platform,\r\n      elements: {\r\n        reference,\r\n        floating\r\n      }\r\n    });\r\n    x = nextX != null ? nextX : x;\r\n    y = nextY != null ? nextY : y;\r\n    middlewareData = {\r\n      ...middlewareData,\r\n      [name]: {\r\n        ...middlewareData[name],\r\n        ...data\r\n      }\r\n    };\r\n    if (reset && resetCount <= 50) {\r\n      resetCount++;\r\n      if (typeof reset === 'object') {\r\n        if (reset.placement) {\r\n          statefulPlacement = reset.placement;\r\n        }\r\n        if (reset.rects) {\r\n          rects = reset.rects === true ? await platform.getElementRects({\r\n            reference,\r\n            floating,\r\n            strategy\r\n          }) : reset.rects;\r\n        }\r\n        ({\r\n          x,\r\n          y\r\n        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\r\n      }\r\n      i = -1;\r\n    }\r\n  }\r\n  return {\r\n    x,\r\n    y,\r\n    placement: statefulPlacement,\r\n    strategy,\r\n    middlewareData\r\n  };\r\n};\r\n\r\n/**\r\n * Resolves with an object of overflow side offsets that determine how much the\r\n * element is overflowing a given clipping boundary on each side.\r\n * - positive = overflowing the boundary by that number of pixels\r\n * - negative = how many pixels left before it will overflow\r\n * - 0 = lies flush with the boundary\r\n * @see https://floating-ui.com/docs/detectOverflow\r\n */\r\nasync function detectOverflow(state, options) {\r\n  var _await$platform$isEle;\r\n  if (options === void 0) {\r\n    options = {};\r\n  }\r\n  const {\r\n    x,\r\n    y,\r\n    platform,\r\n    rects,\r\n    elements,\r\n    strategy\r\n  } = state;\r\n  const {\r\n    boundary = 'clippingAncestors',\r\n    rootBoundary = 'viewport',\r\n    elementContext = 'floating',\r\n    altBoundary = false,\r\n    padding = 0\r\n  } = evaluate(options, state);\r\n  const paddingObject = getPaddingObject(padding);\r\n  const altContext = elementContext === 'floating' ? 'reference' : 'floating';\r\n  const element = elements[altBoundary ? altContext : elementContext];\r\n  const clippingClientRect = rectToClientRect(await platform.getClippingRect({\r\n    element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || (await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating))),\r\n    boundary,\r\n    rootBoundary,\r\n    strategy\r\n  }));\r\n  const rect = elementContext === 'floating' ? {\r\n    x,\r\n    y,\r\n    width: rects.floating.width,\r\n    height: rects.floating.height\r\n  } : rects.reference;\r\n  const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));\r\n  const offsetScale = (await (platform.isElement == null ? void 0 : platform.isElement(offsetParent))) ? (await (platform.getScale == null ? void 0 : platform.getScale(offsetParent))) || {\r\n    x: 1,\r\n    y: 1\r\n  } : {\r\n    x: 1,\r\n    y: 1\r\n  };\r\n  const elementClientRect = rectToClientRect(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({\r\n    elements,\r\n    rect,\r\n    offsetParent,\r\n    strategy\r\n  }) : rect);\r\n  return {\r\n    top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\r\n    bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\r\n    left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\r\n    right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\r\n  };\r\n}\r\n\r\n/**\r\n * Provides data to position an inner element of the floating element so that it\r\n * appears centered to the reference element.\r\n * @see https://floating-ui.com/docs/arrow\r\n */\r\nconst arrow = options => ({\r\n  name: 'arrow',\r\n  options,\r\n  async fn(state) {\r\n    const {\r\n      x,\r\n      y,\r\n      placement,\r\n      rects,\r\n      platform,\r\n      elements,\r\n      middlewareData\r\n    } = state;\r\n    // Since `element` is required, we don't Partial<> the type.\r\n    const {\r\n      element,\r\n      padding = 0\r\n    } = evaluate(options, state) || {};\r\n    if (element == null) {\r\n      return {};\r\n    }\r\n    const paddingObject = getPaddingObject(padding);\r\n    const coords = {\r\n      x,\r\n      y\r\n    };\r\n    const axis = getAlignmentAxis(placement);\r\n    const length = getAxisLength(axis);\r\n    const arrowDimensions = await platform.getDimensions(element);\r\n    const isYAxis = axis === 'y';\r\n    const minProp = isYAxis ? 'top' : 'left';\r\n    const maxProp = isYAxis ? 'bottom' : 'right';\r\n    const clientProp = isYAxis ? 'clientHeight' : 'clientWidth';\r\n    const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\r\n    const startDiff = coords[axis] - rects.reference[axis];\r\n    const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));\r\n    let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;\r\n\r\n    // DOM platform can return `window` as the `offsetParent`.\r\n    if (!clientSize || !(await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent)))) {\r\n      clientSize = elements.floating[clientProp] || rects.floating[length];\r\n    }\r\n    const centerToReference = endDiff / 2 - startDiff / 2;\r\n\r\n    // If the padding is large enough that it causes the arrow to no longer be\r\n    // centered, modify the padding so that it is centered.\r\n    const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\r\n    const minPadding = min(paddingObject[minProp], largestPossiblePadding);\r\n    const maxPadding = min(paddingObject[maxProp], largestPossiblePadding);\r\n\r\n    // Make sure the arrow doesn't overflow the floating element if the center\r\n    // point is outside the floating element's bounds.\r\n    const min$1 = minPadding;\r\n    const max = clientSize - arrowDimensions[length] - maxPadding;\r\n    const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\r\n    const offset = clamp(min$1, center, max);\r\n\r\n    // If the reference is small enough that the arrow's padding causes it to\r\n    // to point to nothing for an aligned placement, adjust the offset of the\r\n    // floating element itself. To ensure `shift()` continues to take action,\r\n    // a single reset is performed when this is true.\r\n    const shouldAddOffset = !middlewareData.arrow && getAlignment(placement) != null && center !== offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\r\n    const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max : 0;\r\n    return {\r\n      [axis]: coords[axis] + alignmentOffset,\r\n      data: {\r\n        [axis]: offset,\r\n        centerOffset: center - offset - alignmentOffset,\r\n        ...(shouldAddOffset && {\r\n          alignmentOffset\r\n        })\r\n      },\r\n      reset: shouldAddOffset\r\n    };\r\n  }\r\n});\r\n\r\nfunction getPlacementList(alignment, autoAlignment, allowedPlacements) {\r\n  const allowedPlacementsSortedByAlignment = alignment ? [...allowedPlacements.filter(placement => getAlignment(placement) === alignment), ...allowedPlacements.filter(placement => getAlignment(placement) !== alignment)] : allowedPlacements.filter(placement => getSide(placement) === placement);\r\n  return allowedPlacementsSortedByAlignment.filter(placement => {\r\n    if (alignment) {\r\n      return getAlignment(placement) === alignment || (autoAlignment ? getOppositeAlignmentPlacement(placement) !== placement : false);\r\n    }\r\n    return true;\r\n  });\r\n}\r\n/**\r\n * Optimizes the visibility of the floating element by choosing the placement\r\n * that has the most space available automatically, without needing to specify a\r\n * preferred placement. Alternative to `flip`.\r\n * @see https://floating-ui.com/docs/autoPlacement\r\n */\r\nconst autoPlacement = function (options) {\r\n  if (options === void 0) {\r\n    options = {};\r\n  }\r\n  return {\r\n    name: 'autoPlacement',\r\n    options,\r\n    async fn(state) {\r\n      var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE;\r\n      const {\r\n        rects,\r\n        middlewareData,\r\n        placement,\r\n        platform,\r\n        elements\r\n      } = state;\r\n      const {\r\n        crossAxis = false,\r\n        alignment,\r\n        allowedPlacements = placements,\r\n        autoAlignment = true,\r\n        ...detectOverflowOptions\r\n      } = evaluate(options, state);\r\n      const placements$1 = alignment !== undefined || allowedPlacements === placements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;\r\n      const overflow = await detectOverflow(state, detectOverflowOptions);\r\n      const currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;\r\n      const currentPlacement = placements$1[currentIndex];\r\n      if (currentPlacement == null) {\r\n        return {};\r\n      }\r\n      const alignmentSides = getAlignmentSides(currentPlacement, rects, await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)));\r\n\r\n      // Make `computeCoords` start from the right place.\r\n      if (placement !== currentPlacement) {\r\n        return {\r\n          reset: {\r\n            placement: placements$1[0]\r\n          }\r\n        };\r\n      }\r\n      const currentOverflows = [overflow[getSide(currentPlacement)], overflow[alignmentSides[0]], overflow[alignmentSides[1]]];\r\n      const allOverflows = [...(((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || []), {\r\n        placement: currentPlacement,\r\n        overflows: currentOverflows\r\n      }];\r\n      const nextPlacement = placements$1[currentIndex + 1];\r\n\r\n      // There are more placements to check.\r\n      if (nextPlacement) {\r\n        return {\r\n          data: {\r\n            index: currentIndex + 1,\r\n            overflows: allOverflows\r\n          },\r\n          reset: {\r\n            placement: nextPlacement\r\n          }\r\n        };\r\n      }\r\n      const placementsSortedByMostSpace = allOverflows.map(d => {\r\n        const alignment = getAlignment(d.placement);\r\n        return [d.placement, alignment && crossAxis ?\r\n        // Check along the mainAxis and main crossAxis side.\r\n        d.overflows.slice(0, 2).reduce((acc, v) => acc + v, 0) :\r\n        // Check only the mainAxis.\r\n        d.overflows[0], d.overflows];\r\n      }).sort((a, b) => a[1] - b[1]);\r\n      const placementsThatFitOnEachSide = placementsSortedByMostSpace.filter(d => d[2].slice(0,\r\n      // Aligned placements should not check their opposite crossAxis\r\n      // side.\r\n      getAlignment(d[0]) ? 2 : 3).every(v => v <= 0));\r\n      const resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];\r\n      if (resetPlacement !== placement) {\r\n        return {\r\n          data: {\r\n            index: currentIndex + 1,\r\n            overflows: allOverflows\r\n          },\r\n          reset: {\r\n            placement: resetPlacement\r\n          }\r\n        };\r\n      }\r\n      return {};\r\n    }\r\n  };\r\n};\r\n\r\n/**\r\n * Optimizes the visibility of the floating element by flipping the `placement`\r\n * in order to keep it in view when the preferred placement(s) will overflow the\r\n * clipping boundary. Alternative to `autoPlacement`.\r\n * @see https://floating-ui.com/docs/flip\r\n */\r\nconst flip = function (options) {\r\n  if (options === void 0) {\r\n    options = {};\r\n  }\r\n  return {\r\n    name: 'flip',\r\n    options,\r\n    async fn(state) {\r\n      var _middlewareData$arrow, _middlewareData$flip;\r\n      const {\r\n        placement,\r\n        middlewareData,\r\n        rects,\r\n        initialPlacement,\r\n        platform,\r\n        elements\r\n      } = state;\r\n      const {\r\n        mainAxis: checkMainAxis = true,\r\n        crossAxis: checkCrossAxis = true,\r\n        fallbackPlacements: specifiedFallbackPlacements,\r\n        fallbackStrategy = 'bestFit',\r\n        fallbackAxisSideDirection = 'none',\r\n        flipAlignment = true,\r\n        ...detectOverflowOptions\r\n      } = evaluate(options, state);\r\n\r\n      // If a reset by the arrow was caused due to an alignment offset being\r\n      // added, we should skip any logic now since `flip()` has already done its\r\n      // work.\r\n      // https://github.com/floating-ui/floating-ui/issues/2549#issuecomment-1719601643\r\n      if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\r\n        return {};\r\n      }\r\n      const side = getSide(placement);\r\n      const initialSideAxis = getSideAxis(initialPlacement);\r\n      const isBasePlacement = getSide(initialPlacement) === initialPlacement;\r\n      const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\r\n      const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));\r\n      const hasFallbackAxisSideDirection = fallbackAxisSideDirection !== 'none';\r\n      if (!specifiedFallbackPlacements && hasFallbackAxisSideDirection) {\r\n        fallbackPlacements.push(...getOppositeAxisPlacements(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));\r\n      }\r\n      const placements = [initialPlacement, ...fallbackPlacements];\r\n      const overflow = await detectOverflow(state, detectOverflowOptions);\r\n      const overflows = [];\r\n      let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\r\n      if (checkMainAxis) {\r\n        overflows.push(overflow[side]);\r\n      }\r\n      if (checkCrossAxis) {\r\n        const sides = getAlignmentSides(placement, rects, rtl);\r\n        overflows.push(overflow[sides[0]], overflow[sides[1]]);\r\n      }\r\n      overflowsData = [...overflowsData, {\r\n        placement,\r\n        overflows\r\n      }];\r\n\r\n      // One or more sides is overflowing.\r\n      if (!overflows.every(side => side <= 0)) {\r\n        var _middlewareData$flip2, _overflowsData$filter;\r\n        const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\r\n        const nextPlacement = placements[nextIndex];\r\n        if (nextPlacement) {\r\n          // Try next placement and re-run the lifecycle.\r\n          return {\r\n            data: {\r\n              index: nextIndex,\r\n              overflows: overflowsData\r\n            },\r\n            reset: {\r\n              placement: nextPlacement\r\n            }\r\n          };\r\n        }\r\n\r\n        // First, find the candidates that fit on the mainAxis side of overflow,\r\n        // then find the placement that fits the best on the main crossAxis side.\r\n        let resetPlacement = (_overflowsData$filter = overflowsData.filter(d => d.overflows[0] <= 0).sort((a, b) => a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;\r\n\r\n        // Otherwise fallback.\r\n        if (!resetPlacement) {\r\n          switch (fallbackStrategy) {\r\n            case 'bestFit':\r\n              {\r\n                var _overflowsData$filter2;\r\n                const placement = (_overflowsData$filter2 = overflowsData.filter(d => {\r\n                  if (hasFallbackAxisSideDirection) {\r\n                    const currentSideAxis = getSideAxis(d.placement);\r\n                    return currentSideAxis === initialSideAxis ||\r\n                    // Create a bias to the `y` side axis due to horizontal\r\n                    // reading directions favoring greater width.\r\n                    currentSideAxis === 'y';\r\n                  }\r\n                  return true;\r\n                }).map(d => [d.placement, d.overflows.filter(overflow => overflow > 0).reduce((acc, overflow) => acc + overflow, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$filter2[0];\r\n                if (placement) {\r\n                  resetPlacement = placement;\r\n                }\r\n                break;\r\n              }\r\n            case 'initialPlacement':\r\n              resetPlacement = initialPlacement;\r\n              break;\r\n          }\r\n        }\r\n        if (placement !== resetPlacement) {\r\n          return {\r\n            reset: {\r\n              placement: resetPlacement\r\n            }\r\n          };\r\n        }\r\n      }\r\n      return {};\r\n    }\r\n  };\r\n};\r\n\r\nfunction getSideOffsets(overflow, rect) {\r\n  return {\r\n    top: overflow.top - rect.height,\r\n    right: overflow.right - rect.width,\r\n    bottom: overflow.bottom - rect.height,\r\n    left: overflow.left - rect.width\r\n  };\r\n}\r\nfunction isAnySideFullyClipped(overflow) {\r\n  return sides.some(side => overflow[side] >= 0);\r\n}\r\n/**\r\n * Provides data to hide the floating element in applicable situations, such as\r\n * when it is not in the same clipping context as the reference element.\r\n * @see https://floating-ui.com/docs/hide\r\n */\r\nconst hide = function (options) {\r\n  if (options === void 0) {\r\n    options = {};\r\n  }\r\n  return {\r\n    name: 'hide',\r\n    options,\r\n    async fn(state) {\r\n      const {\r\n        rects\r\n      } = state;\r\n      const {\r\n        strategy = 'referenceHidden',\r\n        ...detectOverflowOptions\r\n      } = evaluate(options, state);\r\n      switch (strategy) {\r\n        case 'referenceHidden':\r\n          {\r\n            const overflow = await detectOverflow(state, {\r\n              ...detectOverflowOptions,\r\n              elementContext: 'reference'\r\n            });\r\n            const offsets = getSideOffsets(overflow, rects.reference);\r\n            return {\r\n              data: {\r\n                referenceHiddenOffsets: offsets,\r\n                referenceHidden: isAnySideFullyClipped(offsets)\r\n              }\r\n            };\r\n          }\r\n        case 'escaped':\r\n          {\r\n            const overflow = await detectOverflow(state, {\r\n              ...detectOverflowOptions,\r\n              altBoundary: true\r\n            });\r\n            const offsets = getSideOffsets(overflow, rects.floating);\r\n            return {\r\n              data: {\r\n                escapedOffsets: offsets,\r\n                escaped: isAnySideFullyClipped(offsets)\r\n              }\r\n            };\r\n          }\r\n        default:\r\n          {\r\n            return {};\r\n          }\r\n      }\r\n    }\r\n  };\r\n};\r\n\r\nfunction getBoundingRect(rects) {\r\n  const minX = min(...rects.map(rect => rect.left));\r\n  const minY = min(...rects.map(rect => rect.top));\r\n  const maxX = max(...rects.map(rect => rect.right));\r\n  const maxY = max(...rects.map(rect => rect.bottom));\r\n  return {\r\n    x: minX,\r\n    y: minY,\r\n    width: maxX - minX,\r\n    height: maxY - minY\r\n  };\r\n}\r\nfunction getRectsByLine(rects) {\r\n  const sortedRects = rects.slice().sort((a, b) => a.y - b.y);\r\n  const groups = [];\r\n  let prevRect = null;\r\n  for (let i = 0; i < sortedRects.length; i++) {\r\n    const rect = sortedRects[i];\r\n    if (!prevRect || rect.y - prevRect.y > prevRect.height / 2) {\r\n      groups.push([rect]);\r\n    } else {\r\n      groups[groups.length - 1].push(rect);\r\n    }\r\n    prevRect = rect;\r\n  }\r\n  return groups.map(rect => rectToClientRect(getBoundingRect(rect)));\r\n}\r\n/**\r\n * Provides improved positioning for inline reference elements that can span\r\n * over multiple lines, such as hyperlinks or range selections.\r\n * @see https://floating-ui.com/docs/inline\r\n */\r\nconst inline = function (options) {\r\n  if (options === void 0) {\r\n    options = {};\r\n  }\r\n  return {\r\n    name: 'inline',\r\n    options,\r\n    async fn(state) {\r\n      const {\r\n        placement,\r\n        elements,\r\n        rects,\r\n        platform,\r\n        strategy\r\n      } = state;\r\n      // A MouseEvent's client{X,Y} coords can be up to 2 pixels off a\r\n      // ClientRect's bounds, despite the event listener being triggered. A\r\n      // padding of 2 seems to handle this issue.\r\n      const {\r\n        padding = 2,\r\n        x,\r\n        y\r\n      } = evaluate(options, state);\r\n      const nativeClientRects = Array.from((await (platform.getClientRects == null ? void 0 : platform.getClientRects(elements.reference))) || []);\r\n      const clientRects = getRectsByLine(nativeClientRects);\r\n      const fallback = rectToClientRect(getBoundingRect(nativeClientRects));\r\n      const paddingObject = getPaddingObject(padding);\r\n      function getBoundingClientRect() {\r\n        // There are two rects and they are disjoined.\r\n        if (clientRects.length === 2 && clientRects[0].left > clientRects[1].right && x != null && y != null) {\r\n          // Find the first rect in which the point is fully inside.\r\n          return clientRects.find(rect => x > rect.left - paddingObject.left && x < rect.right + paddingObject.right && y > rect.top - paddingObject.top && y < rect.bottom + paddingObject.bottom) || fallback;\r\n        }\r\n\r\n        // There are 2 or more connected rects.\r\n        if (clientRects.length >= 2) {\r\n          if (getSideAxis(placement) === 'y') {\r\n            const firstRect = clientRects[0];\r\n            const lastRect = clientRects[clientRects.length - 1];\r\n            const isTop = getSide(placement) === 'top';\r\n            const top = firstRect.top;\r\n            const bottom = lastRect.bottom;\r\n            const left = isTop ? firstRect.left : lastRect.left;\r\n            const right = isTop ? firstRect.right : lastRect.right;\r\n            const width = right - left;\r\n            const height = bottom - top;\r\n            return {\r\n              top,\r\n              bottom,\r\n              left,\r\n              right,\r\n              width,\r\n              height,\r\n              x: left,\r\n              y: top\r\n            };\r\n          }\r\n          const isLeftSide = getSide(placement) === 'left';\r\n          const maxRight = max(...clientRects.map(rect => rect.right));\r\n          const minLeft = min(...clientRects.map(rect => rect.left));\r\n          const measureRects = clientRects.filter(rect => isLeftSide ? rect.left === minLeft : rect.right === maxRight);\r\n          const top = measureRects[0].top;\r\n          const bottom = measureRects[measureRects.length - 1].bottom;\r\n          const left = minLeft;\r\n          const right = maxRight;\r\n          const width = right - left;\r\n          const height = bottom - top;\r\n          return {\r\n            top,\r\n            bottom,\r\n            left,\r\n            right,\r\n            width,\r\n            height,\r\n            x: left,\r\n            y: top\r\n          };\r\n        }\r\n        return fallback;\r\n      }\r\n      const resetRects = await platform.getElementRects({\r\n        reference: {\r\n          getBoundingClientRect\r\n        },\r\n        floating: elements.floating,\r\n        strategy\r\n      });\r\n      if (rects.reference.x !== resetRects.reference.x || rects.reference.y !== resetRects.reference.y || rects.reference.width !== resetRects.reference.width || rects.reference.height !== resetRects.reference.height) {\r\n        return {\r\n          reset: {\r\n            rects: resetRects\r\n          }\r\n        };\r\n      }\r\n      return {};\r\n    }\r\n  };\r\n};\r\n\r\n// For type backwards-compatibility, the `OffsetOptions` type was also\r\n// Derivable.\r\n\r\nasync function convertValueToCoords(state, options) {\r\n  const {\r\n    placement,\r\n    platform,\r\n    elements\r\n  } = state;\r\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\r\n  const side = getSide(placement);\r\n  const alignment = getAlignment(placement);\r\n  const isVertical = getSideAxis(placement) === 'y';\r\n  const mainAxisMulti = ['left', 'top'].includes(side) ? -1 : 1;\r\n  const crossAxisMulti = rtl && isVertical ? -1 : 1;\r\n  const rawValue = evaluate(options, state);\r\n\r\n  // eslint-disable-next-line prefer-const\r\n  let {\r\n    mainAxis,\r\n    crossAxis,\r\n    alignmentAxis\r\n  } = typeof rawValue === 'number' ? {\r\n    mainAxis: rawValue,\r\n    crossAxis: 0,\r\n    alignmentAxis: null\r\n  } : {\r\n    mainAxis: rawValue.mainAxis || 0,\r\n    crossAxis: rawValue.crossAxis || 0,\r\n    alignmentAxis: rawValue.alignmentAxis\r\n  };\r\n  if (alignment && typeof alignmentAxis === 'number') {\r\n    crossAxis = alignment === 'end' ? alignmentAxis * -1 : alignmentAxis;\r\n  }\r\n  return isVertical ? {\r\n    x: crossAxis * crossAxisMulti,\r\n    y: mainAxis * mainAxisMulti\r\n  } : {\r\n    x: mainAxis * mainAxisMulti,\r\n    y: crossAxis * crossAxisMulti\r\n  };\r\n}\r\n\r\n/**\r\n * Modifies the placement by translating the floating element along the\r\n * specified axes.\r\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\r\n * object may be passed.\r\n * @see https://floating-ui.com/docs/offset\r\n */\r\nconst offset = function (options) {\r\n  if (options === void 0) {\r\n    options = 0;\r\n  }\r\n  return {\r\n    name: 'offset',\r\n    options,\r\n    async fn(state) {\r\n      var _middlewareData$offse, _middlewareData$arrow;\r\n      const {\r\n        x,\r\n        y,\r\n        placement,\r\n        middlewareData\r\n      } = state;\r\n      const diffCoords = await convertValueToCoords(state, options);\r\n\r\n      // If the placement is the same and the arrow caused an alignment offset\r\n      // then we don't need to change the positioning coordinates.\r\n      if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\r\n        return {};\r\n      }\r\n      return {\r\n        x: x + diffCoords.x,\r\n        y: y + diffCoords.y,\r\n        data: {\r\n          ...diffCoords,\r\n          placement\r\n        }\r\n      };\r\n    }\r\n  };\r\n};\r\n\r\n/**\r\n * Optimizes the visibility of the floating element by shifting it in order to\r\n * keep it in view when it will overflow the clipping boundary.\r\n * @see https://floating-ui.com/docs/shift\r\n */\r\nconst shift = function (options) {\r\n  if (options === void 0) {\r\n    options = {};\r\n  }\r\n  return {\r\n    name: 'shift',\r\n    options,\r\n    async fn(state) {\r\n      const {\r\n        x,\r\n        y,\r\n        placement\r\n      } = state;\r\n      const {\r\n        mainAxis: checkMainAxis = true,\r\n        crossAxis: checkCrossAxis = false,\r\n        limiter = {\r\n          fn: _ref => {\r\n            let {\r\n              x,\r\n              y\r\n            } = _ref;\r\n            return {\r\n              x,\r\n              y\r\n            };\r\n          }\r\n        },\r\n        ...detectOverflowOptions\r\n      } = evaluate(options, state);\r\n      const coords = {\r\n        x,\r\n        y\r\n      };\r\n      const overflow = await detectOverflow(state, detectOverflowOptions);\r\n      const crossAxis = getSideAxis(getSide(placement));\r\n      const mainAxis = getOppositeAxis(crossAxis);\r\n      let mainAxisCoord = coords[mainAxis];\r\n      let crossAxisCoord = coords[crossAxis];\r\n      if (checkMainAxis) {\r\n        const minSide = mainAxis === 'y' ? 'top' : 'left';\r\n        const maxSide = mainAxis === 'y' ? 'bottom' : 'right';\r\n        const min = mainAxisCoord + overflow[minSide];\r\n        const max = mainAxisCoord - overflow[maxSide];\r\n        mainAxisCoord = clamp(min, mainAxisCoord, max);\r\n      }\r\n      if (checkCrossAxis) {\r\n        const minSide = crossAxis === 'y' ? 'top' : 'left';\r\n        const maxSide = crossAxis === 'y' ? 'bottom' : 'right';\r\n        const min = crossAxisCoord + overflow[minSide];\r\n        const max = crossAxisCoord - overflow[maxSide];\r\n        crossAxisCoord = clamp(min, crossAxisCoord, max);\r\n      }\r\n      const limitedCoords = limiter.fn({\r\n        ...state,\r\n        [mainAxis]: mainAxisCoord,\r\n        [crossAxis]: crossAxisCoord\r\n      });\r\n      return {\r\n        ...limitedCoords,\r\n        data: {\r\n          x: limitedCoords.x - x,\r\n          y: limitedCoords.y - y,\r\n          enabled: {\r\n            [mainAxis]: checkMainAxis,\r\n            [crossAxis]: checkCrossAxis\r\n          }\r\n        }\r\n      };\r\n    }\r\n  };\r\n};\r\n/**\r\n * Built-in `limiter` that will stop `shift()` at a certain point.\r\n */\r\nconst limitShift = function (options) {\r\n  if (options === void 0) {\r\n    options = {};\r\n  }\r\n  return {\r\n    options,\r\n    fn(state) {\r\n      const {\r\n        x,\r\n        y,\r\n        placement,\r\n        rects,\r\n        middlewareData\r\n      } = state;\r\n      const {\r\n        offset = 0,\r\n        mainAxis: checkMainAxis = true,\r\n        crossAxis: checkCrossAxis = true\r\n      } = evaluate(options, state);\r\n      const coords = {\r\n        x,\r\n        y\r\n      };\r\n      const crossAxis = getSideAxis(placement);\r\n      const mainAxis = getOppositeAxis(crossAxis);\r\n      let mainAxisCoord = coords[mainAxis];\r\n      let crossAxisCoord = coords[crossAxis];\r\n      const rawOffset = evaluate(offset, state);\r\n      const computedOffset = typeof rawOffset === 'number' ? {\r\n        mainAxis: rawOffset,\r\n        crossAxis: 0\r\n      } : {\r\n        mainAxis: 0,\r\n        crossAxis: 0,\r\n        ...rawOffset\r\n      };\r\n      if (checkMainAxis) {\r\n        const len = mainAxis === 'y' ? 'height' : 'width';\r\n        const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\r\n        const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\r\n        if (mainAxisCoord < limitMin) {\r\n          mainAxisCoord = limitMin;\r\n        } else if (mainAxisCoord > limitMax) {\r\n          mainAxisCoord = limitMax;\r\n        }\r\n      }\r\n      if (checkCrossAxis) {\r\n        var _middlewareData$offse, _middlewareData$offse2;\r\n        const len = mainAxis === 'y' ? 'width' : 'height';\r\n        const isOriginSide = ['top', 'left'].includes(getSide(placement));\r\n        const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\r\n        const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);\r\n        if (crossAxisCoord < limitMin) {\r\n          crossAxisCoord = limitMin;\r\n        } else if (crossAxisCoord > limitMax) {\r\n          crossAxisCoord = limitMax;\r\n        }\r\n      }\r\n      return {\r\n        [mainAxis]: mainAxisCoord,\r\n        [crossAxis]: crossAxisCoord\r\n      };\r\n    }\r\n  };\r\n};\r\n\r\n/**\r\n * Provides data that allows you to change the size of the floating element —\r\n * for instance, prevent it from overflowing the clipping boundary or match the\r\n * width of the reference element.\r\n * @see https://floating-ui.com/docs/size\r\n */\r\nconst size = function (options) {\r\n  if (options === void 0) {\r\n    options = {};\r\n  }\r\n  return {\r\n    name: 'size',\r\n    options,\r\n    async fn(state) {\r\n      var _state$middlewareData, _state$middlewareData2;\r\n      const {\r\n        placement,\r\n        rects,\r\n        platform,\r\n        elements\r\n      } = state;\r\n      const {\r\n        apply = () => {},\r\n        ...detectOverflowOptions\r\n      } = evaluate(options, state);\r\n      const overflow = await detectOverflow(state, detectOverflowOptions);\r\n      const side = getSide(placement);\r\n      const alignment = getAlignment(placement);\r\n      const isYAxis = getSideAxis(placement) === 'y';\r\n      const {\r\n        width,\r\n        height\r\n      } = rects.floating;\r\n      let heightSide;\r\n      let widthSide;\r\n      if (side === 'top' || side === 'bottom') {\r\n        heightSide = side;\r\n        widthSide = alignment === ((await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating))) ? 'start' : 'end') ? 'left' : 'right';\r\n      } else {\r\n        widthSide = side;\r\n        heightSide = alignment === 'end' ? 'top' : 'bottom';\r\n      }\r\n      const maximumClippingHeight = height - overflow.top - overflow.bottom;\r\n      const maximumClippingWidth = width - overflow.left - overflow.right;\r\n      const overflowAvailableHeight = min(height - overflow[heightSide], maximumClippingHeight);\r\n      const overflowAvailableWidth = min(width - overflow[widthSide], maximumClippingWidth);\r\n      const noShift = !state.middlewareData.shift;\r\n      let availableHeight = overflowAvailableHeight;\r\n      let availableWidth = overflowAvailableWidth;\r\n      if ((_state$middlewareData = state.middlewareData.shift) != null && _state$middlewareData.enabled.x) {\r\n        availableWidth = maximumClippingWidth;\r\n      }\r\n      if ((_state$middlewareData2 = state.middlewareData.shift) != null && _state$middlewareData2.enabled.y) {\r\n        availableHeight = maximumClippingHeight;\r\n      }\r\n      if (noShift && !alignment) {\r\n        const xMin = max(overflow.left, 0);\r\n        const xMax = max(overflow.right, 0);\r\n        const yMin = max(overflow.top, 0);\r\n        const yMax = max(overflow.bottom, 0);\r\n        if (isYAxis) {\r\n          availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : max(overflow.left, overflow.right));\r\n        } else {\r\n          availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : max(overflow.top, overflow.bottom));\r\n        }\r\n      }\r\n      await apply({\r\n        ...state,\r\n        availableWidth,\r\n        availableHeight\r\n      });\r\n      const nextDimensions = await platform.getDimensions(elements.floating);\r\n      if (width !== nextDimensions.width || height !== nextDimensions.height) {\r\n        return {\r\n          reset: {\r\n            rects: true\r\n          }\r\n        };\r\n      }\r\n      return {};\r\n    }\r\n  };\r\n};\r\n\r\nexport { arrow, autoPlacement, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, shift, size };\r\n", "function hasWindow() {\r\n  return typeof window !== 'undefined';\r\n}\r\nfunction getNodeName(node) {\r\n  if (isNode(node)) {\r\n    return (node.nodeName || '').toLowerCase();\r\n  }\r\n  // Mocked nodes in testing environments may not be instances of Node. By\r\n  // returning `#document` an infinite loop won't occur.\r\n  // https://github.com/floating-ui/floating-ui/issues/2317\r\n  return '#document';\r\n}\r\nfunction getWindow(node) {\r\n  var _node$ownerDocument;\r\n  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\r\n}\r\nfunction getDocumentElement(node) {\r\n  var _ref;\r\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\r\n}\r\nfunction isNode(value) {\r\n  if (!hasWindow()) {\r\n    return false;\r\n  }\r\n  return value instanceof Node || value instanceof getWindow(value).Node;\r\n}\r\nfunction isElement(value) {\r\n  if (!hasWindow()) {\r\n    return false;\r\n  }\r\n  return value instanceof Element || value instanceof getWindow(value).Element;\r\n}\r\nfunction isHTMLElement(value) {\r\n  if (!hasWindow()) {\r\n    return false;\r\n  }\r\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\r\n}\r\nfunction isShadowRoot(value) {\r\n  if (!hasWindow() || typeof ShadowRoot === 'undefined') {\r\n    return false;\r\n  }\r\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\r\n}\r\nfunction isOverflowElement(element) {\r\n  const {\r\n    overflow,\r\n    overflowX,\r\n    overflowY,\r\n    display\r\n  } = getComputedStyle(element);\r\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !['inline', 'contents'].includes(display);\r\n}\r\nfunction isTableElement(element) {\r\n  return ['table', 'td', 'th'].includes(getNodeName(element));\r\n}\r\nfunction isTopLayer(element) {\r\n  return [':popover-open', ':modal'].some(selector => {\r\n    try {\r\n      return element.matches(selector);\r\n    } catch (e) {\r\n      return false;\r\n    }\r\n  });\r\n}\r\nfunction isContainingBlock(elementOrCss) {\r\n  const webkit = isWebKit();\r\n  const css = isElement(elementOrCss) ? getComputedStyle(elementOrCss) : elementOrCss;\r\n\r\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\r\n  // https://drafts.csswg.org/css-transforms-2/#individual-transforms\r\n  return ['transform', 'translate', 'scale', 'rotate', 'perspective'].some(value => css[value] ? css[value] !== 'none' : false) || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || ['transform', 'translate', 'scale', 'rotate', 'perspective', 'filter'].some(value => (css.willChange || '').includes(value)) || ['paint', 'layout', 'strict', 'content'].some(value => (css.contain || '').includes(value));\r\n}\r\nfunction getContainingBlock(element) {\r\n  let currentNode = getParentNode(element);\r\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\r\n    if (isContainingBlock(currentNode)) {\r\n      return currentNode;\r\n    } else if (isTopLayer(currentNode)) {\r\n      return null;\r\n    }\r\n    currentNode = getParentNode(currentNode);\r\n  }\r\n  return null;\r\n}\r\nfunction isWebKit() {\r\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\r\n  return CSS.supports('-webkit-backdrop-filter', 'none');\r\n}\r\nfunction isLastTraversableNode(node) {\r\n  return ['html', 'body', '#document'].includes(getNodeName(node));\r\n}\r\nfunction getComputedStyle(element) {\r\n  return getWindow(element).getComputedStyle(element);\r\n}\r\nfunction getNodeScroll(element) {\r\n  if (isElement(element)) {\r\n    return {\r\n      scrollLeft: element.scrollLeft,\r\n      scrollTop: element.scrollTop\r\n    };\r\n  }\r\n  return {\r\n    scrollLeft: element.scrollX,\r\n    scrollTop: element.scrollY\r\n  };\r\n}\r\nfunction getParentNode(node) {\r\n  if (getNodeName(node) === 'html') {\r\n    return node;\r\n  }\r\n  const result =\r\n  // Step into the shadow DOM of the parent of a slotted node.\r\n  node.assignedSlot ||\r\n  // DOM Element detected.\r\n  node.parentNode ||\r\n  // ShadowRoot detected.\r\n  isShadowRoot(node) && node.host ||\r\n  // Fallback.\r\n  getDocumentElement(node);\r\n  return isShadowRoot(result) ? result.host : result;\r\n}\r\nfunction getNearestOverflowAncestor(node) {\r\n  const parentNode = getParentNode(node);\r\n  if (isLastTraversableNode(parentNode)) {\r\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\r\n  }\r\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\r\n    return parentNode;\r\n  }\r\n  return getNearestOverflowAncestor(parentNode);\r\n}\r\nfunction getOverflowAncestors(node, list, traverseIframes) {\r\n  var _node$ownerDocument2;\r\n  if (list === void 0) {\r\n    list = [];\r\n  }\r\n  if (traverseIframes === void 0) {\r\n    traverseIframes = true;\r\n  }\r\n  const scrollableAncestor = getNearestOverflowAncestor(node);\r\n  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\r\n  const win = getWindow(scrollableAncestor);\r\n  if (isBody) {\r\n    const frameElement = getFrameElement(win);\r\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], frameElement && traverseIframes ? getOverflowAncestors(frameElement) : []);\r\n  }\r\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\r\n}\r\nfunction getFrameElement(win) {\r\n  return win.parent && Object.getPrototypeOf(win.parent) ? win.frameElement : null;\r\n}\r\n\r\nexport { getComputedStyle, getContainingBlock, getDocumentElement, getFrameElement, getNearestOverflowAncestor, getNodeName, getNodeScroll, getOverflowAncestors, getParentNode, getWindow, isContainingBlock, isElement, isHTMLElement, isLastTraversableNode, isNode, isOverflowElement, isShadowRoot, isTableElement, isTopLayer, isWebKit };\r\n", "import { rectToClientRect, detectOverflow as detectOverflow$1, offset as offset$1, autoPlacement as autoPlacement$1, shift as shift$1, flip as flip$1, size as size$1, hide as hide$1, arrow as arrow$1, inline as inline$1, limitShift as limitShift$1, computePosition as computePosition$1 } from '@floating-ui/core';\r\nimport { round, createCoords, max, min, floor } from '@floating-ui/utils';\r\nimport { getComputedStyle, isHTMLElement, isElement, getWindow, isWebKit, getFrameElement, getNodeScroll, getDocumentElement, isTopLayer, getNodeName, isOverflowElement, getOverflowAncestors, getParentNode, isLastTraversableNode, isContainingBlock, isTableElement, getContainingBlock } from '@floating-ui/utils/dom';\r\nexport { getOverflowAncestors } from '@floating-ui/utils/dom';\r\n\r\nfunction getCssDimensions(element) {\r\n  const css = getComputedStyle(element);\r\n  // In testing environments, the `width` and `height` properties are empty\r\n  // strings for SVG elements, returning NaN. Fallback to `0` in this case.\r\n  let width = parseFloat(css.width) || 0;\r\n  let height = parseFloat(css.height) || 0;\r\n  const hasOffset = isHTMLElement(element);\r\n  const offsetWidth = hasOffset ? element.offsetWidth : width;\r\n  const offsetHeight = hasOffset ? element.offsetHeight : height;\r\n  const shouldFallback = round(width) !== offsetWidth || round(height) !== offsetHeight;\r\n  if (shouldFallback) {\r\n    width = offsetWidth;\r\n    height = offsetHeight;\r\n  }\r\n  return {\r\n    width,\r\n    height,\r\n    $: shouldFallback\r\n  };\r\n}\r\n\r\nfunction unwrapElement(element) {\r\n  return !isElement(element) ? element.contextElement : element;\r\n}\r\n\r\nfunction getScale(element) {\r\n  const domElement = unwrapElement(element);\r\n  if (!isHTMLElement(domElement)) {\r\n    return createCoords(1);\r\n  }\r\n  const rect = domElement.getBoundingClientRect();\r\n  const {\r\n    width,\r\n    height,\r\n    $\r\n  } = getCssDimensions(domElement);\r\n  let x = ($ ? round(rect.width) : rect.width) / width;\r\n  let y = ($ ? round(rect.height) : rect.height) / height;\r\n\r\n  // 0, NaN, or Infinity should always fallback to 1.\r\n\r\n  if (!x || !Number.isFinite(x)) {\r\n    x = 1;\r\n  }\r\n  if (!y || !Number.isFinite(y)) {\r\n    y = 1;\r\n  }\r\n  return {\r\n    x,\r\n    y\r\n  };\r\n}\r\n\r\nconst noOffsets = /*#__PURE__*/createCoords(0);\r\nfunction getVisualOffsets(element) {\r\n  const win = getWindow(element);\r\n  if (!isWebKit() || !win.visualViewport) {\r\n    return noOffsets;\r\n  }\r\n  return {\r\n    x: win.visualViewport.offsetLeft,\r\n    y: win.visualViewport.offsetTop\r\n  };\r\n}\r\nfunction shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {\r\n  if (isFixed === void 0) {\r\n    isFixed = false;\r\n  }\r\n  if (!floatingOffsetParent || isFixed && floatingOffsetParent !== getWindow(element)) {\r\n    return false;\r\n  }\r\n  return isFixed;\r\n}\r\n\r\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {\r\n  if (includeScale === void 0) {\r\n    includeScale = false;\r\n  }\r\n  if (isFixedStrategy === void 0) {\r\n    isFixedStrategy = false;\r\n  }\r\n  const clientRect = element.getBoundingClientRect();\r\n  const domElement = unwrapElement(element);\r\n  let scale = createCoords(1);\r\n  if (includeScale) {\r\n    if (offsetParent) {\r\n      if (isElement(offsetParent)) {\r\n        scale = getScale(offsetParent);\r\n      }\r\n    } else {\r\n      scale = getScale(element);\r\n    }\r\n  }\r\n  const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : createCoords(0);\r\n  let x = (clientRect.left + visualOffsets.x) / scale.x;\r\n  let y = (clientRect.top + visualOffsets.y) / scale.y;\r\n  let width = clientRect.width / scale.x;\r\n  let height = clientRect.height / scale.y;\r\n  if (domElement) {\r\n    const win = getWindow(domElement);\r\n    const offsetWin = offsetParent && isElement(offsetParent) ? getWindow(offsetParent) : offsetParent;\r\n    let currentWin = win;\r\n    let currentIFrame = getFrameElement(currentWin);\r\n    while (currentIFrame && offsetParent && offsetWin !== currentWin) {\r\n      const iframeScale = getScale(currentIFrame);\r\n      const iframeRect = currentIFrame.getBoundingClientRect();\r\n      const css = getComputedStyle(currentIFrame);\r\n      const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;\r\n      const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;\r\n      x *= iframeScale.x;\r\n      y *= iframeScale.y;\r\n      width *= iframeScale.x;\r\n      height *= iframeScale.y;\r\n      x += left;\r\n      y += top;\r\n      currentWin = getWindow(currentIFrame);\r\n      currentIFrame = getFrameElement(currentWin);\r\n    }\r\n  }\r\n  return rectToClientRect({\r\n    width,\r\n    height,\r\n    x,\r\n    y\r\n  });\r\n}\r\n\r\n// If <html> has a CSS width greater than the viewport, then this will be\r\n// incorrect for RTL.\r\nfunction getWindowScrollBarX(element, rect) {\r\n  const leftScroll = getNodeScroll(element).scrollLeft;\r\n  if (!rect) {\r\n    return getBoundingClientRect(getDocumentElement(element)).left + leftScroll;\r\n  }\r\n  return rect.left + leftScroll;\r\n}\r\n\r\nfunction getHTMLOffset(documentElement, scroll, ignoreScrollbarX) {\r\n  if (ignoreScrollbarX === void 0) {\r\n    ignoreScrollbarX = false;\r\n  }\r\n  const htmlRect = documentElement.getBoundingClientRect();\r\n  const x = htmlRect.left + scroll.scrollLeft - (ignoreScrollbarX ? 0 :\r\n  // RTL <body> scrollbar.\r\n  getWindowScrollBarX(documentElement, htmlRect));\r\n  const y = htmlRect.top + scroll.scrollTop;\r\n  return {\r\n    x,\r\n    y\r\n  };\r\n}\r\n\r\nfunction convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {\r\n  let {\r\n    elements,\r\n    rect,\r\n    offsetParent,\r\n    strategy\r\n  } = _ref;\r\n  const isFixed = strategy === 'fixed';\r\n  const documentElement = getDocumentElement(offsetParent);\r\n  const topLayer = elements ? isTopLayer(elements.floating) : false;\r\n  if (offsetParent === documentElement || topLayer && isFixed) {\r\n    return rect;\r\n  }\r\n  let scroll = {\r\n    scrollLeft: 0,\r\n    scrollTop: 0\r\n  };\r\n  let scale = createCoords(1);\r\n  const offsets = createCoords(0);\r\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\r\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\r\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\r\n      scroll = getNodeScroll(offsetParent);\r\n    }\r\n    if (isHTMLElement(offsetParent)) {\r\n      const offsetRect = getBoundingClientRect(offsetParent);\r\n      scale = getScale(offsetParent);\r\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\r\n      offsets.y = offsetRect.y + offsetParent.clientTop;\r\n    }\r\n  }\r\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll, true) : createCoords(0);\r\n  return {\r\n    width: rect.width * scale.x,\r\n    height: rect.height * scale.y,\r\n    x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x + htmlOffset.x,\r\n    y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y + htmlOffset.y\r\n  };\r\n}\r\n\r\nfunction getClientRects(element) {\r\n  return Array.from(element.getClientRects());\r\n}\r\n\r\n// Gets the entire size of the scrollable document area, even extending outside\r\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable.\r\nfunction getDocumentRect(element) {\r\n  const html = getDocumentElement(element);\r\n  const scroll = getNodeScroll(element);\r\n  const body = element.ownerDocument.body;\r\n  const width = max(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);\r\n  const height = max(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);\r\n  let x = -scroll.scrollLeft + getWindowScrollBarX(element);\r\n  const y = -scroll.scrollTop;\r\n  if (getComputedStyle(body).direction === 'rtl') {\r\n    x += max(html.clientWidth, body.clientWidth) - width;\r\n  }\r\n  return {\r\n    width,\r\n    height,\r\n    x,\r\n    y\r\n  };\r\n}\r\n\r\nfunction getViewportRect(element, strategy) {\r\n  const win = getWindow(element);\r\n  const html = getDocumentElement(element);\r\n  const visualViewport = win.visualViewport;\r\n  let width = html.clientWidth;\r\n  let height = html.clientHeight;\r\n  let x = 0;\r\n  let y = 0;\r\n  if (visualViewport) {\r\n    width = visualViewport.width;\r\n    height = visualViewport.height;\r\n    const visualViewportBased = isWebKit();\r\n    if (!visualViewportBased || visualViewportBased && strategy === 'fixed') {\r\n      x = visualViewport.offsetLeft;\r\n      y = visualViewport.offsetTop;\r\n    }\r\n  }\r\n  return {\r\n    width,\r\n    height,\r\n    x,\r\n    y\r\n  };\r\n}\r\n\r\n// Returns the inner client rect, subtracting scrollbars if present.\r\nfunction getInnerBoundingClientRect(element, strategy) {\r\n  const clientRect = getBoundingClientRect(element, true, strategy === 'fixed');\r\n  const top = clientRect.top + element.clientTop;\r\n  const left = clientRect.left + element.clientLeft;\r\n  const scale = isHTMLElement(element) ? getScale(element) : createCoords(1);\r\n  const width = element.clientWidth * scale.x;\r\n  const height = element.clientHeight * scale.y;\r\n  const x = left * scale.x;\r\n  const y = top * scale.y;\r\n  return {\r\n    width,\r\n    height,\r\n    x,\r\n    y\r\n  };\r\n}\r\nfunction getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {\r\n  let rect;\r\n  if (clippingAncestor === 'viewport') {\r\n    rect = getViewportRect(element, strategy);\r\n  } else if (clippingAncestor === 'document') {\r\n    rect = getDocumentRect(getDocumentElement(element));\r\n  } else if (isElement(clippingAncestor)) {\r\n    rect = getInnerBoundingClientRect(clippingAncestor, strategy);\r\n  } else {\r\n    const visualOffsets = getVisualOffsets(element);\r\n    rect = {\r\n      x: clippingAncestor.x - visualOffsets.x,\r\n      y: clippingAncestor.y - visualOffsets.y,\r\n      width: clippingAncestor.width,\r\n      height: clippingAncestor.height\r\n    };\r\n  }\r\n  return rectToClientRect(rect);\r\n}\r\nfunction hasFixedPositionAncestor(element, stopNode) {\r\n  const parentNode = getParentNode(element);\r\n  if (parentNode === stopNode || !isElement(parentNode) || isLastTraversableNode(parentNode)) {\r\n    return false;\r\n  }\r\n  return getComputedStyle(parentNode).position === 'fixed' || hasFixedPositionAncestor(parentNode, stopNode);\r\n}\r\n\r\n// A \"clipping ancestor\" is an `overflow` element with the characteristic of\r\n// clipping (or hiding) child elements. This returns all clipping ancestors\r\n// of the given element up the tree.\r\nfunction getClippingElementAncestors(element, cache) {\r\n  const cachedResult = cache.get(element);\r\n  if (cachedResult) {\r\n    return cachedResult;\r\n  }\r\n  let result = getOverflowAncestors(element, [], false).filter(el => isElement(el) && getNodeName(el) !== 'body');\r\n  let currentContainingBlockComputedStyle = null;\r\n  const elementIsFixed = getComputedStyle(element).position === 'fixed';\r\n  let currentNode = elementIsFixed ? getParentNode(element) : element;\r\n\r\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\r\n  while (isElement(currentNode) && !isLastTraversableNode(currentNode)) {\r\n    const computedStyle = getComputedStyle(currentNode);\r\n    const currentNodeIsContaining = isContainingBlock(currentNode);\r\n    if (!currentNodeIsContaining && computedStyle.position === 'fixed') {\r\n      currentContainingBlockComputedStyle = null;\r\n    }\r\n    const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === 'static' && !!currentContainingBlockComputedStyle && ['absolute', 'fixed'].includes(currentContainingBlockComputedStyle.position) || isOverflowElement(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);\r\n    if (shouldDropCurrentNode) {\r\n      // Drop non-containing blocks.\r\n      result = result.filter(ancestor => ancestor !== currentNode);\r\n    } else {\r\n      // Record last containing block for next iteration.\r\n      currentContainingBlockComputedStyle = computedStyle;\r\n    }\r\n    currentNode = getParentNode(currentNode);\r\n  }\r\n  cache.set(element, result);\r\n  return result;\r\n}\r\n\r\n// Gets the maximum area that the element is visible in due to any number of\r\n// clipping ancestors.\r\nfunction getClippingRect(_ref) {\r\n  let {\r\n    element,\r\n    boundary,\r\n    rootBoundary,\r\n    strategy\r\n  } = _ref;\r\n  const elementClippingAncestors = boundary === 'clippingAncestors' ? isTopLayer(element) ? [] : getClippingElementAncestors(element, this._c) : [].concat(boundary);\r\n  const clippingAncestors = [...elementClippingAncestors, rootBoundary];\r\n  const firstClippingAncestor = clippingAncestors[0];\r\n  const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {\r\n    const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);\r\n    accRect.top = max(rect.top, accRect.top);\r\n    accRect.right = min(rect.right, accRect.right);\r\n    accRect.bottom = min(rect.bottom, accRect.bottom);\r\n    accRect.left = max(rect.left, accRect.left);\r\n    return accRect;\r\n  }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));\r\n  return {\r\n    width: clippingRect.right - clippingRect.left,\r\n    height: clippingRect.bottom - clippingRect.top,\r\n    x: clippingRect.left,\r\n    y: clippingRect.top\r\n  };\r\n}\r\n\r\nfunction getDimensions(element) {\r\n  const {\r\n    width,\r\n    height\r\n  } = getCssDimensions(element);\r\n  return {\r\n    width,\r\n    height\r\n  };\r\n}\r\n\r\nfunction getRectRelativeToOffsetParent(element, offsetParent, strategy) {\r\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\r\n  const documentElement = getDocumentElement(offsetParent);\r\n  const isFixed = strategy === 'fixed';\r\n  const rect = getBoundingClientRect(element, true, isFixed, offsetParent);\r\n  let scroll = {\r\n    scrollLeft: 0,\r\n    scrollTop: 0\r\n  };\r\n  const offsets = createCoords(0);\r\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\r\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\r\n      scroll = getNodeScroll(offsetParent);\r\n    }\r\n    if (isOffsetParentAnElement) {\r\n      const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);\r\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\r\n      offsets.y = offsetRect.y + offsetParent.clientTop;\r\n    } else if (documentElement) {\r\n      // If the <body> scrollbar appears on the left (e.g. RTL systems). Use\r\n      // Firefox with layout.scrollbar.side = 3 in about:config to test this.\r\n      offsets.x = getWindowScrollBarX(documentElement);\r\n    }\r\n  }\r\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll) : createCoords(0);\r\n  const x = rect.left + scroll.scrollLeft - offsets.x - htmlOffset.x;\r\n  const y = rect.top + scroll.scrollTop - offsets.y - htmlOffset.y;\r\n  return {\r\n    x,\r\n    y,\r\n    width: rect.width,\r\n    height: rect.height\r\n  };\r\n}\r\n\r\nfunction isStaticPositioned(element) {\r\n  return getComputedStyle(element).position === 'static';\r\n}\r\n\r\nfunction getTrueOffsetParent(element, polyfill) {\r\n  if (!isHTMLElement(element) || getComputedStyle(element).position === 'fixed') {\r\n    return null;\r\n  }\r\n  if (polyfill) {\r\n    return polyfill(element);\r\n  }\r\n  let rawOffsetParent = element.offsetParent;\r\n\r\n  // Firefox returns the <html> element as the offsetParent if it's non-static,\r\n  // while Chrome and Safari return the <body> element. The <body> element must\r\n  // be used to perform the correct calculations even if the <html> element is\r\n  // non-static.\r\n  if (getDocumentElement(element) === rawOffsetParent) {\r\n    rawOffsetParent = rawOffsetParent.ownerDocument.body;\r\n  }\r\n  return rawOffsetParent;\r\n}\r\n\r\n// Gets the closest ancestor positioned element. Handles some edge cases,\r\n// such as table ancestors and cross browser bugs.\r\nfunction getOffsetParent(element, polyfill) {\r\n  const win = getWindow(element);\r\n  if (isTopLayer(element)) {\r\n    return win;\r\n  }\r\n  if (!isHTMLElement(element)) {\r\n    let svgOffsetParent = getParentNode(element);\r\n    while (svgOffsetParent && !isLastTraversableNode(svgOffsetParent)) {\r\n      if (isElement(svgOffsetParent) && !isStaticPositioned(svgOffsetParent)) {\r\n        return svgOffsetParent;\r\n      }\r\n      svgOffsetParent = getParentNode(svgOffsetParent);\r\n    }\r\n    return win;\r\n  }\r\n  let offsetParent = getTrueOffsetParent(element, polyfill);\r\n  while (offsetParent && isTableElement(offsetParent) && isStaticPositioned(offsetParent)) {\r\n    offsetParent = getTrueOffsetParent(offsetParent, polyfill);\r\n  }\r\n  if (offsetParent && isLastTraversableNode(offsetParent) && isStaticPositioned(offsetParent) && !isContainingBlock(offsetParent)) {\r\n    return win;\r\n  }\r\n  return offsetParent || getContainingBlock(element) || win;\r\n}\r\n\r\nconst getElementRects = async function (data) {\r\n  const getOffsetParentFn = this.getOffsetParent || getOffsetParent;\r\n  const getDimensionsFn = this.getDimensions;\r\n  const floatingDimensions = await getDimensionsFn(data.floating);\r\n  return {\r\n    reference: getRectRelativeToOffsetParent(data.reference, await getOffsetParentFn(data.floating), data.strategy),\r\n    floating: {\r\n      x: 0,\r\n      y: 0,\r\n      width: floatingDimensions.width,\r\n      height: floatingDimensions.height\r\n    }\r\n  };\r\n};\r\n\r\nfunction isRTL(element) {\r\n  return getComputedStyle(element).direction === 'rtl';\r\n}\r\n\r\nconst platform = {\r\n  convertOffsetParentRelativeRectToViewportRelativeRect,\r\n  getDocumentElement,\r\n  getClippingRect,\r\n  getOffsetParent,\r\n  getElementRects,\r\n  getClientRects,\r\n  getDimensions,\r\n  getScale,\r\n  isElement,\r\n  isRTL\r\n};\r\n\r\nfunction rectsAreEqual(a, b) {\r\n  return a.x === b.x && a.y === b.y && a.width === b.width && a.height === b.height;\r\n}\r\n\r\n// https://samthor.au/2021/observing-dom/\r\nfunction observeMove(element, onMove) {\r\n  let io = null;\r\n  let timeoutId;\r\n  const root = getDocumentElement(element);\r\n  function cleanup() {\r\n    var _io;\r\n    clearTimeout(timeoutId);\r\n    (_io = io) == null || _io.disconnect();\r\n    io = null;\r\n  }\r\n  function refresh(skip, threshold) {\r\n    if (skip === void 0) {\r\n      skip = false;\r\n    }\r\n    if (threshold === void 0) {\r\n      threshold = 1;\r\n    }\r\n    cleanup();\r\n    const elementRectForRootMargin = element.getBoundingClientRect();\r\n    const {\r\n      left,\r\n      top,\r\n      width,\r\n      height\r\n    } = elementRectForRootMargin;\r\n    if (!skip) {\r\n      onMove();\r\n    }\r\n    if (!width || !height) {\r\n      return;\r\n    }\r\n    const insetTop = floor(top);\r\n    const insetRight = floor(root.clientWidth - (left + width));\r\n    const insetBottom = floor(root.clientHeight - (top + height));\r\n    const insetLeft = floor(left);\r\n    const rootMargin = -insetTop + \"px \" + -insetRight + \"px \" + -insetBottom + \"px \" + -insetLeft + \"px\";\r\n    const options = {\r\n      rootMargin,\r\n      threshold: max(0, min(1, threshold)) || 1\r\n    };\r\n    let isFirstUpdate = true;\r\n    function handleObserve(entries) {\r\n      const ratio = entries[0].intersectionRatio;\r\n      if (ratio !== threshold) {\r\n        if (!isFirstUpdate) {\r\n          return refresh();\r\n        }\r\n        if (!ratio) {\r\n          // If the reference is clipped, the ratio is 0. Throttle the refresh\r\n          // to prevent an infinite loop of updates.\r\n          timeoutId = setTimeout(() => {\r\n            refresh(false, 1e-7);\r\n          }, 1000);\r\n        } else {\r\n          refresh(false, ratio);\r\n        }\r\n      }\r\n      if (ratio === 1 && !rectsAreEqual(elementRectForRootMargin, element.getBoundingClientRect())) {\r\n        // It's possible that even though the ratio is reported as 1, the\r\n        // element is not actually fully within the IntersectionObserver's root\r\n        // area anymore. This can happen under performance constraints. This may\r\n        // be a bug in the browser's IntersectionObserver implementation. To\r\n        // work around this, we compare the element's bounding rect now with\r\n        // what it was at the time we created the IntersectionObserver. If they\r\n        // are not equal then the element moved, so we refresh.\r\n        refresh();\r\n      }\r\n      isFirstUpdate = false;\r\n    }\r\n\r\n    // Older browsers don't support a `document` as the root and will throw an\r\n    // error.\r\n    try {\r\n      io = new IntersectionObserver(handleObserve, {\r\n        ...options,\r\n        // Handle <iframe>s\r\n        root: root.ownerDocument\r\n      });\r\n    } catch (e) {\r\n      io = new IntersectionObserver(handleObserve, options);\r\n    }\r\n    io.observe(element);\r\n  }\r\n  refresh(true);\r\n  return cleanup;\r\n}\r\n\r\n/**\r\n * Automatically updates the position of the floating element when necessary.\r\n * Should only be called when the floating element is mounted on the DOM or\r\n * visible on the screen.\r\n * @returns cleanup function that should be invoked when the floating element is\r\n * removed from the DOM or hidden from the screen.\r\n * @see https://floating-ui.com/docs/autoUpdate\r\n */\r\nfunction autoUpdate(reference, floating, update, options) {\r\n  if (options === void 0) {\r\n    options = {};\r\n  }\r\n  const {\r\n    ancestorScroll = true,\r\n    ancestorResize = true,\r\n    elementResize = typeof ResizeObserver === 'function',\r\n    layoutShift = typeof IntersectionObserver === 'function',\r\n    animationFrame = false\r\n  } = options;\r\n  const referenceEl = unwrapElement(reference);\r\n  const ancestors = ancestorScroll || ancestorResize ? [...(referenceEl ? getOverflowAncestors(referenceEl) : []), ...getOverflowAncestors(floating)] : [];\r\n  ancestors.forEach(ancestor => {\r\n    ancestorScroll && ancestor.addEventListener('scroll', update, {\r\n      passive: true\r\n    });\r\n    ancestorResize && ancestor.addEventListener('resize', update);\r\n  });\r\n  const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;\r\n  let reobserveFrame = -1;\r\n  let resizeObserver = null;\r\n  if (elementResize) {\r\n    resizeObserver = new ResizeObserver(_ref => {\r\n      let [firstEntry] = _ref;\r\n      if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {\r\n        // Prevent update loops when using the `size` middleware.\r\n        // https://github.com/floating-ui/floating-ui/issues/1740\r\n        resizeObserver.unobserve(floating);\r\n        cancelAnimationFrame(reobserveFrame);\r\n        reobserveFrame = requestAnimationFrame(() => {\r\n          var _resizeObserver;\r\n          (_resizeObserver = resizeObserver) == null || _resizeObserver.observe(floating);\r\n        });\r\n      }\r\n      update();\r\n    });\r\n    if (referenceEl && !animationFrame) {\r\n      resizeObserver.observe(referenceEl);\r\n    }\r\n    resizeObserver.observe(floating);\r\n  }\r\n  let frameId;\r\n  let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;\r\n  if (animationFrame) {\r\n    frameLoop();\r\n  }\r\n  function frameLoop() {\r\n    const nextRefRect = getBoundingClientRect(reference);\r\n    if (prevRefRect && !rectsAreEqual(prevRefRect, nextRefRect)) {\r\n      update();\r\n    }\r\n    prevRefRect = nextRefRect;\r\n    frameId = requestAnimationFrame(frameLoop);\r\n  }\r\n  update();\r\n  return () => {\r\n    var _resizeObserver2;\r\n    ancestors.forEach(ancestor => {\r\n      ancestorScroll && ancestor.removeEventListener('scroll', update);\r\n      ancestorResize && ancestor.removeEventListener('resize', update);\r\n    });\r\n    cleanupIo == null || cleanupIo();\r\n    (_resizeObserver2 = resizeObserver) == null || _resizeObserver2.disconnect();\r\n    resizeObserver = null;\r\n    if (animationFrame) {\r\n      cancelAnimationFrame(frameId);\r\n    }\r\n  };\r\n}\r\n\r\n/**\r\n * Resolves with an object of overflow side offsets that determine how much the\r\n * element is overflowing a given clipping boundary on each side.\r\n * - positive = overflowing the boundary by that number of pixels\r\n * - negative = how many pixels left before it will overflow\r\n * - 0 = lies flush with the boundary\r\n * @see https://floating-ui.com/docs/detectOverflow\r\n */\r\nconst detectOverflow = detectOverflow$1;\r\n\r\n/**\r\n * Modifies the placement by translating the floating element along the\r\n * specified axes.\r\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\r\n * object may be passed.\r\n * @see https://floating-ui.com/docs/offset\r\n */\r\nconst offset = offset$1;\r\n\r\n/**\r\n * Optimizes the visibility of the floating element by choosing the placement\r\n * that has the most space available automatically, without needing to specify a\r\n * preferred placement. Alternative to `flip`.\r\n * @see https://floating-ui.com/docs/autoPlacement\r\n */\r\nconst autoPlacement = autoPlacement$1;\r\n\r\n/**\r\n * Optimizes the visibility of the floating element by shifting it in order to\r\n * keep it in view when it will overflow the clipping boundary.\r\n * @see https://floating-ui.com/docs/shift\r\n */\r\nconst shift = shift$1;\r\n\r\n/**\r\n * Optimizes the visibility of the floating element by flipping the `placement`\r\n * in order to keep it in view when the preferred placement(s) will overflow the\r\n * clipping boundary. Alternative to `autoPlacement`.\r\n * @see https://floating-ui.com/docs/flip\r\n */\r\nconst flip = flip$1;\r\n\r\n/**\r\n * Provides data that allows you to change the size of the floating element —\r\n * for instance, prevent it from overflowing the clipping boundary or match the\r\n * width of the reference element.\r\n * @see https://floating-ui.com/docs/size\r\n */\r\nconst size = size$1;\r\n\r\n/**\r\n * Provides data to hide the floating element in applicable situations, such as\r\n * when it is not in the same clipping context as the reference element.\r\n * @see https://floating-ui.com/docs/hide\r\n */\r\nconst hide = hide$1;\r\n\r\n/**\r\n * Provides data to position an inner element of the floating element so that it\r\n * appears centered to the reference element.\r\n * @see https://floating-ui.com/docs/arrow\r\n */\r\nconst arrow = arrow$1;\r\n\r\n/**\r\n * Provides improved positioning for inline reference elements that can span\r\n * over multiple lines, such as hyperlinks or range selections.\r\n * @see https://floating-ui.com/docs/inline\r\n */\r\nconst inline = inline$1;\r\n\r\n/**\r\n * Built-in `limiter` that will stop `shift()` at a certain point.\r\n */\r\nconst limitShift = limitShift$1;\r\n\r\n/**\r\n * Computes the `x` and `y` coordinates that will place the floating element\r\n * next to a given reference element.\r\n */\r\nconst computePosition = (reference, floating, options) => {\r\n  // This caches the expensive `getClippingElementAncestors` function so that\r\n  // multiple lifecycle resets re-use the same result. It only lives for a\r\n  // single call. If other functions become expensive, we can add them as well.\r\n  const cache = new Map();\r\n  const mergedOptions = {\r\n    platform,\r\n    ...options\r\n  };\r\n  const platformWithCache = {\r\n    ...mergedOptions.platform,\r\n    _c: cache\r\n  };\r\n  return computePosition$1(reference, floating, {\r\n    ...mergedOptions,\r\n    platform: platformWithCache\r\n  });\r\n};\r\n\r\nexport { arrow, autoPlacement, autoUpdate, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, platform, shift, size };\r\n"], "mappings": ";;;;;AA0BA,SAAS,MAAM,OAAO,OAAO,KAAK;AAChC,SAAO,IAAI,OAAO,IAAI,OAAO,GAAG,CAAC;AACnC;AACA,SAAS,SAAS,OAAO,OAAO;AAC9B,SAAO,OAAO,UAAU,aAAa,MAAM,KAAK,IAAI;AACtD;AACA,SAAS,QAAQ,WAAW;AAC1B,SAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAC/B;AACA,SAAS,aAAa,WAAW;AAC/B,SAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAC/B;AACA,SAAS,gBAAgB,MAAM;AAC7B,SAAO,SAAS,MAAM,MAAM;AAC9B;AACA,SAAS,cAAc,MAAM;AAC3B,SAAO,SAAS,MAAM,WAAW;AACnC;AACA,SAAS,YAAY,WAAW;AAC9B,SAAO,CAAC,OAAO,QAAQ,EAAE,SAAS,QAAQ,SAAS,CAAC,IAAI,MAAM;AAChE;AACA,SAAS,iBAAiB,WAAW;AACnC,SAAO,gBAAgB,YAAY,SAAS,CAAC;AAC/C;AACA,SAAS,kBAAkB,WAAW,OAAO,KAAK;AAChD,MAAI,QAAQ,QAAQ;AAClB,UAAM;AAAA,EACR;AACA,QAAM,YAAY,aAAa,SAAS;AACxC,QAAM,gBAAgB,iBAAiB,SAAS;AAChD,QAAM,SAAS,cAAc,aAAa;AAC1C,MAAI,oBAAoB,kBAAkB,MAAM,eAAe,MAAM,QAAQ,WAAW,UAAU,SAAS,cAAc,UAAU,WAAW;AAC9I,MAAI,MAAM,UAAU,MAAM,IAAI,MAAM,SAAS,MAAM,GAAG;AACpD,wBAAoB,qBAAqB,iBAAiB;AAAA,EAC5D;AACA,SAAO,CAAC,mBAAmB,qBAAqB,iBAAiB,CAAC;AACpE;AACA,SAAS,sBAAsB,WAAW;AACxC,QAAM,oBAAoB,qBAAqB,SAAS;AACxD,SAAO,CAAC,8BAA8B,SAAS,GAAG,mBAAmB,8BAA8B,iBAAiB,CAAC;AACvH;AACA,SAAS,8BAA8B,WAAW;AAChD,SAAO,UAAU,QAAQ,cAAc,eAAa,qBAAqB,SAAS,CAAC;AACrF;AACA,SAAS,YAAY,MAAM,SAAS,KAAK;AACvC,QAAM,KAAK,CAAC,QAAQ,OAAO;AAC3B,QAAM,KAAK,CAAC,SAAS,MAAM;AAC3B,QAAM,KAAK,CAAC,OAAO,QAAQ;AAC3B,QAAM,KAAK,CAAC,UAAU,KAAK;AAC3B,UAAQ,MAAM;AAAA,IACZ,KAAK;AAAA,IACL,KAAK;AACH,UAAI,IAAK,QAAO,UAAU,KAAK;AAC/B,aAAO,UAAU,KAAK;AAAA,IACxB,KAAK;AAAA,IACL,KAAK;AACH,aAAO,UAAU,KAAK;AAAA,IACxB;AACE,aAAO,CAAC;AAAA,EACZ;AACF;AACA,SAAS,0BAA0B,WAAW,eAAe,WAAW,KAAK;AAC3E,QAAM,YAAY,aAAa,SAAS;AACxC,MAAI,OAAO,YAAY,QAAQ,SAAS,GAAG,cAAc,SAAS,GAAG;AACrE,MAAI,WAAW;AACb,WAAO,KAAK,IAAI,UAAQ,OAAO,MAAM,SAAS;AAC9C,QAAI,eAAe;AACjB,aAAO,KAAK,OAAO,KAAK,IAAI,6BAA6B,CAAC;AAAA,IAC5D;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,qBAAqB,WAAW;AACvC,SAAO,UAAU,QAAQ,0BAA0B,UAAQ,gBAAgB,IAAI,CAAC;AAClF;AACA,SAAS,oBAAoB,SAAS;AACpC,SAAO;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,GAAG;AAAA,EACL;AACF;AACA,SAAS,iBAAiB,SAAS;AACjC,SAAO,OAAO,YAAY,WAAW,oBAAoB,OAAO,IAAI;AAAA,IAClE,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,EACR;AACF;AACA,SAAS,iBAAiB,MAAM;AAC9B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,KAAK;AAAA,IACL,MAAM;AAAA,IACN,OAAO,IAAI;AAAA,IACX,QAAQ,IAAI;AAAA,IACZ;AAAA,IACA;AAAA,EACF;AACF;AAvIA,IAKM,OACA,YACA,YACA,KACA,KACA,OACA,OACA,cAIA,iBAMA;AAtBN;AAAA;AAKA,IAAM,QAAQ,CAAC,OAAO,SAAS,UAAU,MAAM;AAC/C,IAAM,aAAa,CAAC,SAAS,KAAK;AAClC,IAAM,aAA0B,MAAM,OAAO,CAAC,KAAK,SAAS,IAAI,OAAO,MAAM,OAAO,MAAM,WAAW,CAAC,GAAG,OAAO,MAAM,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;AACxI,IAAM,MAAM,KAAK;AACjB,IAAM,MAAM,KAAK;AACjB,IAAM,QAAQ,KAAK;AACnB,IAAM,QAAQ,KAAK;AACnB,IAAM,eAAe,QAAM;AAAA,MACzB,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AACA,IAAM,kBAAkB;AAAA,MACtB,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,KAAK;AAAA,IACP;AACA,IAAM,uBAAuB;AAAA,MAC3B,OAAO;AAAA,MACP,KAAK;AAAA,IACP;AAAA;AAAA;;;ACtBA,SAAS,2BAA2B,MAAM,WAAW,KAAK;AACxD,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,WAAW,YAAY,SAAS;AACtC,QAAM,gBAAgB,iBAAiB,SAAS;AAChD,QAAM,cAAc,cAAc,aAAa;AAC/C,QAAM,OAAO,QAAQ,SAAS;AAC9B,QAAM,aAAa,aAAa;AAChC,QAAM,UAAU,UAAU,IAAI,UAAU,QAAQ,IAAI,SAAS,QAAQ;AACrE,QAAM,UAAU,UAAU,IAAI,UAAU,SAAS,IAAI,SAAS,SAAS;AACvE,QAAM,cAAc,UAAU,WAAW,IAAI,IAAI,SAAS,WAAW,IAAI;AACzE,MAAI;AACJ,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,eAAS;AAAA,QACP,GAAG;AAAA,QACH,GAAG,UAAU,IAAI,SAAS;AAAA,MAC5B;AACA;AAAA,IACF,KAAK;AACH,eAAS;AAAA,QACP,GAAG;AAAA,QACH,GAAG,UAAU,IAAI,UAAU;AAAA,MAC7B;AACA;AAAA,IACF,KAAK;AACH,eAAS;AAAA,QACP,GAAG,UAAU,IAAI,UAAU;AAAA,QAC3B,GAAG;AAAA,MACL;AACA;AAAA,IACF,KAAK;AACH,eAAS;AAAA,QACP,GAAG,UAAU,IAAI,SAAS;AAAA,QAC1B,GAAG;AAAA,MACL;AACA;AAAA,IACF;AACE,eAAS;AAAA,QACP,GAAG,UAAU;AAAA,QACb,GAAG,UAAU;AAAA,MACf;AAAA,EACJ;AACA,UAAQ,aAAa,SAAS,GAAG;AAAA,IAC/B,KAAK;AACH,aAAO,aAAa,KAAK,eAAe,OAAO,aAAa,KAAK;AACjE;AAAA,IACF,KAAK;AACH,aAAO,aAAa,KAAK,eAAe,OAAO,aAAa,KAAK;AACjE;AAAA,EACJ;AACA,SAAO;AACT;AAqGA,eAAe,eAAe,OAAO,SAAS;AAC5C,MAAI;AACJ,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,UAAAA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ,WAAW;AAAA,IACX,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,UAAU;AAAA,EACZ,IAAI,SAAS,SAAS,KAAK;AAC3B,QAAM,gBAAgB,iBAAiB,OAAO;AAC9C,QAAM,aAAa,mBAAmB,aAAa,cAAc;AACjE,QAAM,UAAU,SAAS,cAAc,aAAa,cAAc;AAClE,QAAM,qBAAqB,iBAAiB,MAAMA,UAAS,gBAAgB;AAAA,IACzE,WAAW,wBAAwB,OAAOA,UAAS,aAAa,OAAO,SAASA,UAAS,UAAU,OAAO,OAAO,OAAO,wBAAwB,QAAQ,UAAU,QAAQ,kBAAmB,OAAOA,UAAS,sBAAsB,OAAO,SAASA,UAAS,mBAAmB,SAAS,QAAQ;AAAA,IAChS;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,CAAC;AACF,QAAM,OAAO,mBAAmB,aAAa;AAAA,IAC3C;AAAA,IACA;AAAA,IACA,OAAO,MAAM,SAAS;AAAA,IACtB,QAAQ,MAAM,SAAS;AAAA,EACzB,IAAI,MAAM;AACV,QAAM,eAAe,OAAOA,UAAS,mBAAmB,OAAO,SAASA,UAAS,gBAAgB,SAAS,QAAQ;AAClH,QAAM,cAAe,OAAOA,UAAS,aAAa,OAAO,SAASA,UAAS,UAAU,YAAY,KAAO,OAAOA,UAAS,YAAY,OAAO,SAASA,UAAS,SAAS,YAAY,MAAO;AAAA,IACvL,GAAG;AAAA,IACH,GAAG;AAAA,EACL,IAAI;AAAA,IACF,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,QAAM,oBAAoB,iBAAiBA,UAAS,wDAAwD,MAAMA,UAAS,sDAAsD;AAAA,IAC/K;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,IAAI,IAAI;AACT,SAAO;AAAA,IACL,MAAM,mBAAmB,MAAM,kBAAkB,MAAM,cAAc,OAAO,YAAY;AAAA,IACxF,SAAS,kBAAkB,SAAS,mBAAmB,SAAS,cAAc,UAAU,YAAY;AAAA,IACpG,OAAO,mBAAmB,OAAO,kBAAkB,OAAO,cAAc,QAAQ,YAAY;AAAA,IAC5F,QAAQ,kBAAkB,QAAQ,mBAAmB,QAAQ,cAAc,SAAS,YAAY;AAAA,EAClG;AACF;AAoFA,SAAS,iBAAiB,WAAW,eAAe,mBAAmB;AACrE,QAAM,qCAAqC,YAAY,CAAC,GAAG,kBAAkB,OAAO,eAAa,aAAa,SAAS,MAAM,SAAS,GAAG,GAAG,kBAAkB,OAAO,eAAa,aAAa,SAAS,MAAM,SAAS,CAAC,IAAI,kBAAkB,OAAO,eAAa,QAAQ,SAAS,MAAM,SAAS;AAClS,SAAO,mCAAmC,OAAO,eAAa;AAC5D,QAAI,WAAW;AACb,aAAO,aAAa,SAAS,MAAM,cAAc,gBAAgB,8BAA8B,SAAS,MAAM,YAAY;AAAA,IAC5H;AACA,WAAO;AAAA,EACT,CAAC;AACH;AA6NA,SAAS,eAAe,UAAU,MAAM;AACtC,SAAO;AAAA,IACL,KAAK,SAAS,MAAM,KAAK;AAAA,IACzB,OAAO,SAAS,QAAQ,KAAK;AAAA,IAC7B,QAAQ,SAAS,SAAS,KAAK;AAAA,IAC/B,MAAM,SAAS,OAAO,KAAK;AAAA,EAC7B;AACF;AACA,SAAS,sBAAsB,UAAU;AACvC,SAAO,MAAM,KAAK,UAAQ,SAAS,IAAI,KAAK,CAAC;AAC/C;AA2DA,SAAS,gBAAgB,OAAO;AAC9B,QAAM,OAAO,IAAI,GAAG,MAAM,IAAI,UAAQ,KAAK,IAAI,CAAC;AAChD,QAAM,OAAO,IAAI,GAAG,MAAM,IAAI,UAAQ,KAAK,GAAG,CAAC;AAC/C,QAAM,OAAO,IAAI,GAAG,MAAM,IAAI,UAAQ,KAAK,KAAK,CAAC;AACjD,QAAM,OAAO,IAAI,GAAG,MAAM,IAAI,UAAQ,KAAK,MAAM,CAAC;AAClD,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO,OAAO;AAAA,IACd,QAAQ,OAAO;AAAA,EACjB;AACF;AACA,SAAS,eAAe,OAAO;AAC7B,QAAM,cAAc,MAAM,MAAM,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,IAAI,EAAE,CAAC;AAC1D,QAAM,SAAS,CAAC;AAChB,MAAI,WAAW;AACf,WAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,UAAM,OAAO,YAAY,CAAC;AAC1B,QAAI,CAAC,YAAY,KAAK,IAAI,SAAS,IAAI,SAAS,SAAS,GAAG;AAC1D,aAAO,KAAK,CAAC,IAAI,CAAC;AAAA,IACpB,OAAO;AACL,aAAO,OAAO,SAAS,CAAC,EAAE,KAAK,IAAI;AAAA,IACrC;AACA,eAAW;AAAA,EACb;AACA,SAAO,OAAO,IAAI,UAAQ,iBAAiB,gBAAgB,IAAI,CAAC,CAAC;AACnE;AA4GA,eAAe,qBAAqB,OAAO,SAAS;AAClD,QAAM;AAAA,IACJ;AAAA,IACA,UAAAA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,MAAM,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,SAAS,QAAQ;AACrF,QAAM,OAAO,QAAQ,SAAS;AAC9B,QAAM,YAAY,aAAa,SAAS;AACxC,QAAM,aAAa,YAAY,SAAS,MAAM;AAC9C,QAAM,gBAAgB,CAAC,QAAQ,KAAK,EAAE,SAAS,IAAI,IAAI,KAAK;AAC5D,QAAM,iBAAiB,OAAO,aAAa,KAAK;AAChD,QAAM,WAAW,SAAS,SAAS,KAAK;AAGxC,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,OAAO,aAAa,WAAW;AAAA,IACjC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,eAAe;AAAA,EACjB,IAAI;AAAA,IACF,UAAU,SAAS,YAAY;AAAA,IAC/B,WAAW,SAAS,aAAa;AAAA,IACjC,eAAe,SAAS;AAAA,EAC1B;AACA,MAAI,aAAa,OAAO,kBAAkB,UAAU;AAClD,gBAAY,cAAc,QAAQ,gBAAgB,KAAK;AAAA,EACzD;AACA,SAAO,aAAa;AAAA,IAClB,GAAG,YAAY;AAAA,IACf,GAAG,WAAW;AAAA,EAChB,IAAI;AAAA,IACF,GAAG,WAAW;AAAA,IACd,GAAG,YAAY;AAAA,EACjB;AACF;AA/vBA,IAkEM,iBA0JA,OA4FA,eA8FA,MAwIA,MAqFA,QAqJA,QAuCA,OA2EA,YAwEA;AAl8BN;AAAA;AAAA;AACA;AAiEA,IAAM,kBAAkB,OAAO,WAAW,UAAU,WAAW;AAC7D,YAAM;AAAA,QACJ,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,aAAa,CAAC;AAAA,QACd,UAAAA;AAAA,MACF,IAAI;AACJ,YAAM,kBAAkB,WAAW,OAAO,OAAO;AACjD,YAAM,MAAM,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,QAAQ;AAC5E,UAAI,QAAQ,MAAMA,UAAS,gBAAgB;AAAA,QACzC;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AACD,UAAI;AAAA,QACF;AAAA,QACA;AAAA,MACF,IAAI,2BAA2B,OAAO,WAAW,GAAG;AACpD,UAAI,oBAAoB;AACxB,UAAI,iBAAiB,CAAC;AACtB,UAAI,aAAa;AACjB,eAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC/C,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI,gBAAgB,CAAC;AACrB,cAAM;AAAA,UACJ,GAAG;AAAA,UACH,GAAG;AAAA,UACH;AAAA,UACA;AAAA,QACF,IAAI,MAAM,GAAG;AAAA,UACX;AAAA,UACA;AAAA,UACA,kBAAkB;AAAA,UAClB,WAAW;AAAA,UACX;AAAA,UACA;AAAA,UACA;AAAA,UACA,UAAAA;AAAA,UACA,UAAU;AAAA,YACR;AAAA,YACA;AAAA,UACF;AAAA,QACF,CAAC;AACD,YAAI,SAAS,OAAO,QAAQ;AAC5B,YAAI,SAAS,OAAO,QAAQ;AAC5B,yBAAiB;AAAA,UACf,GAAG;AAAA,UACH,CAAC,IAAI,GAAG;AAAA,YACN,GAAG,eAAe,IAAI;AAAA,YACtB,GAAG;AAAA,UACL;AAAA,QACF;AACA,YAAI,SAAS,cAAc,IAAI;AAC7B;AACA,cAAI,OAAO,UAAU,UAAU;AAC7B,gBAAI,MAAM,WAAW;AACnB,kCAAoB,MAAM;AAAA,YAC5B;AACA,gBAAI,MAAM,OAAO;AACf,sBAAQ,MAAM,UAAU,OAAO,MAAMA,UAAS,gBAAgB;AAAA,gBAC5D;AAAA,gBACA;AAAA,gBACA;AAAA,cACF,CAAC,IAAI,MAAM;AAAA,YACb;AACA,aAAC;AAAA,cACC;AAAA,cACA;AAAA,YACF,IAAI,2BAA2B,OAAO,mBAAmB,GAAG;AAAA,UAC9D;AACA,cAAI;AAAA,QACN;AAAA,MACF;AACA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,WAAW;AAAA,QACX;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAwEA,IAAM,QAAQ,cAAY;AAAA,MACxB,MAAM;AAAA,MACN;AAAA,MACA,MAAM,GAAG,OAAO;AACd,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,UAAAA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AAEJ,cAAM;AAAA,UACJ;AAAA,UACA,UAAU;AAAA,QACZ,IAAI,SAAS,SAAS,KAAK,KAAK,CAAC;AACjC,YAAI,WAAW,MAAM;AACnB,iBAAO,CAAC;AAAA,QACV;AACA,cAAM,gBAAgB,iBAAiB,OAAO;AAC9C,cAAM,SAAS;AAAA,UACb;AAAA,UACA;AAAA,QACF;AACA,cAAM,OAAO,iBAAiB,SAAS;AACvC,cAAM,SAAS,cAAc,IAAI;AACjC,cAAM,kBAAkB,MAAMA,UAAS,cAAc,OAAO;AAC5D,cAAM,UAAU,SAAS;AACzB,cAAM,UAAU,UAAU,QAAQ;AAClC,cAAM,UAAU,UAAU,WAAW;AACrC,cAAM,aAAa,UAAU,iBAAiB;AAC9C,cAAM,UAAU,MAAM,UAAU,MAAM,IAAI,MAAM,UAAU,IAAI,IAAI,OAAO,IAAI,IAAI,MAAM,SAAS,MAAM;AACtG,cAAM,YAAY,OAAO,IAAI,IAAI,MAAM,UAAU,IAAI;AACrD,cAAM,oBAAoB,OAAOA,UAAS,mBAAmB,OAAO,SAASA,UAAS,gBAAgB,OAAO;AAC7G,YAAI,aAAa,oBAAoB,kBAAkB,UAAU,IAAI;AAGrE,YAAI,CAAC,cAAc,CAAE,OAAOA,UAAS,aAAa,OAAO,SAASA,UAAS,UAAU,iBAAiB,IAAK;AACzG,uBAAa,SAAS,SAAS,UAAU,KAAK,MAAM,SAAS,MAAM;AAAA,QACrE;AACA,cAAM,oBAAoB,UAAU,IAAI,YAAY;AAIpD,cAAM,yBAAyB,aAAa,IAAI,gBAAgB,MAAM,IAAI,IAAI;AAC9E,cAAM,aAAa,IAAI,cAAc,OAAO,GAAG,sBAAsB;AACrE,cAAM,aAAa,IAAI,cAAc,OAAO,GAAG,sBAAsB;AAIrE,cAAM,QAAQ;AACd,cAAMC,OAAM,aAAa,gBAAgB,MAAM,IAAI;AACnD,cAAM,SAAS,aAAa,IAAI,gBAAgB,MAAM,IAAI,IAAI;AAC9D,cAAMC,UAAS,MAAM,OAAO,QAAQD,IAAG;AAMvC,cAAM,kBAAkB,CAAC,eAAe,SAAS,aAAa,SAAS,KAAK,QAAQ,WAAWC,WAAU,MAAM,UAAU,MAAM,IAAI,KAAK,SAAS,QAAQ,aAAa,cAAc,gBAAgB,MAAM,IAAI,IAAI;AAClN,cAAM,kBAAkB,kBAAkB,SAAS,QAAQ,SAAS,QAAQ,SAASD,OAAM;AAC3F,eAAO;AAAA,UACL,CAAC,IAAI,GAAG,OAAO,IAAI,IAAI;AAAA,UACvB,MAAM;AAAA,YACJ,CAAC,IAAI,GAAGC;AAAA,YACR,cAAc,SAASA,UAAS;AAAA,YAChC,GAAI,mBAAmB;AAAA,cACrB;AAAA,YACF;AAAA,UACF;AAAA,UACA,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAiBA,IAAM,gBAAgB,SAAU,SAAS;AACvC,UAAI,YAAY,QAAQ;AACtB,kBAAU,CAAC;AAAA,MACb;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN;AAAA,QACA,MAAM,GAAG,OAAO;AACd,cAAI,uBAAuB,wBAAwB;AACnD,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,YACA,UAAAF;AAAA,YACA;AAAA,UACF,IAAI;AACJ,gBAAM;AAAA,YACJ,YAAY;AAAA,YACZ;AAAA,YACA,oBAAoB;AAAA,YACpB,gBAAgB;AAAA,YAChB,GAAG;AAAA,UACL,IAAI,SAAS,SAAS,KAAK;AAC3B,gBAAM,eAAe,cAAc,UAAa,sBAAsB,aAAa,iBAAiB,aAAa,MAAM,eAAe,iBAAiB,IAAI;AAC3J,gBAAM,WAAW,MAAM,eAAe,OAAO,qBAAqB;AAClE,gBAAM,iBAAiB,wBAAwB,eAAe,kBAAkB,OAAO,SAAS,sBAAsB,UAAU;AAChI,gBAAM,mBAAmB,aAAa,YAAY;AAClD,cAAI,oBAAoB,MAAM;AAC5B,mBAAO,CAAC;AAAA,UACV;AACA,gBAAM,iBAAiB,kBAAkB,kBAAkB,OAAO,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,SAAS,QAAQ,EAAE;AAG7I,cAAI,cAAc,kBAAkB;AAClC,mBAAO;AAAA,cACL,OAAO;AAAA,gBACL,WAAW,aAAa,CAAC;AAAA,cAC3B;AAAA,YACF;AAAA,UACF;AACA,gBAAM,mBAAmB,CAAC,SAAS,QAAQ,gBAAgB,CAAC,GAAG,SAAS,eAAe,CAAC,CAAC,GAAG,SAAS,eAAe,CAAC,CAAC,CAAC;AACvH,gBAAM,eAAe,CAAC,KAAM,yBAAyB,eAAe,kBAAkB,OAAO,SAAS,uBAAuB,cAAc,CAAC,GAAI;AAAA,YAC9I,WAAW;AAAA,YACX,WAAW;AAAA,UACb,CAAC;AACD,gBAAM,gBAAgB,aAAa,eAAe,CAAC;AAGnD,cAAI,eAAe;AACjB,mBAAO;AAAA,cACL,MAAM;AAAA,gBACJ,OAAO,eAAe;AAAA,gBACtB,WAAW;AAAA,cACb;AAAA,cACA,OAAO;AAAA,gBACL,WAAW;AAAA,cACb;AAAA,YACF;AAAA,UACF;AACA,gBAAM,8BAA8B,aAAa,IAAI,OAAK;AACxD,kBAAMG,aAAY,aAAa,EAAE,SAAS;AAC1C,mBAAO,CAAC,EAAE,WAAWA,cAAa;AAAA;AAAA,cAElC,EAAE,UAAU,MAAM,GAAG,CAAC,EAAE,OAAO,CAAC,KAAK,MAAM,MAAM,GAAG,CAAC;AAAA;AAAA;AAAA,cAErD,EAAE,UAAU,CAAC;AAAA,eAAG,EAAE,SAAS;AAAA,UAC7B,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;AAC7B,gBAAM,8BAA8B,4BAA4B,OAAO,OAAK,EAAE,CAAC,EAAE;AAAA,YAAM;AAAA;AAAA;AAAA,YAGvF,aAAa,EAAE,CAAC,CAAC,IAAI,IAAI;AAAA,UAAC,EAAE,MAAM,OAAK,KAAK,CAAC,CAAC;AAC9C,gBAAM,mBAAmB,wBAAwB,4BAA4B,CAAC,MAAM,OAAO,SAAS,sBAAsB,CAAC,MAAM,4BAA4B,CAAC,EAAE,CAAC;AACjK,cAAI,mBAAmB,WAAW;AAChC,mBAAO;AAAA,cACL,MAAM;AAAA,gBACJ,OAAO,eAAe;AAAA,gBACtB,WAAW;AAAA,cACb;AAAA,cACA,OAAO;AAAA,gBACL,WAAW;AAAA,cACb;AAAA,YACF;AAAA,UACF;AACA,iBAAO,CAAC;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAQA,IAAM,OAAO,SAAU,SAAS;AAC9B,UAAI,YAAY,QAAQ;AACtB,kBAAU,CAAC;AAAA,MACb;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN;AAAA,QACA,MAAM,GAAG,OAAO;AACd,cAAI,uBAAuB;AAC3B,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,UAAAH;AAAA,YACA;AAAA,UACF,IAAI;AACJ,gBAAM;AAAA,YACJ,UAAU,gBAAgB;AAAA,YAC1B,WAAW,iBAAiB;AAAA,YAC5B,oBAAoB;AAAA,YACpB,mBAAmB;AAAA,YACnB,4BAA4B;AAAA,YAC5B,gBAAgB;AAAA,YAChB,GAAG;AAAA,UACL,IAAI,SAAS,SAAS,KAAK;AAM3B,eAAK,wBAAwB,eAAe,UAAU,QAAQ,sBAAsB,iBAAiB;AACnG,mBAAO,CAAC;AAAA,UACV;AACA,gBAAM,OAAO,QAAQ,SAAS;AAC9B,gBAAM,kBAAkB,YAAY,gBAAgB;AACpD,gBAAM,kBAAkB,QAAQ,gBAAgB,MAAM;AACtD,gBAAM,MAAM,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,SAAS,QAAQ;AACrF,gBAAM,qBAAqB,gCAAgC,mBAAmB,CAAC,gBAAgB,CAAC,qBAAqB,gBAAgB,CAAC,IAAI,sBAAsB,gBAAgB;AAChL,gBAAM,+BAA+B,8BAA8B;AACnE,cAAI,CAAC,+BAA+B,8BAA8B;AAChE,+BAAmB,KAAK,GAAG,0BAA0B,kBAAkB,eAAe,2BAA2B,GAAG,CAAC;AAAA,UACvH;AACA,gBAAMI,cAAa,CAAC,kBAAkB,GAAG,kBAAkB;AAC3D,gBAAM,WAAW,MAAM,eAAe,OAAO,qBAAqB;AAClE,gBAAM,YAAY,CAAC;AACnB,cAAI,kBAAkB,uBAAuB,eAAe,SAAS,OAAO,SAAS,qBAAqB,cAAc,CAAC;AACzH,cAAI,eAAe;AACjB,sBAAU,KAAK,SAAS,IAAI,CAAC;AAAA,UAC/B;AACA,cAAI,gBAAgB;AAClB,kBAAMC,SAAQ,kBAAkB,WAAW,OAAO,GAAG;AACrD,sBAAU,KAAK,SAASA,OAAM,CAAC,CAAC,GAAG,SAASA,OAAM,CAAC,CAAC,CAAC;AAAA,UACvD;AACA,0BAAgB,CAAC,GAAG,eAAe;AAAA,YACjC;AAAA,YACA;AAAA,UACF,CAAC;AAGD,cAAI,CAAC,UAAU,MAAM,CAAAC,UAAQA,SAAQ,CAAC,GAAG;AACvC,gBAAI,uBAAuB;AAC3B,kBAAM,eAAe,wBAAwB,eAAe,SAAS,OAAO,SAAS,sBAAsB,UAAU,KAAK;AAC1H,kBAAM,gBAAgBF,YAAW,SAAS;AAC1C,gBAAI,eAAe;AAEjB,qBAAO;AAAA,gBACL,MAAM;AAAA,kBACJ,OAAO;AAAA,kBACP,WAAW;AAAA,gBACb;AAAA,gBACA,OAAO;AAAA,kBACL,WAAW;AAAA,gBACb;AAAA,cACF;AAAA,YACF;AAIA,gBAAI,kBAAkB,wBAAwB,cAAc,OAAO,OAAK,EAAE,UAAU,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,MAAM,OAAO,SAAS,sBAAsB;AAG1L,gBAAI,CAAC,gBAAgB;AACnB,sBAAQ,kBAAkB;AAAA,gBACxB,KAAK,WACH;AACE,sBAAI;AACJ,wBAAMG,cAAa,yBAAyB,cAAc,OAAO,OAAK;AACpE,wBAAI,8BAA8B;AAChC,4BAAM,kBAAkB,YAAY,EAAE,SAAS;AAC/C,6BAAO,oBAAoB;AAAA;AAAA,sBAG3B,oBAAoB;AAAA,oBACtB;AACA,2BAAO;AAAA,kBACT,CAAC,EAAE,IAAI,OAAK,CAAC,EAAE,WAAW,EAAE,UAAU,OAAO,CAAAC,cAAYA,YAAW,CAAC,EAAE,OAAO,CAAC,KAAKA,cAAa,MAAMA,WAAU,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,MAAM,OAAO,SAAS,uBAAuB,CAAC;AACjM,sBAAID,YAAW;AACb,qCAAiBA;AAAA,kBACnB;AACA;AAAA,gBACF;AAAA,gBACF,KAAK;AACH,mCAAiB;AACjB;AAAA,cACJ;AAAA,YACF;AACA,gBAAI,cAAc,gBAAgB;AAChC,qBAAO;AAAA,gBACL,OAAO;AAAA,kBACL,WAAW;AAAA,gBACb;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,iBAAO,CAAC;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAkBA,IAAM,OAAO,SAAU,SAAS;AAC9B,UAAI,YAAY,QAAQ;AACtB,kBAAU,CAAC;AAAA,MACb;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN;AAAA,QACA,MAAM,GAAG,OAAO;AACd,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI;AACJ,gBAAM;AAAA,YACJ,WAAW;AAAA,YACX,GAAG;AAAA,UACL,IAAI,SAAS,SAAS,KAAK;AAC3B,kBAAQ,UAAU;AAAA,YAChB,KAAK,mBACH;AACE,oBAAM,WAAW,MAAM,eAAe,OAAO;AAAA,gBAC3C,GAAG;AAAA,gBACH,gBAAgB;AAAA,cAClB,CAAC;AACD,oBAAM,UAAU,eAAe,UAAU,MAAM,SAAS;AACxD,qBAAO;AAAA,gBACL,MAAM;AAAA,kBACJ,wBAAwB;AAAA,kBACxB,iBAAiB,sBAAsB,OAAO;AAAA,gBAChD;AAAA,cACF;AAAA,YACF;AAAA,YACF,KAAK,WACH;AACE,oBAAM,WAAW,MAAM,eAAe,OAAO;AAAA,gBAC3C,GAAG;AAAA,gBACH,aAAa;AAAA,cACf,CAAC;AACD,oBAAM,UAAU,eAAe,UAAU,MAAM,QAAQ;AACvD,qBAAO;AAAA,gBACL,MAAM;AAAA,kBACJ,gBAAgB;AAAA,kBAChB,SAAS,sBAAsB,OAAO;AAAA,gBACxC;AAAA,cACF;AAAA,YACF;AAAA,YACF,SACE;AACE,qBAAO,CAAC;AAAA,YACV;AAAA,UACJ;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAkCA,IAAM,SAAS,SAAU,SAAS;AAChC,UAAI,YAAY,QAAQ;AACtB,kBAAU,CAAC;AAAA,MACb;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN;AAAA,QACA,MAAM,GAAG,OAAO;AACd,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,YACA,UAAAP;AAAA,YACA;AAAA,UACF,IAAI;AAIJ,gBAAM;AAAA,YACJ,UAAU;AAAA,YACV;AAAA,YACA;AAAA,UACF,IAAI,SAAS,SAAS,KAAK;AAC3B,gBAAM,oBAAoB,MAAM,KAAM,OAAOA,UAAS,kBAAkB,OAAO,SAASA,UAAS,eAAe,SAAS,SAAS,MAAO,CAAC,CAAC;AAC3I,gBAAM,cAAc,eAAe,iBAAiB;AACpD,gBAAM,WAAW,iBAAiB,gBAAgB,iBAAiB,CAAC;AACpE,gBAAM,gBAAgB,iBAAiB,OAAO;AAC9C,mBAASS,yBAAwB;AAE/B,gBAAI,YAAY,WAAW,KAAK,YAAY,CAAC,EAAE,OAAO,YAAY,CAAC,EAAE,SAAS,KAAK,QAAQ,KAAK,MAAM;AAEpG,qBAAO,YAAY,KAAK,UAAQ,IAAI,KAAK,OAAO,cAAc,QAAQ,IAAI,KAAK,QAAQ,cAAc,SAAS,IAAI,KAAK,MAAM,cAAc,OAAO,IAAI,KAAK,SAAS,cAAc,MAAM,KAAK;AAAA,YAC/L;AAGA,gBAAI,YAAY,UAAU,GAAG;AAC3B,kBAAI,YAAY,SAAS,MAAM,KAAK;AAClC,sBAAM,YAAY,YAAY,CAAC;AAC/B,sBAAM,WAAW,YAAY,YAAY,SAAS,CAAC;AACnD,sBAAM,QAAQ,QAAQ,SAAS,MAAM;AACrC,sBAAMC,OAAM,UAAU;AACtB,sBAAMC,UAAS,SAAS;AACxB,sBAAMC,QAAO,QAAQ,UAAU,OAAO,SAAS;AAC/C,sBAAMC,SAAQ,QAAQ,UAAU,QAAQ,SAAS;AACjD,sBAAMC,SAAQD,SAAQD;AACtB,sBAAMG,UAASJ,UAASD;AACxB,uBAAO;AAAA,kBACL,KAAAA;AAAA,kBACA,QAAAC;AAAA,kBACA,MAAAC;AAAA,kBACA,OAAAC;AAAA,kBACA,OAAAC;AAAA,kBACA,QAAAC;AAAA,kBACA,GAAGH;AAAA,kBACH,GAAGF;AAAA,gBACL;AAAA,cACF;AACA,oBAAM,aAAa,QAAQ,SAAS,MAAM;AAC1C,oBAAM,WAAW,IAAI,GAAG,YAAY,IAAI,UAAQ,KAAK,KAAK,CAAC;AAC3D,oBAAM,UAAU,IAAI,GAAG,YAAY,IAAI,UAAQ,KAAK,IAAI,CAAC;AACzD,oBAAM,eAAe,YAAY,OAAO,UAAQ,aAAa,KAAK,SAAS,UAAU,KAAK,UAAU,QAAQ;AAC5G,oBAAM,MAAM,aAAa,CAAC,EAAE;AAC5B,oBAAM,SAAS,aAAa,aAAa,SAAS,CAAC,EAAE;AACrD,oBAAM,OAAO;AACb,oBAAM,QAAQ;AACd,oBAAM,QAAQ,QAAQ;AACtB,oBAAM,SAAS,SAAS;AACxB,qBAAO;AAAA,gBACL;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,GAAG;AAAA,gBACH,GAAG;AAAA,cACL;AAAA,YACF;AACA,mBAAO;AAAA,UACT;AACA,gBAAM,aAAa,MAAMV,UAAS,gBAAgB;AAAA,YAChD,WAAW;AAAA,cACT,uBAAAS;AAAA,YACF;AAAA,YACA,UAAU,SAAS;AAAA,YACnB;AAAA,UACF,CAAC;AACD,cAAI,MAAM,UAAU,MAAM,WAAW,UAAU,KAAK,MAAM,UAAU,MAAM,WAAW,UAAU,KAAK,MAAM,UAAU,UAAU,WAAW,UAAU,SAAS,MAAM,UAAU,WAAW,WAAW,UAAU,QAAQ;AAClN,mBAAO;AAAA,cACL,OAAO;AAAA,gBACL,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AACA,iBAAO,CAAC;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAoDA,IAAM,SAAS,SAAU,SAAS;AAChC,UAAI,YAAY,QAAQ;AACtB,kBAAU;AAAA,MACZ;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN;AAAA,QACA,MAAM,GAAG,OAAO;AACd,cAAI,uBAAuB;AAC3B,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI;AACJ,gBAAM,aAAa,MAAM,qBAAqB,OAAO,OAAO;AAI5D,cAAI,gBAAgB,wBAAwB,eAAe,WAAW,OAAO,SAAS,sBAAsB,eAAe,wBAAwB,eAAe,UAAU,QAAQ,sBAAsB,iBAAiB;AACzN,mBAAO,CAAC;AAAA,UACV;AACA,iBAAO;AAAA,YACL,GAAG,IAAI,WAAW;AAAA,YAClB,GAAG,IAAI,WAAW;AAAA,YAClB,MAAM;AAAA,cACJ,GAAG;AAAA,cACH;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAOA,IAAM,QAAQ,SAAU,SAAS;AAC/B,UAAI,YAAY,QAAQ;AACtB,kBAAU,CAAC;AAAA,MACb;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN;AAAA,QACA,MAAM,GAAG,OAAO;AACd,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI;AACJ,gBAAM;AAAA,YACJ,UAAU,gBAAgB;AAAA,YAC1B,WAAW,iBAAiB;AAAA,YAC5B,UAAU;AAAA,cACR,IAAI,UAAQ;AACV,oBAAI;AAAA,kBACF,GAAAO;AAAA,kBACA,GAAAC;AAAA,gBACF,IAAI;AACJ,uBAAO;AAAA,kBACL,GAAAD;AAAA,kBACA,GAAAC;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,YACA,GAAG;AAAA,UACL,IAAI,SAAS,SAAS,KAAK;AAC3B,gBAAM,SAAS;AAAA,YACb;AAAA,YACA;AAAA,UACF;AACA,gBAAM,WAAW,MAAM,eAAe,OAAO,qBAAqB;AAClE,gBAAM,YAAY,YAAY,QAAQ,SAAS,CAAC;AAChD,gBAAM,WAAW,gBAAgB,SAAS;AAC1C,cAAI,gBAAgB,OAAO,QAAQ;AACnC,cAAI,iBAAiB,OAAO,SAAS;AACrC,cAAI,eAAe;AACjB,kBAAM,UAAU,aAAa,MAAM,QAAQ;AAC3C,kBAAM,UAAU,aAAa,MAAM,WAAW;AAC9C,kBAAMC,OAAM,gBAAgB,SAAS,OAAO;AAC5C,kBAAMjB,OAAM,gBAAgB,SAAS,OAAO;AAC5C,4BAAgB,MAAMiB,MAAK,eAAejB,IAAG;AAAA,UAC/C;AACA,cAAI,gBAAgB;AAClB,kBAAM,UAAU,cAAc,MAAM,QAAQ;AAC5C,kBAAM,UAAU,cAAc,MAAM,WAAW;AAC/C,kBAAMiB,OAAM,iBAAiB,SAAS,OAAO;AAC7C,kBAAMjB,OAAM,iBAAiB,SAAS,OAAO;AAC7C,6BAAiB,MAAMiB,MAAK,gBAAgBjB,IAAG;AAAA,UACjD;AACA,gBAAM,gBAAgB,QAAQ,GAAG;AAAA,YAC/B,GAAG;AAAA,YACH,CAAC,QAAQ,GAAG;AAAA,YACZ,CAAC,SAAS,GAAG;AAAA,UACf,CAAC;AACD,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,MAAM;AAAA,cACJ,GAAG,cAAc,IAAI;AAAA,cACrB,GAAG,cAAc,IAAI;AAAA,cACrB,SAAS;AAAA,gBACP,CAAC,QAAQ,GAAG;AAAA,gBACZ,CAAC,SAAS,GAAG;AAAA,cACf;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAIA,IAAM,aAAa,SAAU,SAAS;AACpC,UAAI,YAAY,QAAQ;AACtB,kBAAU,CAAC;AAAA,MACb;AACA,aAAO;AAAA,QACL;AAAA,QACA,GAAG,OAAO;AACR,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI;AACJ,gBAAM;AAAA,YACJ,QAAAC,UAAS;AAAA,YACT,UAAU,gBAAgB;AAAA,YAC1B,WAAW,iBAAiB;AAAA,UAC9B,IAAI,SAAS,SAAS,KAAK;AAC3B,gBAAM,SAAS;AAAA,YACb;AAAA,YACA;AAAA,UACF;AACA,gBAAM,YAAY,YAAY,SAAS;AACvC,gBAAM,WAAW,gBAAgB,SAAS;AAC1C,cAAI,gBAAgB,OAAO,QAAQ;AACnC,cAAI,iBAAiB,OAAO,SAAS;AACrC,gBAAM,YAAY,SAASA,SAAQ,KAAK;AACxC,gBAAM,iBAAiB,OAAO,cAAc,WAAW;AAAA,YACrD,UAAU;AAAA,YACV,WAAW;AAAA,UACb,IAAI;AAAA,YACF,UAAU;AAAA,YACV,WAAW;AAAA,YACX,GAAG;AAAA,UACL;AACA,cAAI,eAAe;AACjB,kBAAM,MAAM,aAAa,MAAM,WAAW;AAC1C,kBAAM,WAAW,MAAM,UAAU,QAAQ,IAAI,MAAM,SAAS,GAAG,IAAI,eAAe;AAClF,kBAAM,WAAW,MAAM,UAAU,QAAQ,IAAI,MAAM,UAAU,GAAG,IAAI,eAAe;AACnF,gBAAI,gBAAgB,UAAU;AAC5B,8BAAgB;AAAA,YAClB,WAAW,gBAAgB,UAAU;AACnC,8BAAgB;AAAA,YAClB;AAAA,UACF;AACA,cAAI,gBAAgB;AAClB,gBAAI,uBAAuB;AAC3B,kBAAM,MAAM,aAAa,MAAM,UAAU;AACzC,kBAAM,eAAe,CAAC,OAAO,MAAM,EAAE,SAAS,QAAQ,SAAS,CAAC;AAChE,kBAAM,WAAW,MAAM,UAAU,SAAS,IAAI,MAAM,SAAS,GAAG,KAAK,iBAAiB,wBAAwB,eAAe,WAAW,OAAO,SAAS,sBAAsB,SAAS,MAAM,IAAI,MAAM,eAAe,IAAI,eAAe;AACzO,kBAAM,WAAW,MAAM,UAAU,SAAS,IAAI,MAAM,UAAU,GAAG,KAAK,eAAe,MAAM,yBAAyB,eAAe,WAAW,OAAO,SAAS,uBAAuB,SAAS,MAAM,MAAM,eAAe,eAAe,YAAY;AACpP,gBAAI,iBAAiB,UAAU;AAC7B,+BAAiB;AAAA,YACnB,WAAW,iBAAiB,UAAU;AACpC,+BAAiB;AAAA,YACnB;AAAA,UACF;AACA,iBAAO;AAAA,YACL,CAAC,QAAQ,GAAG;AAAA,YACZ,CAAC,SAAS,GAAG;AAAA,UACf;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAQA,IAAM,OAAO,SAAU,SAAS;AAC9B,UAAI,YAAY,QAAQ;AACtB,kBAAU,CAAC;AAAA,MACb;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN;AAAA,QACA,MAAM,GAAG,OAAO;AACd,cAAI,uBAAuB;AAC3B,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA,UAAAF;AAAA,YACA;AAAA,UACF,IAAI;AACJ,gBAAM;AAAA,YACJ,QAAQ,MAAM;AAAA,YAAC;AAAA,YACf,GAAG;AAAA,UACL,IAAI,SAAS,SAAS,KAAK;AAC3B,gBAAM,WAAW,MAAM,eAAe,OAAO,qBAAqB;AAClE,gBAAM,OAAO,QAAQ,SAAS;AAC9B,gBAAM,YAAY,aAAa,SAAS;AACxC,gBAAM,UAAU,YAAY,SAAS,MAAM;AAC3C,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI,MAAM;AACV,cAAI;AACJ,cAAI;AACJ,cAAI,SAAS,SAAS,SAAS,UAAU;AACvC,yBAAa;AACb,wBAAY,eAAgB,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,SAAS,QAAQ,KAAM,UAAU,SAAS,SAAS;AAAA,UACzI,OAAO;AACL,wBAAY;AACZ,yBAAa,cAAc,QAAQ,QAAQ;AAAA,UAC7C;AACA,gBAAM,wBAAwB,SAAS,SAAS,MAAM,SAAS;AAC/D,gBAAM,uBAAuB,QAAQ,SAAS,OAAO,SAAS;AAC9D,gBAAM,0BAA0B,IAAI,SAAS,SAAS,UAAU,GAAG,qBAAqB;AACxF,gBAAM,yBAAyB,IAAI,QAAQ,SAAS,SAAS,GAAG,oBAAoB;AACpF,gBAAM,UAAU,CAAC,MAAM,eAAe;AACtC,cAAI,kBAAkB;AACtB,cAAI,iBAAiB;AACrB,eAAK,wBAAwB,MAAM,eAAe,UAAU,QAAQ,sBAAsB,QAAQ,GAAG;AACnG,6BAAiB;AAAA,UACnB;AACA,eAAK,yBAAyB,MAAM,eAAe,UAAU,QAAQ,uBAAuB,QAAQ,GAAG;AACrG,8BAAkB;AAAA,UACpB;AACA,cAAI,WAAW,CAAC,WAAW;AACzB,kBAAM,OAAO,IAAI,SAAS,MAAM,CAAC;AACjC,kBAAM,OAAO,IAAI,SAAS,OAAO,CAAC;AAClC,kBAAM,OAAO,IAAI,SAAS,KAAK,CAAC;AAChC,kBAAM,OAAO,IAAI,SAAS,QAAQ,CAAC;AACnC,gBAAI,SAAS;AACX,+BAAiB,QAAQ,KAAK,SAAS,KAAK,SAAS,IAAI,OAAO,OAAO,IAAI,SAAS,MAAM,SAAS,KAAK;AAAA,YAC1G,OAAO;AACL,gCAAkB,SAAS,KAAK,SAAS,KAAK,SAAS,IAAI,OAAO,OAAO,IAAI,SAAS,KAAK,SAAS,MAAM;AAAA,YAC5G;AAAA,UACF;AACA,gBAAM,MAAM;AAAA,YACV,GAAG;AAAA,YACH;AAAA,YACA;AAAA,UACF,CAAC;AACD,gBAAM,iBAAiB,MAAMA,UAAS,cAAc,SAAS,QAAQ;AACrE,cAAI,UAAU,eAAe,SAAS,WAAW,eAAe,QAAQ;AACtE,mBAAO;AAAA,cACL,OAAO;AAAA,gBACL,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AACA,iBAAO,CAAC;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;AC9gCA,SAAS,YAAY;AACnB,SAAO,OAAO,WAAW;AAC3B;AACA,SAAS,YAAY,MAAM;AACzB,MAAI,OAAO,IAAI,GAAG;AAChB,YAAQ,KAAK,YAAY,IAAI,YAAY;AAAA,EAC3C;AAIA,SAAO;AACT;AACA,SAAS,UAAU,MAAM;AACvB,MAAI;AACJ,UAAQ,QAAQ,SAAS,sBAAsB,KAAK,kBAAkB,OAAO,SAAS,oBAAoB,gBAAgB;AAC5H;AACA,SAAS,mBAAmB,MAAM;AAChC,MAAI;AACJ,UAAQ,QAAQ,OAAO,IAAI,IAAI,KAAK,gBAAgB,KAAK,aAAa,OAAO,aAAa,OAAO,SAAS,KAAK;AACjH;AACA,SAAS,OAAO,OAAO;AACrB,MAAI,CAAC,UAAU,GAAG;AAChB,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,QAAQ,iBAAiB,UAAU,KAAK,EAAE;AACpE;AACA,SAAS,UAAU,OAAO;AACxB,MAAI,CAAC,UAAU,GAAG;AAChB,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,WAAW,iBAAiB,UAAU,KAAK,EAAE;AACvE;AACA,SAAS,cAAc,OAAO;AAC5B,MAAI,CAAC,UAAU,GAAG;AAChB,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,eAAe,iBAAiB,UAAU,KAAK,EAAE;AAC3E;AACA,SAAS,aAAa,OAAO;AAC3B,MAAI,CAAC,UAAU,KAAK,OAAO,eAAe,aAAa;AACrD,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,cAAc,iBAAiB,UAAU,KAAK,EAAE;AAC1E;AACA,SAAS,kBAAkB,SAAS;AAClC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB,OAAO;AAC5B,SAAO,kCAAkC,KAAK,WAAW,YAAY,SAAS,KAAK,CAAC,CAAC,UAAU,UAAU,EAAE,SAAS,OAAO;AAC7H;AACA,SAAS,eAAe,SAAS;AAC/B,SAAO,CAAC,SAAS,MAAM,IAAI,EAAE,SAAS,YAAY,OAAO,CAAC;AAC5D;AACA,SAAS,WAAW,SAAS;AAC3B,SAAO,CAAC,iBAAiB,QAAQ,EAAE,KAAK,cAAY;AAClD,QAAI;AACF,aAAO,QAAQ,QAAQ,QAAQ;AAAA,IACjC,SAAS,GAAG;AACV,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACH;AACA,SAAS,kBAAkB,cAAc;AACvC,QAAM,SAAS,SAAS;AACxB,QAAM,MAAM,UAAU,YAAY,IAAI,iBAAiB,YAAY,IAAI;AAIvE,SAAO,CAAC,aAAa,aAAa,SAAS,UAAU,aAAa,EAAE,KAAK,WAAS,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,SAAS,KAAK,MAAM,IAAI,gBAAgB,IAAI,kBAAkB,WAAW,UAAU,CAAC,WAAW,IAAI,iBAAiB,IAAI,mBAAmB,SAAS,UAAU,CAAC,WAAW,IAAI,SAAS,IAAI,WAAW,SAAS,UAAU,CAAC,aAAa,aAAa,SAAS,UAAU,eAAe,QAAQ,EAAE,KAAK,YAAU,IAAI,cAAc,IAAI,SAAS,KAAK,CAAC,KAAK,CAAC,SAAS,UAAU,UAAU,SAAS,EAAE,KAAK,YAAU,IAAI,WAAW,IAAI,SAAS,KAAK,CAAC;AACniB;AACA,SAAS,mBAAmB,SAAS;AACnC,MAAI,cAAc,cAAc,OAAO;AACvC,SAAO,cAAc,WAAW,KAAK,CAAC,sBAAsB,WAAW,GAAG;AACxE,QAAI,kBAAkB,WAAW,GAAG;AAClC,aAAO;AAAA,IACT,WAAW,WAAW,WAAW,GAAG;AAClC,aAAO;AAAA,IACT;AACA,kBAAc,cAAc,WAAW;AAAA,EACzC;AACA,SAAO;AACT;AACA,SAAS,WAAW;AAClB,MAAI,OAAO,QAAQ,eAAe,CAAC,IAAI,SAAU,QAAO;AACxD,SAAO,IAAI,SAAS,2BAA2B,MAAM;AACvD;AACA,SAAS,sBAAsB,MAAM;AACnC,SAAO,CAAC,QAAQ,QAAQ,WAAW,EAAE,SAAS,YAAY,IAAI,CAAC;AACjE;AACA,SAAS,iBAAiB,SAAS;AACjC,SAAO,UAAU,OAAO,EAAE,iBAAiB,OAAO;AACpD;AACA,SAAS,cAAc,SAAS;AAC9B,MAAI,UAAU,OAAO,GAAG;AACtB,WAAO;AAAA,MACL,YAAY,QAAQ;AAAA,MACpB,WAAW,QAAQ;AAAA,IACrB;AAAA,EACF;AACA,SAAO;AAAA,IACL,YAAY,QAAQ;AAAA,IACpB,WAAW,QAAQ;AAAA,EACrB;AACF;AACA,SAAS,cAAc,MAAM;AAC3B,MAAI,YAAY,IAAI,MAAM,QAAQ;AAChC,WAAO;AAAA,EACT;AACA,QAAM;AAAA;AAAA,IAEN,KAAK;AAAA,IAEL,KAAK;AAAA,IAEL,aAAa,IAAI,KAAK,KAAK;AAAA,IAE3B,mBAAmB,IAAI;AAAA;AACvB,SAAO,aAAa,MAAM,IAAI,OAAO,OAAO;AAC9C;AACA,SAAS,2BAA2B,MAAM;AACxC,QAAM,aAAa,cAAc,IAAI;AACrC,MAAI,sBAAsB,UAAU,GAAG;AACrC,WAAO,KAAK,gBAAgB,KAAK,cAAc,OAAO,KAAK;AAAA,EAC7D;AACA,MAAI,cAAc,UAAU,KAAK,kBAAkB,UAAU,GAAG;AAC9D,WAAO;AAAA,EACT;AACA,SAAO,2BAA2B,UAAU;AAC9C;AACA,SAAS,qBAAqB,MAAM,MAAM,iBAAiB;AACzD,MAAI;AACJ,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EACV;AACA,MAAI,oBAAoB,QAAQ;AAC9B,sBAAkB;AAAA,EACpB;AACA,QAAM,qBAAqB,2BAA2B,IAAI;AAC1D,QAAM,SAAS,yBAAyB,uBAAuB,KAAK,kBAAkB,OAAO,SAAS,qBAAqB;AAC3H,QAAM,MAAM,UAAU,kBAAkB;AACxC,MAAI,QAAQ;AACV,UAAM,eAAe,gBAAgB,GAAG;AACxC,WAAO,KAAK,OAAO,KAAK,IAAI,kBAAkB,CAAC,GAAG,kBAAkB,kBAAkB,IAAI,qBAAqB,CAAC,GAAG,gBAAgB,kBAAkB,qBAAqB,YAAY,IAAI,CAAC,CAAC;AAAA,EAC9L;AACA,SAAO,KAAK,OAAO,oBAAoB,qBAAqB,oBAAoB,CAAC,GAAG,eAAe,CAAC;AACtG;AACA,SAAS,gBAAgB,KAAK;AAC5B,SAAO,IAAI,UAAU,OAAO,eAAe,IAAI,MAAM,IAAI,IAAI,eAAe;AAC9E;AAvJA;AAAA;AAAA;AAAA;;;ACKA,SAAS,iBAAiB,SAAS;AACjC,QAAM,MAAM,iBAAiB,OAAO;AAGpC,MAAI,QAAQ,WAAW,IAAI,KAAK,KAAK;AACrC,MAAI,SAAS,WAAW,IAAI,MAAM,KAAK;AACvC,QAAM,YAAY,cAAc,OAAO;AACvC,QAAM,cAAc,YAAY,QAAQ,cAAc;AACtD,QAAM,eAAe,YAAY,QAAQ,eAAe;AACxD,QAAM,iBAAiB,MAAM,KAAK,MAAM,eAAe,MAAM,MAAM,MAAM;AACzE,MAAI,gBAAgB;AAClB,YAAQ;AACR,aAAS;AAAA,EACX;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL;AACF;AAEA,SAAS,cAAc,SAAS;AAC9B,SAAO,CAAC,UAAU,OAAO,IAAI,QAAQ,iBAAiB;AACxD;AAEA,SAAS,SAAS,SAAS;AACzB,QAAM,aAAa,cAAc,OAAO;AACxC,MAAI,CAAC,cAAc,UAAU,GAAG;AAC9B,WAAO,aAAa,CAAC;AAAA,EACvB;AACA,QAAM,OAAO,WAAW,sBAAsB;AAC9C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB,UAAU;AAC/B,MAAI,KAAK,IAAI,MAAM,KAAK,KAAK,IAAI,KAAK,SAAS;AAC/C,MAAI,KAAK,IAAI,MAAM,KAAK,MAAM,IAAI,KAAK,UAAU;AAIjD,MAAI,CAAC,KAAK,CAAC,OAAO,SAAS,CAAC,GAAG;AAC7B,QAAI;AAAA,EACN;AACA,MAAI,CAAC,KAAK,CAAC,OAAO,SAAS,CAAC,GAAG;AAC7B,QAAI;AAAA,EACN;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAGA,SAAS,iBAAiB,SAAS;AACjC,QAAM,MAAM,UAAU,OAAO;AAC7B,MAAI,CAAC,SAAS,KAAK,CAAC,IAAI,gBAAgB;AACtC,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL,GAAG,IAAI,eAAe;AAAA,IACtB,GAAG,IAAI,eAAe;AAAA,EACxB;AACF;AACA,SAAS,uBAAuB,SAAS,SAAS,sBAAsB;AACtE,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AACA,MAAI,CAAC,wBAAwB,WAAW,yBAAyB,UAAU,OAAO,GAAG;AACnF,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,sBAAsB,SAAS,cAAc,iBAAiB,cAAc;AACnF,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACjB;AACA,MAAI,oBAAoB,QAAQ;AAC9B,sBAAkB;AAAA,EACpB;AACA,QAAM,aAAa,QAAQ,sBAAsB;AACjD,QAAM,aAAa,cAAc,OAAO;AACxC,MAAI,QAAQ,aAAa,CAAC;AAC1B,MAAI,cAAc;AAChB,QAAI,cAAc;AAChB,UAAI,UAAU,YAAY,GAAG;AAC3B,gBAAQ,SAAS,YAAY;AAAA,MAC/B;AAAA,IACF,OAAO;AACL,cAAQ,SAAS,OAAO;AAAA,IAC1B;AAAA,EACF;AACA,QAAM,gBAAgB,uBAAuB,YAAY,iBAAiB,YAAY,IAAI,iBAAiB,UAAU,IAAI,aAAa,CAAC;AACvI,MAAI,KAAK,WAAW,OAAO,cAAc,KAAK,MAAM;AACpD,MAAI,KAAK,WAAW,MAAM,cAAc,KAAK,MAAM;AACnD,MAAI,QAAQ,WAAW,QAAQ,MAAM;AACrC,MAAI,SAAS,WAAW,SAAS,MAAM;AACvC,MAAI,YAAY;AACd,UAAM,MAAM,UAAU,UAAU;AAChC,UAAM,YAAY,gBAAgB,UAAU,YAAY,IAAI,UAAU,YAAY,IAAI;AACtF,QAAI,aAAa;AACjB,QAAI,gBAAgB,gBAAgB,UAAU;AAC9C,WAAO,iBAAiB,gBAAgB,cAAc,YAAY;AAChE,YAAM,cAAc,SAAS,aAAa;AAC1C,YAAM,aAAa,cAAc,sBAAsB;AACvD,YAAM,MAAM,iBAAiB,aAAa;AAC1C,YAAM,OAAO,WAAW,QAAQ,cAAc,aAAa,WAAW,IAAI,WAAW,KAAK,YAAY;AACtG,YAAM,MAAM,WAAW,OAAO,cAAc,YAAY,WAAW,IAAI,UAAU,KAAK,YAAY;AAClG,WAAK,YAAY;AACjB,WAAK,YAAY;AACjB,eAAS,YAAY;AACrB,gBAAU,YAAY;AACtB,WAAK;AACL,WAAK;AACL,mBAAa,UAAU,aAAa;AACpC,sBAAgB,gBAAgB,UAAU;AAAA,IAC5C;AAAA,EACF;AACA,SAAO,iBAAiB;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAIA,SAAS,oBAAoB,SAAS,MAAM;AAC1C,QAAM,aAAa,cAAc,OAAO,EAAE;AAC1C,MAAI,CAAC,MAAM;AACT,WAAO,sBAAsB,mBAAmB,OAAO,CAAC,EAAE,OAAO;AAAA,EACnE;AACA,SAAO,KAAK,OAAO;AACrB;AAEA,SAAS,cAAc,iBAAiB,QAAQ,kBAAkB;AAChE,MAAI,qBAAqB,QAAQ;AAC/B,uBAAmB;AAAA,EACrB;AACA,QAAM,WAAW,gBAAgB,sBAAsB;AACvD,QAAM,IAAI,SAAS,OAAO,OAAO,cAAc,mBAAmB;AAAA;AAAA,IAElE,oBAAoB,iBAAiB,QAAQ;AAAA;AAC7C,QAAM,IAAI,SAAS,MAAM,OAAO;AAChC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,sDAAsD,MAAM;AACnE,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,UAAU,aAAa;AAC7B,QAAM,kBAAkB,mBAAmB,YAAY;AACvD,QAAM,WAAW,WAAW,WAAW,SAAS,QAAQ,IAAI;AAC5D,MAAI,iBAAiB,mBAAmB,YAAY,SAAS;AAC3D,WAAO;AAAA,EACT;AACA,MAAI,SAAS;AAAA,IACX,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AACA,MAAI,QAAQ,aAAa,CAAC;AAC1B,QAAM,UAAU,aAAa,CAAC;AAC9B,QAAM,0BAA0B,cAAc,YAAY;AAC1D,MAAI,2BAA2B,CAAC,2BAA2B,CAAC,SAAS;AACnE,QAAI,YAAY,YAAY,MAAM,UAAU,kBAAkB,eAAe,GAAG;AAC9E,eAAS,cAAc,YAAY;AAAA,IACrC;AACA,QAAI,cAAc,YAAY,GAAG;AAC/B,YAAM,aAAa,sBAAsB,YAAY;AACrD,cAAQ,SAAS,YAAY;AAC7B,cAAQ,IAAI,WAAW,IAAI,aAAa;AACxC,cAAQ,IAAI,WAAW,IAAI,aAAa;AAAA,IAC1C;AAAA,EACF;AACA,QAAM,aAAa,mBAAmB,CAAC,2BAA2B,CAAC,UAAU,cAAc,iBAAiB,QAAQ,IAAI,IAAI,aAAa,CAAC;AAC1I,SAAO;AAAA,IACL,OAAO,KAAK,QAAQ,MAAM;AAAA,IAC1B,QAAQ,KAAK,SAAS,MAAM;AAAA,IAC5B,GAAG,KAAK,IAAI,MAAM,IAAI,OAAO,aAAa,MAAM,IAAI,QAAQ,IAAI,WAAW;AAAA,IAC3E,GAAG,KAAK,IAAI,MAAM,IAAI,OAAO,YAAY,MAAM,IAAI,QAAQ,IAAI,WAAW;AAAA,EAC5E;AACF;AAEA,SAAS,eAAe,SAAS;AAC/B,SAAO,MAAM,KAAK,QAAQ,eAAe,CAAC;AAC5C;AAIA,SAAS,gBAAgB,SAAS;AAChC,QAAM,OAAO,mBAAmB,OAAO;AACvC,QAAM,SAAS,cAAc,OAAO;AACpC,QAAM,OAAO,QAAQ,cAAc;AACnC,QAAM,QAAQ,IAAI,KAAK,aAAa,KAAK,aAAa,KAAK,aAAa,KAAK,WAAW;AACxF,QAAM,SAAS,IAAI,KAAK,cAAc,KAAK,cAAc,KAAK,cAAc,KAAK,YAAY;AAC7F,MAAI,IAAI,CAAC,OAAO,aAAa,oBAAoB,OAAO;AACxD,QAAM,IAAI,CAAC,OAAO;AAClB,MAAI,iBAAiB,IAAI,EAAE,cAAc,OAAO;AAC9C,SAAK,IAAI,KAAK,aAAa,KAAK,WAAW,IAAI;AAAA,EACjD;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,gBAAgB,SAAS,UAAU;AAC1C,QAAM,MAAM,UAAU,OAAO;AAC7B,QAAM,OAAO,mBAAmB,OAAO;AACvC,QAAM,iBAAiB,IAAI;AAC3B,MAAI,QAAQ,KAAK;AACjB,MAAI,SAAS,KAAK;AAClB,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,gBAAgB;AAClB,YAAQ,eAAe;AACvB,aAAS,eAAe;AACxB,UAAM,sBAAsB,SAAS;AACrC,QAAI,CAAC,uBAAuB,uBAAuB,aAAa,SAAS;AACvE,UAAI,eAAe;AACnB,UAAI,eAAe;AAAA,IACrB;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAGA,SAAS,2BAA2B,SAAS,UAAU;AACrD,QAAM,aAAa,sBAAsB,SAAS,MAAM,aAAa,OAAO;AAC5E,QAAM,MAAM,WAAW,MAAM,QAAQ;AACrC,QAAM,OAAO,WAAW,OAAO,QAAQ;AACvC,QAAM,QAAQ,cAAc,OAAO,IAAI,SAAS,OAAO,IAAI,aAAa,CAAC;AACzE,QAAM,QAAQ,QAAQ,cAAc,MAAM;AAC1C,QAAM,SAAS,QAAQ,eAAe,MAAM;AAC5C,QAAM,IAAI,OAAO,MAAM;AACvB,QAAM,IAAI,MAAM,MAAM;AACtB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,kCAAkC,SAAS,kBAAkB,UAAU;AAC9E,MAAI;AACJ,MAAI,qBAAqB,YAAY;AACnC,WAAO,gBAAgB,SAAS,QAAQ;AAAA,EAC1C,WAAW,qBAAqB,YAAY;AAC1C,WAAO,gBAAgB,mBAAmB,OAAO,CAAC;AAAA,EACpD,WAAW,UAAU,gBAAgB,GAAG;AACtC,WAAO,2BAA2B,kBAAkB,QAAQ;AAAA,EAC9D,OAAO;AACL,UAAM,gBAAgB,iBAAiB,OAAO;AAC9C,WAAO;AAAA,MACL,GAAG,iBAAiB,IAAI,cAAc;AAAA,MACtC,GAAG,iBAAiB,IAAI,cAAc;AAAA,MACtC,OAAO,iBAAiB;AAAA,MACxB,QAAQ,iBAAiB;AAAA,IAC3B;AAAA,EACF;AACA,SAAO,iBAAiB,IAAI;AAC9B;AACA,SAAS,yBAAyB,SAAS,UAAU;AACnD,QAAM,aAAa,cAAc,OAAO;AACxC,MAAI,eAAe,YAAY,CAAC,UAAU,UAAU,KAAK,sBAAsB,UAAU,GAAG;AAC1F,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,UAAU,EAAE,aAAa,WAAW,yBAAyB,YAAY,QAAQ;AAC3G;AAKA,SAAS,4BAA4B,SAAS,OAAO;AACnD,QAAM,eAAe,MAAM,IAAI,OAAO;AACtC,MAAI,cAAc;AAChB,WAAO;AAAA,EACT;AACA,MAAI,SAAS,qBAAqB,SAAS,CAAC,GAAG,KAAK,EAAE,OAAO,QAAM,UAAU,EAAE,KAAK,YAAY,EAAE,MAAM,MAAM;AAC9G,MAAI,sCAAsC;AAC1C,QAAM,iBAAiB,iBAAiB,OAAO,EAAE,aAAa;AAC9D,MAAI,cAAc,iBAAiB,cAAc,OAAO,IAAI;AAG5D,SAAO,UAAU,WAAW,KAAK,CAAC,sBAAsB,WAAW,GAAG;AACpE,UAAM,gBAAgB,iBAAiB,WAAW;AAClD,UAAM,0BAA0B,kBAAkB,WAAW;AAC7D,QAAI,CAAC,2BAA2B,cAAc,aAAa,SAAS;AAClE,4CAAsC;AAAA,IACxC;AACA,UAAM,wBAAwB,iBAAiB,CAAC,2BAA2B,CAAC,sCAAsC,CAAC,2BAA2B,cAAc,aAAa,YAAY,CAAC,CAAC,uCAAuC,CAAC,YAAY,OAAO,EAAE,SAAS,oCAAoC,QAAQ,KAAK,kBAAkB,WAAW,KAAK,CAAC,2BAA2B,yBAAyB,SAAS,WAAW;AACzZ,QAAI,uBAAuB;AAEzB,eAAS,OAAO,OAAO,cAAY,aAAa,WAAW;AAAA,IAC7D,OAAO;AAEL,4CAAsC;AAAA,IACxC;AACA,kBAAc,cAAc,WAAW;AAAA,EACzC;AACA,QAAM,IAAI,SAAS,MAAM;AACzB,SAAO;AACT;AAIA,SAAS,gBAAgB,MAAM;AAC7B,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,2BAA2B,aAAa,sBAAsB,WAAW,OAAO,IAAI,CAAC,IAAI,4BAA4B,SAAS,KAAK,EAAE,IAAI,CAAC,EAAE,OAAO,QAAQ;AACjK,QAAM,oBAAoB,CAAC,GAAG,0BAA0B,YAAY;AACpE,QAAM,wBAAwB,kBAAkB,CAAC;AACjD,QAAM,eAAe,kBAAkB,OAAO,CAAC,SAAS,qBAAqB;AAC3E,UAAM,OAAO,kCAAkC,SAAS,kBAAkB,QAAQ;AAClF,YAAQ,MAAM,IAAI,KAAK,KAAK,QAAQ,GAAG;AACvC,YAAQ,QAAQ,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC7C,YAAQ,SAAS,IAAI,KAAK,QAAQ,QAAQ,MAAM;AAChD,YAAQ,OAAO,IAAI,KAAK,MAAM,QAAQ,IAAI;AAC1C,WAAO;AAAA,EACT,GAAG,kCAAkC,SAAS,uBAAuB,QAAQ,CAAC;AAC9E,SAAO;AAAA,IACL,OAAO,aAAa,QAAQ,aAAa;AAAA,IACzC,QAAQ,aAAa,SAAS,aAAa;AAAA,IAC3C,GAAG,aAAa;AAAA,IAChB,GAAG,aAAa;AAAA,EAClB;AACF;AAEA,SAAS,cAAc,SAAS;AAC9B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB,OAAO;AAC5B,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,8BAA8B,SAAS,cAAc,UAAU;AACtE,QAAM,0BAA0B,cAAc,YAAY;AAC1D,QAAM,kBAAkB,mBAAmB,YAAY;AACvD,QAAM,UAAU,aAAa;AAC7B,QAAM,OAAO,sBAAsB,SAAS,MAAM,SAAS,YAAY;AACvE,MAAI,SAAS;AAAA,IACX,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AACA,QAAM,UAAU,aAAa,CAAC;AAC9B,MAAI,2BAA2B,CAAC,2BAA2B,CAAC,SAAS;AACnE,QAAI,YAAY,YAAY,MAAM,UAAU,kBAAkB,eAAe,GAAG;AAC9E,eAAS,cAAc,YAAY;AAAA,IACrC;AACA,QAAI,yBAAyB;AAC3B,YAAM,aAAa,sBAAsB,cAAc,MAAM,SAAS,YAAY;AAClF,cAAQ,IAAI,WAAW,IAAI,aAAa;AACxC,cAAQ,IAAI,WAAW,IAAI,aAAa;AAAA,IAC1C,WAAW,iBAAiB;AAG1B,cAAQ,IAAI,oBAAoB,eAAe;AAAA,IACjD;AAAA,EACF;AACA,QAAM,aAAa,mBAAmB,CAAC,2BAA2B,CAAC,UAAU,cAAc,iBAAiB,MAAM,IAAI,aAAa,CAAC;AACpI,QAAM,IAAI,KAAK,OAAO,OAAO,aAAa,QAAQ,IAAI,WAAW;AACjE,QAAM,IAAI,KAAK,MAAM,OAAO,YAAY,QAAQ,IAAI,WAAW;AAC/D,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,OAAO,KAAK;AAAA,IACZ,QAAQ,KAAK;AAAA,EACf;AACF;AAEA,SAAS,mBAAmB,SAAS;AACnC,SAAO,iBAAiB,OAAO,EAAE,aAAa;AAChD;AAEA,SAAS,oBAAoB,SAAS,UAAU;AAC9C,MAAI,CAAC,cAAc,OAAO,KAAK,iBAAiB,OAAO,EAAE,aAAa,SAAS;AAC7E,WAAO;AAAA,EACT;AACA,MAAI,UAAU;AACZ,WAAO,SAAS,OAAO;AAAA,EACzB;AACA,MAAI,kBAAkB,QAAQ;AAM9B,MAAI,mBAAmB,OAAO,MAAM,iBAAiB;AACnD,sBAAkB,gBAAgB,cAAc;AAAA,EAClD;AACA,SAAO;AACT;AAIA,SAAS,gBAAgB,SAAS,UAAU;AAC1C,QAAM,MAAM,UAAU,OAAO;AAC7B,MAAI,WAAW,OAAO,GAAG;AACvB,WAAO;AAAA,EACT;AACA,MAAI,CAAC,cAAc,OAAO,GAAG;AAC3B,QAAI,kBAAkB,cAAc,OAAO;AAC3C,WAAO,mBAAmB,CAAC,sBAAsB,eAAe,GAAG;AACjE,UAAI,UAAU,eAAe,KAAK,CAAC,mBAAmB,eAAe,GAAG;AACtE,eAAO;AAAA,MACT;AACA,wBAAkB,cAAc,eAAe;AAAA,IACjD;AACA,WAAO;AAAA,EACT;AACA,MAAI,eAAe,oBAAoB,SAAS,QAAQ;AACxD,SAAO,gBAAgB,eAAe,YAAY,KAAK,mBAAmB,YAAY,GAAG;AACvF,mBAAe,oBAAoB,cAAc,QAAQ;AAAA,EAC3D;AACA,MAAI,gBAAgB,sBAAsB,YAAY,KAAK,mBAAmB,YAAY,KAAK,CAAC,kBAAkB,YAAY,GAAG;AAC/H,WAAO;AAAA,EACT;AACA,SAAO,gBAAgB,mBAAmB,OAAO,KAAK;AACxD;AAiBA,SAAS,MAAM,SAAS;AACtB,SAAO,iBAAiB,OAAO,EAAE,cAAc;AACjD;AAeA,SAAS,cAAc,GAAG,GAAG;AAC3B,SAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE;AAC7E;AAGA,SAAS,YAAY,SAAS,QAAQ;AACpC,MAAI,KAAK;AACT,MAAI;AACJ,QAAM,OAAO,mBAAmB,OAAO;AACvC,WAAS,UAAU;AACjB,QAAI;AACJ,iBAAa,SAAS;AACtB,KAAC,MAAM,OAAO,QAAQ,IAAI,WAAW;AACrC,SAAK;AAAA,EACP;AACA,WAAS,QAAQ,MAAM,WAAW;AAChC,QAAI,SAAS,QAAQ;AACnB,aAAO;AAAA,IACT;AACA,QAAI,cAAc,QAAQ;AACxB,kBAAY;AAAA,IACd;AACA,YAAQ;AACR,UAAM,2BAA2B,QAAQ,sBAAsB;AAC/D,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AACA,QAAI,CAAC,SAAS,CAAC,QAAQ;AACrB;AAAA,IACF;AACA,UAAM,WAAW,MAAM,GAAG;AAC1B,UAAM,aAAa,MAAM,KAAK,eAAe,OAAO,MAAM;AAC1D,UAAM,cAAc,MAAM,KAAK,gBAAgB,MAAM,OAAO;AAC5D,UAAM,YAAY,MAAM,IAAI;AAC5B,UAAM,aAAa,CAAC,WAAW,QAAQ,CAAC,aAAa,QAAQ,CAAC,cAAc,QAAQ,CAAC,YAAY;AACjG,UAAM,UAAU;AAAA,MACd;AAAA,MACA,WAAW,IAAI,GAAG,IAAI,GAAG,SAAS,CAAC,KAAK;AAAA,IAC1C;AACA,QAAI,gBAAgB;AACpB,aAAS,cAAc,SAAS;AAC9B,YAAM,QAAQ,QAAQ,CAAC,EAAE;AACzB,UAAI,UAAU,WAAW;AACvB,YAAI,CAAC,eAAe;AAClB,iBAAO,QAAQ;AAAA,QACjB;AACA,YAAI,CAAC,OAAO;AAGV,sBAAY,WAAW,MAAM;AAC3B,oBAAQ,OAAO,IAAI;AAAA,UACrB,GAAG,GAAI;AAAA,QACT,OAAO;AACL,kBAAQ,OAAO,KAAK;AAAA,QACtB;AAAA,MACF;AACA,UAAI,UAAU,KAAK,CAAC,cAAc,0BAA0B,QAAQ,sBAAsB,CAAC,GAAG;AAQ5F,gBAAQ;AAAA,MACV;AACA,sBAAgB;AAAA,IAClB;AAIA,QAAI;AACF,WAAK,IAAI,qBAAqB,eAAe;AAAA,QAC3C,GAAG;AAAA;AAAA,QAEH,MAAM,KAAK;AAAA,MACb,CAAC;AAAA,IACH,SAAS,GAAG;AACV,WAAK,IAAI,qBAAqB,eAAe,OAAO;AAAA,IACtD;AACA,OAAG,QAAQ,OAAO;AAAA,EACpB;AACA,UAAQ,IAAI;AACZ,SAAO;AACT;AAUA,SAAS,WAAW,WAAW,UAAU,QAAQ,SAAS;AACxD,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,QAAM;AAAA,IACJ,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,gBAAgB,OAAO,mBAAmB;AAAA,IAC1C,cAAc,OAAO,yBAAyB;AAAA,IAC9C,iBAAiB;AAAA,EACnB,IAAI;AACJ,QAAM,cAAc,cAAc,SAAS;AAC3C,QAAM,YAAY,kBAAkB,iBAAiB,CAAC,GAAI,cAAc,qBAAqB,WAAW,IAAI,CAAC,GAAI,GAAG,qBAAqB,QAAQ,CAAC,IAAI,CAAC;AACvJ,YAAU,QAAQ,cAAY;AAC5B,sBAAkB,SAAS,iBAAiB,UAAU,QAAQ;AAAA,MAC5D,SAAS;AAAA,IACX,CAAC;AACD,sBAAkB,SAAS,iBAAiB,UAAU,MAAM;AAAA,EAC9D,CAAC;AACD,QAAM,YAAY,eAAe,cAAc,YAAY,aAAa,MAAM,IAAI;AAClF,MAAI,iBAAiB;AACrB,MAAI,iBAAiB;AACrB,MAAI,eAAe;AACjB,qBAAiB,IAAI,eAAe,UAAQ;AAC1C,UAAI,CAAC,UAAU,IAAI;AACnB,UAAI,cAAc,WAAW,WAAW,eAAe,gBAAgB;AAGrE,uBAAe,UAAU,QAAQ;AACjC,6BAAqB,cAAc;AACnC,yBAAiB,sBAAsB,MAAM;AAC3C,cAAI;AACJ,WAAC,kBAAkB,mBAAmB,QAAQ,gBAAgB,QAAQ,QAAQ;AAAA,QAChF,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT,CAAC;AACD,QAAI,eAAe,CAAC,gBAAgB;AAClC,qBAAe,QAAQ,WAAW;AAAA,IACpC;AACA,mBAAe,QAAQ,QAAQ;AAAA,EACjC;AACA,MAAI;AACJ,MAAI,cAAc,iBAAiB,sBAAsB,SAAS,IAAI;AACtE,MAAI,gBAAgB;AAClB,cAAU;AAAA,EACZ;AACA,WAAS,YAAY;AACnB,UAAM,cAAc,sBAAsB,SAAS;AACnD,QAAI,eAAe,CAAC,cAAc,aAAa,WAAW,GAAG;AAC3D,aAAO;AAAA,IACT;AACA,kBAAc;AACd,cAAU,sBAAsB,SAAS;AAAA,EAC3C;AACA,SAAO;AACP,SAAO,MAAM;AACX,QAAI;AACJ,cAAU,QAAQ,cAAY;AAC5B,wBAAkB,SAAS,oBAAoB,UAAU,MAAM;AAC/D,wBAAkB,SAAS,oBAAoB,UAAU,MAAM;AAAA,IACjE,CAAC;AACD,iBAAa,QAAQ,UAAU;AAC/B,KAAC,mBAAmB,mBAAmB,QAAQ,iBAAiB,WAAW;AAC3E,qBAAiB;AACjB,QAAI,gBAAgB;AAClB,2BAAqB,OAAO;AAAA,IAC9B;AAAA,EACF;AACF;AA1oBA,IA0DM,WAuYA,iBAmBA,UAgMAmB,iBASAC,SAQAC,gBAOAC,QAQAC,OAQAC,OAOAC,OAOAC,QAOAC,SAKAC,aAMAC;AA5tBN;AAAA;AAAA;AACA;AACA;AACA;AAuDA,IAAM,YAAyB,aAAa,CAAC;AAuY7C,IAAM,kBAAkB,eAAgB,MAAM;AAC5C,YAAM,oBAAoB,KAAK,mBAAmB;AAClD,YAAM,kBAAkB,KAAK;AAC7B,YAAM,qBAAqB,MAAM,gBAAgB,KAAK,QAAQ;AAC9D,aAAO;AAAA,QACL,WAAW,8BAA8B,KAAK,WAAW,MAAM,kBAAkB,KAAK,QAAQ,GAAG,KAAK,QAAQ;AAAA,QAC9G,UAAU;AAAA,UACR,GAAG;AAAA,UACH,GAAG;AAAA,UACH,OAAO,mBAAmB;AAAA,UAC1B,QAAQ,mBAAmB;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AAMA,IAAM,WAAW;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAqLA,IAAMV,kBAAiB;AASvB,IAAMC,UAAS;AAQf,IAAMC,iBAAgB;AAOtB,IAAMC,SAAQ;AAQd,IAAMC,QAAO;AAQb,IAAMC,QAAO;AAOb,IAAMC,QAAO;AAOb,IAAMC,SAAQ;AAOd,IAAMC,UAAS;AAKf,IAAMC,cAAa;AAMnB,IAAMC,mBAAkB,CAAC,WAAW,UAAU,YAAY;AAIxD,YAAM,QAAQ,oBAAI,IAAI;AACtB,YAAM,gBAAgB;AAAA,QACpB;AAAA,QACA,GAAG;AAAA,MACL;AACA,YAAM,oBAAoB;AAAA,QACxB,GAAG,cAAc;AAAA,QACjB,IAAI;AAAA,MACN;AACA,aAAO,gBAAkB,WAAW,UAAU;AAAA,QAC5C,GAAG;AAAA,QACH,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AAAA;AAAA;", "names": ["platform", "max", "offset", "alignment", "placements", "sides", "side", "placement", "overflow", "getBoundingClientRect", "top", "bottom", "left", "right", "width", "height", "x", "y", "min", "detectOverflow", "offset", "autoPlacement", "shift", "flip", "size", "hide", "arrow", "inline", "limitShift", "computePosition"]}