<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Purchase extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'date_of_purchase',
        'bill_number',
        'invoice_number',
        'payment_method',
        'cheque_no',
        'bank_name',
        'issue_date',
        'bank',
        'supplier_id',
        'store_id',
        'total',
        'paid_amount',
        'balance_amount',
        'discount_percentage',
        'discount_amount',
        'tax',
        'status',
        'deleted_by',
    ];

    protected $casts = [
        'date_of_purchase' => 'date',
        'issue_date' => 'date',
    ];

    public function supplier()
    {
        return $this->belongsTo(Supplier::class, 'supplier_id');
    }

    public function store()
    {
        return $this->belongsTo(StoreLocation::class, 'store_id');
    }

    public function items()
    {
        return $this->hasMany(PurchaseItem::class);
    }

    public function itemsWithTrashed()
    {
        return $this->hasMany(PurchaseItem::class)->withTrashed();
    }

    public function deletedByUser()
    {
        return $this->belongsTo(User::class, 'deleted_by')->withTrashed();
    }
}
