{"version": 3, "sources": ["../../react-is/cjs/react-is.development.js", "../../react-is/index.js"], "sourcesContent": ["/** @license React v16.13.1\r\n * react-is.development.js\r\n *\r\n * Copyright (c) Facebook, Inc. and its affiliates.\r\n *\r\n * This source code is licensed under the MIT license found in the\r\n * LICENSE file in the root directory of this source tree.\r\n */\r\n\r\n'use strict';\r\n\r\n\r\n\r\nif (process.env.NODE_ENV !== \"production\") {\r\n  (function() {\r\n'use strict';\r\n\r\n// The Symbol used to tag the ReactElement-like types. If there is no native Symbol\r\n// nor polyfill, then a plain number is used for performance.\r\nvar hasSymbol = typeof Symbol === 'function' && Symbol.for;\r\nvar REACT_ELEMENT_TYPE = hasSymbol ? Symbol.for('react.element') : 0xeac7;\r\nvar REACT_PORTAL_TYPE = hasSymbol ? Symbol.for('react.portal') : 0xeaca;\r\nvar REACT_FRAGMENT_TYPE = hasSymbol ? Symbol.for('react.fragment') : 0xeacb;\r\nvar REACT_STRICT_MODE_TYPE = hasSymbol ? Symbol.for('react.strict_mode') : 0xeacc;\r\nvar REACT_PROFILER_TYPE = hasSymbol ? Symbol.for('react.profiler') : 0xead2;\r\nvar REACT_PROVIDER_TYPE = hasSymbol ? Symbol.for('react.provider') : 0xeacd;\r\nvar REACT_CONTEXT_TYPE = hasSymbol ? Symbol.for('react.context') : 0xeace; // TODO: We don't use AsyncMode or ConcurrentMode anymore. They were temporary\r\n// (unstable) APIs that have been removed. Can we remove the symbols?\r\n\r\nvar REACT_ASYNC_MODE_TYPE = hasSymbol ? Symbol.for('react.async_mode') : 0xeacf;\r\nvar REACT_CONCURRENT_MODE_TYPE = hasSymbol ? Symbol.for('react.concurrent_mode') : 0xeacf;\r\nvar REACT_FORWARD_REF_TYPE = hasSymbol ? Symbol.for('react.forward_ref') : 0xead0;\r\nvar REACT_SUSPENSE_TYPE = hasSymbol ? Symbol.for('react.suspense') : 0xead1;\r\nvar REACT_SUSPENSE_LIST_TYPE = hasSymbol ? Symbol.for('react.suspense_list') : 0xead8;\r\nvar REACT_MEMO_TYPE = hasSymbol ? Symbol.for('react.memo') : 0xead3;\r\nvar REACT_LAZY_TYPE = hasSymbol ? Symbol.for('react.lazy') : 0xead4;\r\nvar REACT_BLOCK_TYPE = hasSymbol ? Symbol.for('react.block') : 0xead9;\r\nvar REACT_FUNDAMENTAL_TYPE = hasSymbol ? Symbol.for('react.fundamental') : 0xead5;\r\nvar REACT_RESPONDER_TYPE = hasSymbol ? Symbol.for('react.responder') : 0xead6;\r\nvar REACT_SCOPE_TYPE = hasSymbol ? Symbol.for('react.scope') : 0xead7;\r\n\r\nfunction isValidElementType(type) {\r\n  return typeof type === 'string' || typeof type === 'function' || // Note: its typeof might be other than 'symbol' or 'number' if it's a polyfill.\r\n  type === REACT_FRAGMENT_TYPE || type === REACT_CONCURRENT_MODE_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || typeof type === 'object' && type !== null && (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_FUNDAMENTAL_TYPE || type.$$typeof === REACT_RESPONDER_TYPE || type.$$typeof === REACT_SCOPE_TYPE || type.$$typeof === REACT_BLOCK_TYPE);\r\n}\r\n\r\nfunction typeOf(object) {\r\n  if (typeof object === 'object' && object !== null) {\r\n    var $$typeof = object.$$typeof;\r\n\r\n    switch ($$typeof) {\r\n      case REACT_ELEMENT_TYPE:\r\n        var type = object.type;\r\n\r\n        switch (type) {\r\n          case REACT_ASYNC_MODE_TYPE:\r\n          case REACT_CONCURRENT_MODE_TYPE:\r\n          case REACT_FRAGMENT_TYPE:\r\n          case REACT_PROFILER_TYPE:\r\n          case REACT_STRICT_MODE_TYPE:\r\n          case REACT_SUSPENSE_TYPE:\r\n            return type;\r\n\r\n          default:\r\n            var $$typeofType = type && type.$$typeof;\r\n\r\n            switch ($$typeofType) {\r\n              case REACT_CONTEXT_TYPE:\r\n              case REACT_FORWARD_REF_TYPE:\r\n              case REACT_LAZY_TYPE:\r\n              case REACT_MEMO_TYPE:\r\n              case REACT_PROVIDER_TYPE:\r\n                return $$typeofType;\r\n\r\n              default:\r\n                return $$typeof;\r\n            }\r\n\r\n        }\r\n\r\n      case REACT_PORTAL_TYPE:\r\n        return $$typeof;\r\n    }\r\n  }\r\n\r\n  return undefined;\r\n} // AsyncMode is deprecated along with isAsyncMode\r\n\r\nvar AsyncMode = REACT_ASYNC_MODE_TYPE;\r\nvar ConcurrentMode = REACT_CONCURRENT_MODE_TYPE;\r\nvar ContextConsumer = REACT_CONTEXT_TYPE;\r\nvar ContextProvider = REACT_PROVIDER_TYPE;\r\nvar Element = REACT_ELEMENT_TYPE;\r\nvar ForwardRef = REACT_FORWARD_REF_TYPE;\r\nvar Fragment = REACT_FRAGMENT_TYPE;\r\nvar Lazy = REACT_LAZY_TYPE;\r\nvar Memo = REACT_MEMO_TYPE;\r\nvar Portal = REACT_PORTAL_TYPE;\r\nvar Profiler = REACT_PROFILER_TYPE;\r\nvar StrictMode = REACT_STRICT_MODE_TYPE;\r\nvar Suspense = REACT_SUSPENSE_TYPE;\r\nvar hasWarnedAboutDeprecatedIsAsyncMode = false; // AsyncMode should be deprecated\r\n\r\nfunction isAsyncMode(object) {\r\n  {\r\n    if (!hasWarnedAboutDeprecatedIsAsyncMode) {\r\n      hasWarnedAboutDeprecatedIsAsyncMode = true; // Using console['warn'] to evade Babel and ESLint\r\n\r\n      console['warn']('The ReactIs.isAsyncMode() alias has been deprecated, ' + 'and will be removed in React 17+. Update your code to use ' + 'ReactIs.isConcurrentMode() instead. It has the exact same API.');\r\n    }\r\n  }\r\n\r\n  return isConcurrentMode(object) || typeOf(object) === REACT_ASYNC_MODE_TYPE;\r\n}\r\nfunction isConcurrentMode(object) {\r\n  return typeOf(object) === REACT_CONCURRENT_MODE_TYPE;\r\n}\r\nfunction isContextConsumer(object) {\r\n  return typeOf(object) === REACT_CONTEXT_TYPE;\r\n}\r\nfunction isContextProvider(object) {\r\n  return typeOf(object) === REACT_PROVIDER_TYPE;\r\n}\r\nfunction isElement(object) {\r\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\r\n}\r\nfunction isForwardRef(object) {\r\n  return typeOf(object) === REACT_FORWARD_REF_TYPE;\r\n}\r\nfunction isFragment(object) {\r\n  return typeOf(object) === REACT_FRAGMENT_TYPE;\r\n}\r\nfunction isLazy(object) {\r\n  return typeOf(object) === REACT_LAZY_TYPE;\r\n}\r\nfunction isMemo(object) {\r\n  return typeOf(object) === REACT_MEMO_TYPE;\r\n}\r\nfunction isPortal(object) {\r\n  return typeOf(object) === REACT_PORTAL_TYPE;\r\n}\r\nfunction isProfiler(object) {\r\n  return typeOf(object) === REACT_PROFILER_TYPE;\r\n}\r\nfunction isStrictMode(object) {\r\n  return typeOf(object) === REACT_STRICT_MODE_TYPE;\r\n}\r\nfunction isSuspense(object) {\r\n  return typeOf(object) === REACT_SUSPENSE_TYPE;\r\n}\r\n\r\nexports.AsyncMode = AsyncMode;\r\nexports.ConcurrentMode = ConcurrentMode;\r\nexports.ContextConsumer = ContextConsumer;\r\nexports.ContextProvider = ContextProvider;\r\nexports.Element = Element;\r\nexports.ForwardRef = ForwardRef;\r\nexports.Fragment = Fragment;\r\nexports.Lazy = Lazy;\r\nexports.Memo = Memo;\r\nexports.Portal = Portal;\r\nexports.Profiler = Profiler;\r\nexports.StrictMode = StrictMode;\r\nexports.Suspense = Suspense;\r\nexports.isAsyncMode = isAsyncMode;\r\nexports.isConcurrentMode = isConcurrentMode;\r\nexports.isContextConsumer = isContextConsumer;\r\nexports.isContextProvider = isContextProvider;\r\nexports.isElement = isElement;\r\nexports.isForwardRef = isForwardRef;\r\nexports.isFragment = isFragment;\r\nexports.isLazy = isLazy;\r\nexports.isMemo = isMemo;\r\nexports.isPortal = isPortal;\r\nexports.isProfiler = isProfiler;\r\nexports.isStrictMode = isStrictMode;\r\nexports.isSuspense = isSuspense;\r\nexports.isValidElementType = isValidElementType;\r\nexports.typeOf = typeOf;\r\n  })();\r\n}\r\n", "'use strict';\r\n\r\nif (process.env.NODE_ENV === 'production') {\r\n  module.exports = require('./cjs/react-is.production.min.js');\r\n} else {\r\n  module.exports = require('./cjs/react-is.development.js');\r\n}\r\n"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAaA,QAAI,MAAuC;AACzC,OAAC,WAAW;AACd;AAIA,YAAI,YAAY,OAAO,WAAW,cAAc,OAAO;AACvD,YAAI,qBAAqB,YAAY,OAAO,IAAI,eAAe,IAAI;AACnE,YAAI,oBAAoB,YAAY,OAAO,IAAI,cAAc,IAAI;AACjE,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,yBAAyB,YAAY,OAAO,IAAI,mBAAmB,IAAI;AAC3E,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,qBAAqB,YAAY,OAAO,IAAI,eAAe,IAAI;AAGnE,YAAI,wBAAwB,YAAY,OAAO,IAAI,kBAAkB,IAAI;AACzE,YAAI,6BAA6B,YAAY,OAAO,IAAI,uBAAuB,IAAI;AACnF,YAAI,yBAAyB,YAAY,OAAO,IAAI,mBAAmB,IAAI;AAC3E,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,2BAA2B,YAAY,OAAO,IAAI,qBAAqB,IAAI;AAC/E,YAAI,kBAAkB,YAAY,OAAO,IAAI,YAAY,IAAI;AAC7D,YAAI,kBAAkB,YAAY,OAAO,IAAI,YAAY,IAAI;AAC7D,YAAI,mBAAmB,YAAY,OAAO,IAAI,aAAa,IAAI;AAC/D,YAAI,yBAAyB,YAAY,OAAO,IAAI,mBAAmB,IAAI;AAC3E,YAAI,uBAAuB,YAAY,OAAO,IAAI,iBAAiB,IAAI;AACvE,YAAI,mBAAmB,YAAY,OAAO,IAAI,aAAa,IAAI;AAE/D,iBAAS,mBAAmB,MAAM;AAChC,iBAAO,OAAO,SAAS,YAAY,OAAO,SAAS;AAAA,UACnD,SAAS,uBAAuB,SAAS,8BAA8B,SAAS,uBAAuB,SAAS,0BAA0B,SAAS,uBAAuB,SAAS,4BAA4B,OAAO,SAAS,YAAY,SAAS,SAAS,KAAK,aAAa,mBAAmB,KAAK,aAAa,mBAAmB,KAAK,aAAa,uBAAuB,KAAK,aAAa,sBAAsB,KAAK,aAAa,0BAA0B,KAAK,aAAa,0BAA0B,KAAK,aAAa,wBAAwB,KAAK,aAAa,oBAAoB,KAAK,aAAa;AAAA,QACplB;AAEA,iBAAS,OAAO,QAAQ;AACtB,cAAI,OAAO,WAAW,YAAY,WAAW,MAAM;AACjD,gBAAI,WAAW,OAAO;AAEtB,oBAAQ,UAAU;AAAA,cAChB,KAAK;AACH,oBAAI,OAAO,OAAO;AAElB,wBAAQ,MAAM;AAAA,kBACZ,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AACH,2BAAO;AAAA,kBAET;AACE,wBAAI,eAAe,QAAQ,KAAK;AAEhC,4BAAQ,cAAc;AAAA,sBACpB,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AACH,+BAAO;AAAA,sBAET;AACE,+BAAO;AAAA,oBACX;AAAA,gBAEJ;AAAA,cAEF,KAAK;AACH,uBAAO;AAAA,YACX;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAEA,YAAI,YAAY;AAChB,YAAI,iBAAiB;AACrB,YAAI,kBAAkB;AACtB,YAAI,kBAAkB;AACtB,YAAI,UAAU;AACd,YAAI,aAAa;AACjB,YAAI,WAAW;AACf,YAAI,OAAO;AACX,YAAI,OAAO;AACX,YAAI,SAAS;AACb,YAAI,WAAW;AACf,YAAI,aAAa;AACjB,YAAI,WAAW;AACf,YAAI,sCAAsC;AAE1C,iBAAS,YAAY,QAAQ;AAC3B;AACE,gBAAI,CAAC,qCAAqC;AACxC,oDAAsC;AAEtC,sBAAQ,MAAM,EAAE,+KAAyL;AAAA,YAC3M;AAAA,UACF;AAEA,iBAAO,iBAAiB,MAAM,KAAK,OAAO,MAAM,MAAM;AAAA,QACxD;AACA,iBAAS,iBAAiB,QAAQ;AAChC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,kBAAkB,QAAQ;AACjC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,kBAAkB,QAAQ;AACjC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,UAAU,QAAQ;AACzB,iBAAO,OAAO,WAAW,YAAY,WAAW,QAAQ,OAAO,aAAa;AAAA,QAC9E;AACA,iBAAS,aAAa,QAAQ;AAC5B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,OAAO,QAAQ;AACtB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,OAAO,QAAQ;AACtB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,SAAS,QAAQ;AACxB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,aAAa,QAAQ;AAC5B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AAEA,gBAAQ,YAAY;AACpB,gBAAQ,iBAAiB;AACzB,gBAAQ,kBAAkB;AAC1B,gBAAQ,kBAAkB;AAC1B,gBAAQ,UAAU;AAClB,gBAAQ,aAAa;AACrB,gBAAQ,WAAW;AACnB,gBAAQ,OAAO;AACf,gBAAQ,OAAO;AACf,gBAAQ,SAAS;AACjB,gBAAQ,WAAW;AACnB,gBAAQ,aAAa;AACrB,gBAAQ,WAAW;AACnB,gBAAQ,cAAc;AACtB,gBAAQ,mBAAmB;AAC3B,gBAAQ,oBAAoB;AAC5B,gBAAQ,oBAAoB;AAC5B,gBAAQ,YAAY;AACpB,gBAAQ,eAAe;AACvB,gBAAQ,aAAa;AACrB,gBAAQ,SAAS;AACjB,gBAAQ,SAAS;AACjB,gBAAQ,WAAW;AACnB,gBAAQ,aAAa;AACrB,gBAAQ,eAAe;AACvB,gBAAQ,aAAa;AACrB,gBAAQ,qBAAqB;AAC7B,gBAAQ,SAAS;AAAA,MACf,GAAG;AAAA,IACL;AAAA;AAAA;;;ACpLA;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;", "names": []}