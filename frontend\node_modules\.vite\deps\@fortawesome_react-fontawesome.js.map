{"version": 3, "sources": ["../../@fortawesome/fontawesome-svg-core/index.mjs", "../../@fortawesome/react-fontawesome/index.es.js"], "sourcesContent": ["/*!\r\n * Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com\r\n * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)\r\n * Copyright 2024 Fonticons, Inc.\r\n */\r\nfunction _defineProperty(e, r, t) {\r\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\r\n    value: t,\r\n    enumerable: !0,\r\n    configurable: !0,\r\n    writable: !0\r\n  }) : e[r] = t, e;\r\n}\r\nfunction _inherits(t, e) {\r\n  if (\"function\" != typeof e && null !== e) throw new TypeError(\"Super expression must either be null or a function\");\r\n  t.prototype = Object.create(e && e.prototype, {\r\n    constructor: {\r\n      value: t,\r\n      writable: !0,\r\n      configurable: !0\r\n    }\r\n  }), Object.defineProperty(t, \"prototype\", {\r\n    writable: !1\r\n  }), e && _setPrototypeOf(t, e);\r\n}\r\nfunction ownKeys(e, r) {\r\n  var t = Object.keys(e);\r\n  if (Object.getOwnPropertySymbols) {\r\n    var o = Object.getOwnPropertySymbols(e);\r\n    r && (o = o.filter(function (r) {\r\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\r\n    })), t.push.apply(t, o);\r\n  }\r\n  return t;\r\n}\r\nfunction _objectSpread2(e) {\r\n  for (var r = 1; r < arguments.length; r++) {\r\n    var t = null != arguments[r] ? arguments[r] : {};\r\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\r\n      _defineProperty(e, r, t[r]);\r\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\r\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\r\n    });\r\n  }\r\n  return e;\r\n}\r\nfunction _setPrototypeOf(t, e) {\r\n  return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) {\r\n    return t.__proto__ = e, t;\r\n  }, _setPrototypeOf(t, e);\r\n}\r\nfunction _toPrimitive(t, r) {\r\n  if (\"object\" != typeof t || !t) return t;\r\n  var e = t[Symbol.toPrimitive];\r\n  if (void 0 !== e) {\r\n    var i = e.call(t, r || \"default\");\r\n    if (\"object\" != typeof i) return i;\r\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\r\n  }\r\n  return (\"string\" === r ? String : Number)(t);\r\n}\r\nfunction _toPropertyKey(t) {\r\n  var i = _toPrimitive(t, \"string\");\r\n  return \"symbol\" == typeof i ? i : i + \"\";\r\n}\r\nfunction _wrapRegExp() {\r\n  _wrapRegExp = function (e, r) {\r\n    return new BabelRegExp(e, void 0, r);\r\n  };\r\n  var e = RegExp.prototype,\r\n    r = new WeakMap();\r\n  function BabelRegExp(e, t, p) {\r\n    var o = RegExp(e, t);\r\n    return r.set(o, p || r.get(e)), _setPrototypeOf(o, BabelRegExp.prototype);\r\n  }\r\n  function buildGroups(e, t) {\r\n    var p = r.get(t);\r\n    return Object.keys(p).reduce(function (r, t) {\r\n      var o = p[t];\r\n      if (\"number\" == typeof o) r[t] = e[o];else {\r\n        for (var i = 0; void 0 === e[o[i]] && i + 1 < o.length;) i++;\r\n        r[t] = e[o[i]];\r\n      }\r\n      return r;\r\n    }, Object.create(null));\r\n  }\r\n  return _inherits(BabelRegExp, RegExp), BabelRegExp.prototype.exec = function (r) {\r\n    var t = e.exec.call(this, r);\r\n    if (t) {\r\n      t.groups = buildGroups(t, this);\r\n      var p = t.indices;\r\n      p && (p.groups = buildGroups(p, this));\r\n    }\r\n    return t;\r\n  }, BabelRegExp.prototype[Symbol.replace] = function (t, p) {\r\n    if (\"string\" == typeof p) {\r\n      var o = r.get(this);\r\n      return e[Symbol.replace].call(this, t, p.replace(/\\$<([^>]+)>/g, function (e, r) {\r\n        var t = o[r];\r\n        return \"$\" + (Array.isArray(t) ? t.join(\"$\") : t);\r\n      }));\r\n    }\r\n    if (\"function\" == typeof p) {\r\n      var i = this;\r\n      return e[Symbol.replace].call(this, t, function () {\r\n        var e = arguments;\r\n        return \"object\" != typeof e[e.length - 1] && (e = [].slice.call(e)).push(buildGroups(e, i)), p.apply(this, e);\r\n      });\r\n    }\r\n    return e[Symbol.replace].call(this, t, p);\r\n  }, _wrapRegExp.apply(this, arguments);\r\n}\r\n\r\nconst noop = () => {};\r\nlet _WINDOW = {};\r\nlet _DOCUMENT = {};\r\nlet _MUTATION_OBSERVER = null;\r\nlet _PERFORMANCE = {\r\n  mark: noop,\r\n  measure: noop\r\n};\r\ntry {\r\n  if (typeof window !== 'undefined') _WINDOW = window;\r\n  if (typeof document !== 'undefined') _DOCUMENT = document;\r\n  if (typeof MutationObserver !== 'undefined') _MUTATION_OBSERVER = MutationObserver;\r\n  if (typeof performance !== 'undefined') _PERFORMANCE = performance;\r\n} catch (e) {}\r\nconst {\r\n  userAgent = ''\r\n} = _WINDOW.navigator || {};\r\nconst WINDOW = _WINDOW;\r\nconst DOCUMENT = _DOCUMENT;\r\nconst MUTATION_OBSERVER = _MUTATION_OBSERVER;\r\nconst PERFORMANCE = _PERFORMANCE;\r\nconst IS_BROWSER = !!WINDOW.document;\r\nconst IS_DOM = !!DOCUMENT.documentElement && !!DOCUMENT.head && typeof DOCUMENT.addEventListener === 'function' && typeof DOCUMENT.createElement === 'function';\r\nconst IS_IE = ~userAgent.indexOf('MSIE') || ~userAgent.indexOf('Trident/');\r\n\r\nvar p = /fa(s|r|l|t|d|dr|dl|dt|b|k|kd|ss|sr|sl|st|sds|sdr|sdl|sdt)?[\\-\\ ]/,\r\n  g = /Font ?Awesome ?([56 ]*)(Solid|Regular|Light|Thin|Duotone|Brands|Free|Pro|Sharp Duotone|Sharp|Kit)?.*/i;\r\nvar S = {\r\n    classic: {\r\n      fa: \"solid\",\r\n      fas: \"solid\",\r\n      \"fa-solid\": \"solid\",\r\n      far: \"regular\",\r\n      \"fa-regular\": \"regular\",\r\n      fal: \"light\",\r\n      \"fa-light\": \"light\",\r\n      fat: \"thin\",\r\n      \"fa-thin\": \"thin\",\r\n      fab: \"brands\",\r\n      \"fa-brands\": \"brands\"\r\n    },\r\n    duotone: {\r\n      fa: \"solid\",\r\n      fad: \"solid\",\r\n      \"fa-solid\": \"solid\",\r\n      \"fa-duotone\": \"solid\",\r\n      fadr: \"regular\",\r\n      \"fa-regular\": \"regular\",\r\n      fadl: \"light\",\r\n      \"fa-light\": \"light\",\r\n      fadt: \"thin\",\r\n      \"fa-thin\": \"thin\"\r\n    },\r\n    sharp: {\r\n      fa: \"solid\",\r\n      fass: \"solid\",\r\n      \"fa-solid\": \"solid\",\r\n      fasr: \"regular\",\r\n      \"fa-regular\": \"regular\",\r\n      fasl: \"light\",\r\n      \"fa-light\": \"light\",\r\n      fast: \"thin\",\r\n      \"fa-thin\": \"thin\"\r\n    },\r\n    \"sharp-duotone\": {\r\n      fa: \"solid\",\r\n      fasds: \"solid\",\r\n      \"fa-solid\": \"solid\",\r\n      fasdr: \"regular\",\r\n      \"fa-regular\": \"regular\",\r\n      fasdl: \"light\",\r\n      \"fa-light\": \"light\",\r\n      fasdt: \"thin\",\r\n      \"fa-thin\": \"thin\"\r\n    }\r\n  },\r\n  A = {\r\n    GROUP: \"duotone-group\",\r\n    SWAP_OPACITY: \"swap-opacity\",\r\n    PRIMARY: \"primary\",\r\n    SECONDARY: \"secondary\"\r\n  },\r\n  P = [\"fa-classic\", \"fa-duotone\", \"fa-sharp\", \"fa-sharp-duotone\"];\r\nvar s = \"classic\",\r\n  t = \"duotone\",\r\n  r = \"sharp\",\r\n  o = \"sharp-duotone\",\r\n  L = [s, t, r, o];\r\nvar G = {\r\n    classic: {\r\n      900: \"fas\",\r\n      400: \"far\",\r\n      normal: \"far\",\r\n      300: \"fal\",\r\n      100: \"fat\"\r\n    },\r\n    duotone: {\r\n      900: \"fad\",\r\n      400: \"fadr\",\r\n      300: \"fadl\",\r\n      100: \"fadt\"\r\n    },\r\n    sharp: {\r\n      900: \"fass\",\r\n      400: \"fasr\",\r\n      300: \"fasl\",\r\n      100: \"fast\"\r\n    },\r\n    \"sharp-duotone\": {\r\n      900: \"fasds\",\r\n      400: \"fasdr\",\r\n      300: \"fasdl\",\r\n      100: \"fasdt\"\r\n    }\r\n  };\r\nvar lt = {\r\n    \"Font Awesome 6 Free\": {\r\n      900: \"fas\",\r\n      400: \"far\"\r\n    },\r\n    \"Font Awesome 6 Pro\": {\r\n      900: \"fas\",\r\n      400: \"far\",\r\n      normal: \"far\",\r\n      300: \"fal\",\r\n      100: \"fat\"\r\n    },\r\n    \"Font Awesome 6 Brands\": {\r\n      400: \"fab\",\r\n      normal: \"fab\"\r\n    },\r\n    \"Font Awesome 6 Duotone\": {\r\n      900: \"fad\",\r\n      400: \"fadr\",\r\n      normal: \"fadr\",\r\n      300: \"fadl\",\r\n      100: \"fadt\"\r\n    },\r\n    \"Font Awesome 6 Sharp\": {\r\n      900: \"fass\",\r\n      400: \"fasr\",\r\n      normal: \"fasr\",\r\n      300: \"fasl\",\r\n      100: \"fast\"\r\n    },\r\n    \"Font Awesome 6 Sharp Duotone\": {\r\n      900: \"fasds\",\r\n      400: \"fasdr\",\r\n      normal: \"fasdr\",\r\n      300: \"fasdl\",\r\n      100: \"fasdt\"\r\n    }\r\n  };\r\nvar pt = new Map([[\"classic\", {\r\n    defaultShortPrefixId: \"fas\",\r\n    defaultStyleId: \"solid\",\r\n    styleIds: [\"solid\", \"regular\", \"light\", \"thin\", \"brands\"],\r\n    futureStyleIds: [],\r\n    defaultFontWeight: 900\r\n  }], [\"sharp\", {\r\n    defaultShortPrefixId: \"fass\",\r\n    defaultStyleId: \"solid\",\r\n    styleIds: [\"solid\", \"regular\", \"light\", \"thin\"],\r\n    futureStyleIds: [],\r\n    defaultFontWeight: 900\r\n  }], [\"duotone\", {\r\n    defaultShortPrefixId: \"fad\",\r\n    defaultStyleId: \"solid\",\r\n    styleIds: [\"solid\", \"regular\", \"light\", \"thin\"],\r\n    futureStyleIds: [],\r\n    defaultFontWeight: 900\r\n  }], [\"sharp-duotone\", {\r\n    defaultShortPrefixId: \"fasds\",\r\n    defaultStyleId: \"solid\",\r\n    styleIds: [\"solid\", \"regular\", \"light\", \"thin\"],\r\n    futureStyleIds: [],\r\n    defaultFontWeight: 900\r\n  }]]),\r\n  xt = {\r\n    classic: {\r\n      solid: \"fas\",\r\n      regular: \"far\",\r\n      light: \"fal\",\r\n      thin: \"fat\",\r\n      brands: \"fab\"\r\n    },\r\n    duotone: {\r\n      solid: \"fad\",\r\n      regular: \"fadr\",\r\n      light: \"fadl\",\r\n      thin: \"fadt\"\r\n    },\r\n    sharp: {\r\n      solid: \"fass\",\r\n      regular: \"fasr\",\r\n      light: \"fasl\",\r\n      thin: \"fast\"\r\n    },\r\n    \"sharp-duotone\": {\r\n      solid: \"fasds\",\r\n      regular: \"fasdr\",\r\n      light: \"fasdl\",\r\n      thin: \"fasdt\"\r\n    }\r\n  };\r\nvar Ft = [\"fak\", \"fa-kit\", \"fakd\", \"fa-kit-duotone\"],\r\n  St = {\r\n    kit: {\r\n      fak: \"kit\",\r\n      \"fa-kit\": \"kit\"\r\n    },\r\n    \"kit-duotone\": {\r\n      fakd: \"kit-duotone\",\r\n      \"fa-kit-duotone\": \"kit-duotone\"\r\n    }\r\n  },\r\n  At = [\"kit\"];\r\nvar Ct = {\r\n  kit: {\r\n    \"fa-kit\": \"fak\"\r\n  },\r\n  \"kit-duotone\": {\r\n    \"fa-kit-duotone\": \"fakd\"\r\n  }\r\n};\r\nvar Lt = [\"fak\", \"fakd\"],\r\n  Wt = {\r\n    kit: {\r\n      fak: \"fa-kit\"\r\n    },\r\n    \"kit-duotone\": {\r\n      fakd: \"fa-kit-duotone\"\r\n    }\r\n  };\r\nvar Et = {\r\n    kit: {\r\n      kit: \"fak\"\r\n    },\r\n    \"kit-duotone\": {\r\n      \"kit-duotone\": \"fakd\"\r\n    }\r\n  };\r\n\r\nvar t$1 = {\r\n    GROUP: \"duotone-group\",\r\n    SWAP_OPACITY: \"swap-opacity\",\r\n    PRIMARY: \"primary\",\r\n    SECONDARY: \"secondary\"\r\n  },\r\n  r$1 = [\"fa-classic\", \"fa-duotone\", \"fa-sharp\", \"fa-sharp-duotone\"];\r\nvar bt$1 = [\"fak\", \"fa-kit\", \"fakd\", \"fa-kit-duotone\"];\r\nvar Yt = {\r\n    \"Font Awesome Kit\": {\r\n      400: \"fak\",\r\n      normal: \"fak\"\r\n    },\r\n    \"Font Awesome Kit Duotone\": {\r\n      400: \"fakd\",\r\n      normal: \"fakd\"\r\n    }\r\n  };\r\nvar ua = {\r\n    classic: {\r\n      \"fa-brands\": \"fab\",\r\n      \"fa-duotone\": \"fad\",\r\n      \"fa-light\": \"fal\",\r\n      \"fa-regular\": \"far\",\r\n      \"fa-solid\": \"fas\",\r\n      \"fa-thin\": \"fat\"\r\n    },\r\n    duotone: {\r\n      \"fa-regular\": \"fadr\",\r\n      \"fa-light\": \"fadl\",\r\n      \"fa-thin\": \"fadt\"\r\n    },\r\n    sharp: {\r\n      \"fa-solid\": \"fass\",\r\n      \"fa-regular\": \"fasr\",\r\n      \"fa-light\": \"fasl\",\r\n      \"fa-thin\": \"fast\"\r\n    },\r\n    \"sharp-duotone\": {\r\n      \"fa-solid\": \"fasds\",\r\n      \"fa-regular\": \"fasdr\",\r\n      \"fa-light\": \"fasdl\",\r\n      \"fa-thin\": \"fasdt\"\r\n    }\r\n  },\r\n  I$1 = {\r\n    classic: [\"fas\", \"far\", \"fal\", \"fat\", \"fad\"],\r\n    duotone: [\"fadr\", \"fadl\", \"fadt\"],\r\n    sharp: [\"fass\", \"fasr\", \"fasl\", \"fast\"],\r\n    \"sharp-duotone\": [\"fasds\", \"fasdr\", \"fasdl\", \"fasdt\"]\r\n  },\r\n  ga = {\r\n    classic: {\r\n      fab: \"fa-brands\",\r\n      fad: \"fa-duotone\",\r\n      fal: \"fa-light\",\r\n      far: \"fa-regular\",\r\n      fas: \"fa-solid\",\r\n      fat: \"fa-thin\"\r\n    },\r\n    duotone: {\r\n      fadr: \"fa-regular\",\r\n      fadl: \"fa-light\",\r\n      fadt: \"fa-thin\"\r\n    },\r\n    sharp: {\r\n      fass: \"fa-solid\",\r\n      fasr: \"fa-regular\",\r\n      fasl: \"fa-light\",\r\n      fast: \"fa-thin\"\r\n    },\r\n    \"sharp-duotone\": {\r\n      fasds: \"fa-solid\",\r\n      fasdr: \"fa-regular\",\r\n      fasdl: \"fa-light\",\r\n      fasdt: \"fa-thin\"\r\n    }\r\n  },\r\n  x = [\"fa-solid\", \"fa-regular\", \"fa-light\", \"fa-thin\", \"fa-duotone\", \"fa-brands\"],\r\n  Ia = [\"fa\", \"fas\", \"far\", \"fal\", \"fat\", \"fad\", \"fadr\", \"fadl\", \"fadt\", \"fab\", \"fass\", \"fasr\", \"fasl\", \"fast\", \"fasds\", \"fasdr\", \"fasdl\", \"fasdt\", ...r$1, ...x],\r\n  m$1 = [\"solid\", \"regular\", \"light\", \"thin\", \"duotone\", \"brands\"],\r\n  c$1 = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],\r\n  F$1 = c$1.concat([11, 12, 13, 14, 15, 16, 17, 18, 19, 20]),\r\n  ma = [...Object.keys(I$1), ...m$1, \"2xs\", \"xs\", \"sm\", \"lg\", \"xl\", \"2xl\", \"beat\", \"border\", \"fade\", \"beat-fade\", \"bounce\", \"flip-both\", \"flip-horizontal\", \"flip-vertical\", \"flip\", \"fw\", \"inverse\", \"layers-counter\", \"layers-text\", \"layers\", \"li\", \"pull-left\", \"pull-right\", \"pulse\", \"rotate-180\", \"rotate-270\", \"rotate-90\", \"rotate-by\", \"shake\", \"spin-pulse\", \"spin-reverse\", \"spin\", \"stack-1x\", \"stack-2x\", \"stack\", \"ul\", t$1.GROUP, t$1.SWAP_OPACITY, t$1.PRIMARY, t$1.SECONDARY].concat(c$1.map(a => \"\".concat(a, \"x\"))).concat(F$1.map(a => \"w-\".concat(a)));\r\nvar wa = {\r\n    \"Font Awesome 5 Free\": {\r\n      900: \"fas\",\r\n      400: \"far\"\r\n    },\r\n    \"Font Awesome 5 Pro\": {\r\n      900: \"fas\",\r\n      400: \"far\",\r\n      normal: \"far\",\r\n      300: \"fal\"\r\n    },\r\n    \"Font Awesome 5 Brands\": {\r\n      400: \"fab\",\r\n      normal: \"fab\"\r\n    },\r\n    \"Font Awesome 5 Duotone\": {\r\n      900: \"fad\"\r\n    }\r\n  };\r\n\r\nconst NAMESPACE_IDENTIFIER = '___FONT_AWESOME___';\r\nconst UNITS_IN_GRID = 16;\r\nconst DEFAULT_CSS_PREFIX = 'fa';\r\nconst DEFAULT_REPLACEMENT_CLASS = 'svg-inline--fa';\r\nconst DATA_FA_I2SVG = 'data-fa-i2svg';\r\nconst DATA_FA_PSEUDO_ELEMENT = 'data-fa-pseudo-element';\r\nconst DATA_FA_PSEUDO_ELEMENT_PENDING = 'data-fa-pseudo-element-pending';\r\nconst DATA_PREFIX = 'data-prefix';\r\nconst DATA_ICON = 'data-icon';\r\nconst HTML_CLASS_I2SVG_BASE_CLASS = 'fontawesome-i2svg';\r\nconst MUTATION_APPROACH_ASYNC = 'async';\r\nconst TAGNAMES_TO_SKIP_FOR_PSEUDOELEMENTS = ['HTML', 'HEAD', 'STYLE', 'SCRIPT'];\r\nconst PRODUCTION = (() => {\r\n  try {\r\n    return process.env.NODE_ENV === 'production';\r\n  } catch (e$$1) {\r\n    return false;\r\n  }\r\n})();\r\nfunction familyProxy(obj) {\r\n  // Defaults to the classic family if family is not available\r\n  return new Proxy(obj, {\r\n    get(target, prop) {\r\n      return prop in target ? target[prop] : target[s];\r\n    }\r\n  });\r\n}\r\nconst _PREFIX_TO_STYLE = _objectSpread2({}, S);\r\n\r\n// We changed FACSSClassesToStyleId in the icons repo to be canonical and as such, \"classic\" family does not have any\r\n// duotone styles.  But we do still need duotone in _PREFIX_TO_STYLE below, so we are manually adding\r\n// {'fa-duotone': 'duotone'}\r\n_PREFIX_TO_STYLE[s] = _objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({}, {\r\n  'fa-duotone': 'duotone'\r\n}), S[s]), St['kit']), St['kit-duotone']);\r\nconst PREFIX_TO_STYLE = familyProxy(_PREFIX_TO_STYLE);\r\nconst _STYLE_TO_PREFIX = _objectSpread2({}, xt);\r\n\r\n// We changed FAStyleIdToShortPrefixId in the icons repo to be canonical and as such, \"classic\" family does not have any\r\n// duotone styles.  But we do still need duotone in _STYLE_TO_PREFIX below, so we are manually adding {duotone: 'fad'}\r\n_STYLE_TO_PREFIX[s] = _objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({}, {\r\n  duotone: 'fad'\r\n}), _STYLE_TO_PREFIX[s]), Et['kit']), Et['kit-duotone']);\r\nconst STYLE_TO_PREFIX = familyProxy(_STYLE_TO_PREFIX);\r\nconst _PREFIX_TO_LONG_STYLE = _objectSpread2({}, ga);\r\n_PREFIX_TO_LONG_STYLE[s] = _objectSpread2(_objectSpread2({}, _PREFIX_TO_LONG_STYLE[s]), Wt['kit']);\r\nconst PREFIX_TO_LONG_STYLE = familyProxy(_PREFIX_TO_LONG_STYLE);\r\nconst _LONG_STYLE_TO_PREFIX = _objectSpread2({}, ua);\r\n_LONG_STYLE_TO_PREFIX[s] = _objectSpread2(_objectSpread2({}, _LONG_STYLE_TO_PREFIX[s]), Ct['kit']);\r\nconst LONG_STYLE_TO_PREFIX = familyProxy(_LONG_STYLE_TO_PREFIX);\r\nconst ICON_SELECTION_SYNTAX_PATTERN = p; // eslint-disable-line no-useless-escape\r\n\r\nconst LAYERS_TEXT_CLASSNAME = 'fa-layers-text';\r\nconst FONT_FAMILY_PATTERN = g;\r\nconst _FONT_WEIGHT_TO_PREFIX = _objectSpread2({}, G);\r\nconst FONT_WEIGHT_TO_PREFIX = familyProxy(_FONT_WEIGHT_TO_PREFIX);\r\nconst ATTRIBUTES_WATCHED_FOR_MUTATION = ['class', 'data-prefix', 'data-icon', 'data-fa-transform', 'data-fa-mask'];\r\nconst DUOTONE_CLASSES = A;\r\nconst RESERVED_CLASSES = [...At, ...ma];\r\n\r\nconst initial = WINDOW.FontAwesomeConfig || {};\r\nfunction getAttrConfig(attr) {\r\n  var element = DOCUMENT.querySelector('script[' + attr + ']');\r\n  if (element) {\r\n    return element.getAttribute(attr);\r\n  }\r\n}\r\nfunction coerce(val) {\r\n  // Getting an empty string will occur if the attribute is set on the HTML tag but without a value\r\n  // We'll assume that this is an indication that it should be toggled to true\r\n  if (val === '') return true;\r\n  if (val === 'false') return false;\r\n  if (val === 'true') return true;\r\n  return val;\r\n}\r\nif (DOCUMENT && typeof DOCUMENT.querySelector === 'function') {\r\n  const attrs = [['data-family-prefix', 'familyPrefix'], ['data-css-prefix', 'cssPrefix'], ['data-family-default', 'familyDefault'], ['data-style-default', 'styleDefault'], ['data-replacement-class', 'replacementClass'], ['data-auto-replace-svg', 'autoReplaceSvg'], ['data-auto-add-css', 'autoAddCss'], ['data-auto-a11y', 'autoA11y'], ['data-search-pseudo-elements', 'searchPseudoElements'], ['data-observe-mutations', 'observeMutations'], ['data-mutate-approach', 'mutateApproach'], ['data-keep-original-source', 'keepOriginalSource'], ['data-measure-performance', 'measurePerformance'], ['data-show-missing-icons', 'showMissingIcons']];\r\n  attrs.forEach(_ref => {\r\n    let [attr, key] = _ref;\r\n    const val = coerce(getAttrConfig(attr));\r\n    if (val !== undefined && val !== null) {\r\n      initial[key] = val;\r\n    }\r\n  });\r\n}\r\nconst _default = {\r\n  styleDefault: 'solid',\r\n  familyDefault: s,\r\n  cssPrefix: DEFAULT_CSS_PREFIX,\r\n  replacementClass: DEFAULT_REPLACEMENT_CLASS,\r\n  autoReplaceSvg: true,\r\n  autoAddCss: true,\r\n  autoA11y: true,\r\n  searchPseudoElements: false,\r\n  observeMutations: true,\r\n  mutateApproach: 'async',\r\n  keepOriginalSource: true,\r\n  measurePerformance: false,\r\n  showMissingIcons: true\r\n};\r\n\r\n// familyPrefix is deprecated but we must still support it if present\r\nif (initial.familyPrefix) {\r\n  initial.cssPrefix = initial.familyPrefix;\r\n}\r\nconst _config = _objectSpread2(_objectSpread2({}, _default), initial);\r\nif (!_config.autoReplaceSvg) _config.observeMutations = false;\r\nconst config = {};\r\nObject.keys(_default).forEach(key => {\r\n  Object.defineProperty(config, key, {\r\n    enumerable: true,\r\n    set: function (val) {\r\n      _config[key] = val;\r\n      _onChangeCb.forEach(cb => cb(config));\r\n    },\r\n    get: function () {\r\n      return _config[key];\r\n    }\r\n  });\r\n});\r\n\r\n// familyPrefix is deprecated as of 6.2.0 and should be removed in 7.0.0\r\nObject.defineProperty(config, 'familyPrefix', {\r\n  enumerable: true,\r\n  set: function (val) {\r\n    _config.cssPrefix = val;\r\n    _onChangeCb.forEach(cb => cb(config));\r\n  },\r\n  get: function () {\r\n    return _config.cssPrefix;\r\n  }\r\n});\r\nWINDOW.FontAwesomeConfig = config;\r\nconst _onChangeCb = [];\r\nfunction onChange(cb) {\r\n  _onChangeCb.push(cb);\r\n  return () => {\r\n    _onChangeCb.splice(_onChangeCb.indexOf(cb), 1);\r\n  };\r\n}\r\n\r\nconst d$2 = UNITS_IN_GRID;\r\nconst meaninglessTransform = {\r\n  size: 16,\r\n  x: 0,\r\n  y: 0,\r\n  rotate: 0,\r\n  flipX: false,\r\n  flipY: false\r\n};\r\nfunction insertCss(css) {\r\n  if (!css || !IS_DOM) {\r\n    return;\r\n  }\r\n  const style = DOCUMENT.createElement('style');\r\n  style.setAttribute('type', 'text/css');\r\n  style.innerHTML = css;\r\n  const headChildren = DOCUMENT.head.childNodes;\r\n  let beforeChild = null;\r\n  for (let i = headChildren.length - 1; i > -1; i--) {\r\n    const child = headChildren[i];\r\n    const tagName = (child.tagName || '').toUpperCase();\r\n    if (['STYLE', 'LINK'].indexOf(tagName) > -1) {\r\n      beforeChild = child;\r\n    }\r\n  }\r\n  DOCUMENT.head.insertBefore(style, beforeChild);\r\n  return css;\r\n}\r\nconst idPool = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';\r\nfunction nextUniqueId() {\r\n  let size = 12;\r\n  let id = '';\r\n  while (size-- > 0) {\r\n    id += idPool[Math.random() * 62 | 0];\r\n  }\r\n  return id;\r\n}\r\nfunction toArray(obj) {\r\n  const array = [];\r\n  for (let i = (obj || []).length >>> 0; i--;) {\r\n    array[i] = obj[i];\r\n  }\r\n  return array;\r\n}\r\nfunction classArray(node) {\r\n  if (node.classList) {\r\n    return toArray(node.classList);\r\n  } else {\r\n    return (node.getAttribute('class') || '').split(' ').filter(i => i);\r\n  }\r\n}\r\nfunction htmlEscape(str) {\r\n  return \"\".concat(str).replace(/&/g, '&amp;').replace(/\"/g, '&quot;').replace(/'/g, '&#39;').replace(/</g, '&lt;').replace(/>/g, '&gt;');\r\n}\r\nfunction joinAttributes(attributes) {\r\n  return Object.keys(attributes || {}).reduce((acc, attributeName) => {\r\n    return acc + \"\".concat(attributeName, \"=\\\"\").concat(htmlEscape(attributes[attributeName]), \"\\\" \");\r\n  }, '').trim();\r\n}\r\nfunction joinStyles(styles) {\r\n  return Object.keys(styles || {}).reduce((acc, styleName) => {\r\n    return acc + \"\".concat(styleName, \": \").concat(styles[styleName].trim(), \";\");\r\n  }, '');\r\n}\r\nfunction transformIsMeaningful(transform) {\r\n  return transform.size !== meaninglessTransform.size || transform.x !== meaninglessTransform.x || transform.y !== meaninglessTransform.y || transform.rotate !== meaninglessTransform.rotate || transform.flipX || transform.flipY;\r\n}\r\nfunction transformForSvg(_ref) {\r\n  let {\r\n    transform,\r\n    containerWidth,\r\n    iconWidth\r\n  } = _ref;\r\n  const outer = {\r\n    transform: \"translate(\".concat(containerWidth / 2, \" 256)\")\r\n  };\r\n  const innerTranslate = \"translate(\".concat(transform.x * 32, \", \").concat(transform.y * 32, \") \");\r\n  const innerScale = \"scale(\".concat(transform.size / 16 * (transform.flipX ? -1 : 1), \", \").concat(transform.size / 16 * (transform.flipY ? -1 : 1), \") \");\r\n  const innerRotate = \"rotate(\".concat(transform.rotate, \" 0 0)\");\r\n  const inner = {\r\n    transform: \"\".concat(innerTranslate, \" \").concat(innerScale, \" \").concat(innerRotate)\r\n  };\r\n  const path = {\r\n    transform: \"translate(\".concat(iconWidth / 2 * -1, \" -256)\")\r\n  };\r\n  return {\r\n    outer,\r\n    inner,\r\n    path\r\n  };\r\n}\r\nfunction transformForCss(_ref2) {\r\n  let {\r\n    transform,\r\n    width = UNITS_IN_GRID,\r\n    height = UNITS_IN_GRID,\r\n    startCentered = false\r\n  } = _ref2;\r\n  let val = '';\r\n  if (startCentered && IS_IE) {\r\n    val += \"translate(\".concat(transform.x / d$2 - width / 2, \"em, \").concat(transform.y / d$2 - height / 2, \"em) \");\r\n  } else if (startCentered) {\r\n    val += \"translate(calc(-50% + \".concat(transform.x / d$2, \"em), calc(-50% + \").concat(transform.y / d$2, \"em)) \");\r\n  } else {\r\n    val += \"translate(\".concat(transform.x / d$2, \"em, \").concat(transform.y / d$2, \"em) \");\r\n  }\r\n  val += \"scale(\".concat(transform.size / d$2 * (transform.flipX ? -1 : 1), \", \").concat(transform.size / d$2 * (transform.flipY ? -1 : 1), \") \");\r\n  val += \"rotate(\".concat(transform.rotate, \"deg) \");\r\n  return val;\r\n}\r\n\r\nvar baseStyles = \":root, :host {\\n  --fa-font-solid: normal 900 1em/1 \\\"Font Awesome 6 Free\\\";\\n  --fa-font-regular: normal 400 1em/1 \\\"Font Awesome 6 Free\\\";\\n  --fa-font-light: normal 300 1em/1 \\\"Font Awesome 6 Pro\\\";\\n  --fa-font-thin: normal 100 1em/1 \\\"Font Awesome 6 Pro\\\";\\n  --fa-font-duotone: normal 900 1em/1 \\\"Font Awesome 6 Duotone\\\";\\n  --fa-font-duotone-regular: normal 400 1em/1 \\\"Font Awesome 6 Duotone\\\";\\n  --fa-font-duotone-light: normal 300 1em/1 \\\"Font Awesome 6 Duotone\\\";\\n  --fa-font-duotone-thin: normal 100 1em/1 \\\"Font Awesome 6 Duotone\\\";\\n  --fa-font-brands: normal 400 1em/1 \\\"Font Awesome 6 Brands\\\";\\n  --fa-font-sharp-solid: normal 900 1em/1 \\\"Font Awesome 6 Sharp\\\";\\n  --fa-font-sharp-regular: normal 400 1em/1 \\\"Font Awesome 6 Sharp\\\";\\n  --fa-font-sharp-light: normal 300 1em/1 \\\"Font Awesome 6 Sharp\\\";\\n  --fa-font-sharp-thin: normal 100 1em/1 \\\"Font Awesome 6 Sharp\\\";\\n  --fa-font-sharp-duotone-solid: normal 900 1em/1 \\\"Font Awesome 6 Sharp Duotone\\\";\\n  --fa-font-sharp-duotone-regular: normal 400 1em/1 \\\"Font Awesome 6 Sharp Duotone\\\";\\n  --fa-font-sharp-duotone-light: normal 300 1em/1 \\\"Font Awesome 6 Sharp Duotone\\\";\\n  --fa-font-sharp-duotone-thin: normal 100 1em/1 \\\"Font Awesome 6 Sharp Duotone\\\";\\n}\\n\\nsvg:not(:root).svg-inline--fa, svg:not(:host).svg-inline--fa {\\n  overflow: visible;\\n  box-sizing: content-box;\\n}\\n\\n.svg-inline--fa {\\n  display: var(--fa-display, inline-block);\\n  height: 1em;\\n  overflow: visible;\\n  vertical-align: -0.125em;\\n}\\n.svg-inline--fa.fa-2xs {\\n  vertical-align: 0.1em;\\n}\\n.svg-inline--fa.fa-xs {\\n  vertical-align: 0em;\\n}\\n.svg-inline--fa.fa-sm {\\n  vertical-align: -0.0714285705em;\\n}\\n.svg-inline--fa.fa-lg {\\n  vertical-align: -0.2em;\\n}\\n.svg-inline--fa.fa-xl {\\n  vertical-align: -0.25em;\\n}\\n.svg-inline--fa.fa-2xl {\\n  vertical-align: -0.3125em;\\n}\\n.svg-inline--fa.fa-pull-left {\\n  margin-right: var(--fa-pull-margin, 0.3em);\\n  width: auto;\\n}\\n.svg-inline--fa.fa-pull-right {\\n  margin-left: var(--fa-pull-margin, 0.3em);\\n  width: auto;\\n}\\n.svg-inline--fa.fa-li {\\n  width: var(--fa-li-width, 2em);\\n  top: 0.25em;\\n}\\n.svg-inline--fa.fa-fw {\\n  width: var(--fa-fw-width, 1.25em);\\n}\\n\\n.fa-layers svg.svg-inline--fa {\\n  bottom: 0;\\n  left: 0;\\n  margin: auto;\\n  position: absolute;\\n  right: 0;\\n  top: 0;\\n}\\n\\n.fa-layers-counter, .fa-layers-text {\\n  display: inline-block;\\n  position: absolute;\\n  text-align: center;\\n}\\n\\n.fa-layers {\\n  display: inline-block;\\n  height: 1em;\\n  position: relative;\\n  text-align: center;\\n  vertical-align: -0.125em;\\n  width: 1em;\\n}\\n.fa-layers svg.svg-inline--fa {\\n  transform-origin: center center;\\n}\\n\\n.fa-layers-text {\\n  left: 50%;\\n  top: 50%;\\n  transform: translate(-50%, -50%);\\n  transform-origin: center center;\\n}\\n\\n.fa-layers-counter {\\n  background-color: var(--fa-counter-background-color, #ff253a);\\n  border-radius: var(--fa-counter-border-radius, 1em);\\n  box-sizing: border-box;\\n  color: var(--fa-inverse, #fff);\\n  line-height: var(--fa-counter-line-height, 1);\\n  max-width: var(--fa-counter-max-width, 5em);\\n  min-width: var(--fa-counter-min-width, 1.5em);\\n  overflow: hidden;\\n  padding: var(--fa-counter-padding, 0.25em 0.5em);\\n  right: var(--fa-right, 0);\\n  text-overflow: ellipsis;\\n  top: var(--fa-top, 0);\\n  transform: scale(var(--fa-counter-scale, 0.25));\\n  transform-origin: top right;\\n}\\n\\n.fa-layers-bottom-right {\\n  bottom: var(--fa-bottom, 0);\\n  right: var(--fa-right, 0);\\n  top: auto;\\n  transform: scale(var(--fa-layers-scale, 0.25));\\n  transform-origin: bottom right;\\n}\\n\\n.fa-layers-bottom-left {\\n  bottom: var(--fa-bottom, 0);\\n  left: var(--fa-left, 0);\\n  right: auto;\\n  top: auto;\\n  transform: scale(var(--fa-layers-scale, 0.25));\\n  transform-origin: bottom left;\\n}\\n\\n.fa-layers-top-right {\\n  top: var(--fa-top, 0);\\n  right: var(--fa-right, 0);\\n  transform: scale(var(--fa-layers-scale, 0.25));\\n  transform-origin: top right;\\n}\\n\\n.fa-layers-top-left {\\n  left: var(--fa-left, 0);\\n  right: auto;\\n  top: var(--fa-top, 0);\\n  transform: scale(var(--fa-layers-scale, 0.25));\\n  transform-origin: top left;\\n}\\n\\n.fa-1x {\\n  font-size: 1em;\\n}\\n\\n.fa-2x {\\n  font-size: 2em;\\n}\\n\\n.fa-3x {\\n  font-size: 3em;\\n}\\n\\n.fa-4x {\\n  font-size: 4em;\\n}\\n\\n.fa-5x {\\n  font-size: 5em;\\n}\\n\\n.fa-6x {\\n  font-size: 6em;\\n}\\n\\n.fa-7x {\\n  font-size: 7em;\\n}\\n\\n.fa-8x {\\n  font-size: 8em;\\n}\\n\\n.fa-9x {\\n  font-size: 9em;\\n}\\n\\n.fa-10x {\\n  font-size: 10em;\\n}\\n\\n.fa-2xs {\\n  font-size: 0.625em;\\n  line-height: 0.1em;\\n  vertical-align: 0.225em;\\n}\\n\\n.fa-xs {\\n  font-size: 0.75em;\\n  line-height: 0.0833333337em;\\n  vertical-align: 0.125em;\\n}\\n\\n.fa-sm {\\n  font-size: 0.875em;\\n  line-height: 0.0714285718em;\\n  vertical-align: 0.0535714295em;\\n}\\n\\n.fa-lg {\\n  font-size: 1.25em;\\n  line-height: 0.05em;\\n  vertical-align: -0.075em;\\n}\\n\\n.fa-xl {\\n  font-size: 1.5em;\\n  line-height: 0.0416666682em;\\n  vertical-align: -0.125em;\\n}\\n\\n.fa-2xl {\\n  font-size: 2em;\\n  line-height: 0.03125em;\\n  vertical-align: -0.1875em;\\n}\\n\\n.fa-fw {\\n  text-align: center;\\n  width: 1.25em;\\n}\\n\\n.fa-ul {\\n  list-style-type: none;\\n  margin-left: var(--fa-li-margin, 2.5em);\\n  padding-left: 0;\\n}\\n.fa-ul > li {\\n  position: relative;\\n}\\n\\n.fa-li {\\n  left: calc(-1 * var(--fa-li-width, 2em));\\n  position: absolute;\\n  text-align: center;\\n  width: var(--fa-li-width, 2em);\\n  line-height: inherit;\\n}\\n\\n.fa-border {\\n  border-color: var(--fa-border-color, #eee);\\n  border-radius: var(--fa-border-radius, 0.1em);\\n  border-style: var(--fa-border-style, solid);\\n  border-width: var(--fa-border-width, 0.08em);\\n  padding: var(--fa-border-padding, 0.2em 0.25em 0.15em);\\n}\\n\\n.fa-pull-left {\\n  float: left;\\n  margin-right: var(--fa-pull-margin, 0.3em);\\n}\\n\\n.fa-pull-right {\\n  float: right;\\n  margin-left: var(--fa-pull-margin, 0.3em);\\n}\\n\\n.fa-beat {\\n  animation-name: fa-beat;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, ease-in-out);\\n}\\n\\n.fa-bounce {\\n  animation-name: fa-bounce;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1));\\n}\\n\\n.fa-fade {\\n  animation-name: fa-fade;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\\n}\\n\\n.fa-beat-fade {\\n  animation-name: fa-beat-fade;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\\n}\\n\\n.fa-flip {\\n  animation-name: fa-flip;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, ease-in-out);\\n}\\n\\n.fa-shake {\\n  animation-name: fa-shake;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, linear);\\n}\\n\\n.fa-spin {\\n  animation-name: fa-spin;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 2s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, linear);\\n}\\n\\n.fa-spin-reverse {\\n  --fa-animation-direction: reverse;\\n}\\n\\n.fa-pulse,\\n.fa-spin-pulse {\\n  animation-name: fa-spin;\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, steps(8));\\n}\\n\\n@media (prefers-reduced-motion: reduce) {\\n  .fa-beat,\\n.fa-bounce,\\n.fa-fade,\\n.fa-beat-fade,\\n.fa-flip,\\n.fa-pulse,\\n.fa-shake,\\n.fa-spin,\\n.fa-spin-pulse {\\n    animation-delay: -1ms;\\n    animation-duration: 1ms;\\n    animation-iteration-count: 1;\\n    transition-delay: 0s;\\n    transition-duration: 0s;\\n  }\\n}\\n@keyframes fa-beat {\\n  0%, 90% {\\n    transform: scale(1);\\n  }\\n  45% {\\n    transform: scale(var(--fa-beat-scale, 1.25));\\n  }\\n}\\n@keyframes fa-bounce {\\n  0% {\\n    transform: scale(1, 1) translateY(0);\\n  }\\n  10% {\\n    transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);\\n  }\\n  30% {\\n    transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));\\n  }\\n  50% {\\n    transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);\\n  }\\n  57% {\\n    transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));\\n  }\\n  64% {\\n    transform: scale(1, 1) translateY(0);\\n  }\\n  100% {\\n    transform: scale(1, 1) translateY(0);\\n  }\\n}\\n@keyframes fa-fade {\\n  50% {\\n    opacity: var(--fa-fade-opacity, 0.4);\\n  }\\n}\\n@keyframes fa-beat-fade {\\n  0%, 100% {\\n    opacity: var(--fa-beat-fade-opacity, 0.4);\\n    transform: scale(1);\\n  }\\n  50% {\\n    opacity: 1;\\n    transform: scale(var(--fa-beat-fade-scale, 1.125));\\n  }\\n}\\n@keyframes fa-flip {\\n  50% {\\n    transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));\\n  }\\n}\\n@keyframes fa-shake {\\n  0% {\\n    transform: rotate(-15deg);\\n  }\\n  4% {\\n    transform: rotate(15deg);\\n  }\\n  8%, 24% {\\n    transform: rotate(-18deg);\\n  }\\n  12%, 28% {\\n    transform: rotate(18deg);\\n  }\\n  16% {\\n    transform: rotate(-22deg);\\n  }\\n  20% {\\n    transform: rotate(22deg);\\n  }\\n  32% {\\n    transform: rotate(-12deg);\\n  }\\n  36% {\\n    transform: rotate(12deg);\\n  }\\n  40%, 100% {\\n    transform: rotate(0deg);\\n  }\\n}\\n@keyframes fa-spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.fa-rotate-90 {\\n  transform: rotate(90deg);\\n}\\n\\n.fa-rotate-180 {\\n  transform: rotate(180deg);\\n}\\n\\n.fa-rotate-270 {\\n  transform: rotate(270deg);\\n}\\n\\n.fa-flip-horizontal {\\n  transform: scale(-1, 1);\\n}\\n\\n.fa-flip-vertical {\\n  transform: scale(1, -1);\\n}\\n\\n.fa-flip-both,\\n.fa-flip-horizontal.fa-flip-vertical {\\n  transform: scale(-1, -1);\\n}\\n\\n.fa-rotate-by {\\n  transform: rotate(var(--fa-rotate-angle, 0));\\n}\\n\\n.fa-stack {\\n  display: inline-block;\\n  vertical-align: middle;\\n  height: 2em;\\n  position: relative;\\n  width: 2.5em;\\n}\\n\\n.fa-stack-1x,\\n.fa-stack-2x {\\n  bottom: 0;\\n  left: 0;\\n  margin: auto;\\n  position: absolute;\\n  right: 0;\\n  top: 0;\\n  z-index: var(--fa-stack-z-index, auto);\\n}\\n\\n.svg-inline--fa.fa-stack-1x {\\n  height: 1em;\\n  width: 1.25em;\\n}\\n.svg-inline--fa.fa-stack-2x {\\n  height: 2em;\\n  width: 2.5em;\\n}\\n\\n.fa-inverse {\\n  color: var(--fa-inverse, #fff);\\n}\\n\\n.sr-only,\\n.fa-sr-only {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\n\\n.sr-only-focusable:not(:focus),\\n.fa-sr-only-focusable:not(:focus) {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\n\\n.svg-inline--fa .fa-primary {\\n  fill: var(--fa-primary-color, currentColor);\\n  opacity: var(--fa-primary-opacity, 1);\\n}\\n\\n.svg-inline--fa .fa-secondary {\\n  fill: var(--fa-secondary-color, currentColor);\\n  opacity: var(--fa-secondary-opacity, 0.4);\\n}\\n\\n.svg-inline--fa.fa-swap-opacity .fa-primary {\\n  opacity: var(--fa-secondary-opacity, 0.4);\\n}\\n\\n.svg-inline--fa.fa-swap-opacity .fa-secondary {\\n  opacity: var(--fa-primary-opacity, 1);\\n}\\n\\n.svg-inline--fa mask .fa-primary,\\n.svg-inline--fa mask .fa-secondary {\\n  fill: black;\\n}\";\r\n\r\nfunction css() {\r\n  const dcp = DEFAULT_CSS_PREFIX;\r\n  const drc = DEFAULT_REPLACEMENT_CLASS;\r\n  const fp = config.cssPrefix;\r\n  const rc = config.replacementClass;\r\n  let s = baseStyles;\r\n  if (fp !== dcp || rc !== drc) {\r\n    const dPatt = new RegExp(\"\\\\.\".concat(dcp, \"\\\\-\"), 'g');\r\n    const customPropPatt = new RegExp(\"\\\\--\".concat(dcp, \"\\\\-\"), 'g');\r\n    const rPatt = new RegExp(\"\\\\.\".concat(drc), 'g');\r\n    s = s.replace(dPatt, \".\".concat(fp, \"-\")).replace(customPropPatt, \"--\".concat(fp, \"-\")).replace(rPatt, \".\".concat(rc));\r\n  }\r\n  return s;\r\n}\r\nlet _cssInserted = false;\r\nfunction ensureCss() {\r\n  if (config.autoAddCss && !_cssInserted) {\r\n    insertCss(css());\r\n    _cssInserted = true;\r\n  }\r\n}\r\nvar InjectCSS = {\r\n  mixout() {\r\n    return {\r\n      dom: {\r\n        css,\r\n        insertCss: ensureCss\r\n      }\r\n    };\r\n  },\r\n  hooks() {\r\n    return {\r\n      beforeDOMElementCreation() {\r\n        ensureCss();\r\n      },\r\n      beforeI2svg() {\r\n        ensureCss();\r\n      }\r\n    };\r\n  }\r\n};\r\n\r\nconst w = WINDOW || {};\r\nif (!w[NAMESPACE_IDENTIFIER]) w[NAMESPACE_IDENTIFIER] = {};\r\nif (!w[NAMESPACE_IDENTIFIER].styles) w[NAMESPACE_IDENTIFIER].styles = {};\r\nif (!w[NAMESPACE_IDENTIFIER].hooks) w[NAMESPACE_IDENTIFIER].hooks = {};\r\nif (!w[NAMESPACE_IDENTIFIER].shims) w[NAMESPACE_IDENTIFIER].shims = [];\r\nvar namespace = w[NAMESPACE_IDENTIFIER];\r\n\r\nconst functions = [];\r\nconst listener = function () {\r\n  DOCUMENT.removeEventListener('DOMContentLoaded', listener);\r\n  loaded = 1;\r\n  functions.map(fn => fn());\r\n};\r\nlet loaded = false;\r\nif (IS_DOM) {\r\n  loaded = (DOCUMENT.documentElement.doScroll ? /^loaded|^c/ : /^loaded|^i|^c/).test(DOCUMENT.readyState);\r\n  if (!loaded) DOCUMENT.addEventListener('DOMContentLoaded', listener);\r\n}\r\nfunction domready (fn) {\r\n  if (!IS_DOM) return;\r\n  loaded ? setTimeout(fn, 0) : functions.push(fn);\r\n}\r\n\r\nfunction toHtml(abstractNodes) {\r\n  const {\r\n    tag,\r\n    attributes = {},\r\n    children = []\r\n  } = abstractNodes;\r\n  if (typeof abstractNodes === 'string') {\r\n    return htmlEscape(abstractNodes);\r\n  } else {\r\n    return \"<\".concat(tag, \" \").concat(joinAttributes(attributes), \">\").concat(children.map(toHtml).join(''), \"</\").concat(tag, \">\");\r\n  }\r\n}\r\n\r\nfunction iconFromMapping(mapping, prefix, iconName) {\r\n  if (mapping && mapping[prefix] && mapping[prefix][iconName]) {\r\n    return {\r\n      prefix,\r\n      iconName,\r\n      icon: mapping[prefix][iconName]\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Internal helper to bind a function known to have 4 arguments\r\n * to a given context.\r\n */\r\nvar bindInternal4 = function bindInternal4(func, thisContext) {\r\n  return function (a, b, c, d) {\r\n    return func.call(thisContext, a, b, c, d);\r\n  };\r\n};\r\n\r\n/**\r\n * # Reduce\r\n *\r\n * A fast object `.reduce()` implementation.\r\n *\r\n * @param  {Object}   subject      The object to reduce over.\r\n * @param  {Function} fn           The reducer function.\r\n * @param  {mixed}    initialValue The initial value for the reducer, defaults to subject[0].\r\n * @param  {Object}   thisContext  The context for the reducer.\r\n * @return {mixed}                 The final result.\r\n */\r\nvar reduce = function fastReduceObject(subject, fn, initialValue, thisContext) {\r\n  var keys = Object.keys(subject),\r\n    length = keys.length,\r\n    iterator = thisContext !== undefined ? bindInternal4(fn, thisContext) : fn,\r\n    i,\r\n    key,\r\n    result;\r\n  if (initialValue === undefined) {\r\n    i = 1;\r\n    result = subject[keys[0]];\r\n  } else {\r\n    i = 0;\r\n    result = initialValue;\r\n  }\r\n  for (; i < length; i++) {\r\n    key = keys[i];\r\n    result = iterator(result, subject[key], key, subject);\r\n  }\r\n  return result;\r\n};\r\n\r\n/**\r\n * ucs2decode() and codePointAt() are both works of Mathias Bynens and licensed under MIT\r\n *\r\n * Copyright Mathias Bynens <https://mathiasbynens.be/>\r\n\r\n * Permission is hereby granted, free of charge, to any person obtaining\r\n * a copy of this software and associated documentation files (the\r\n * \"Software\"), to deal in the Software without restriction, including\r\n * without limitation the rights to use, copy, modify, merge, publish,\r\n * distribute, sublicense, and/or sell copies of the Software, and to\r\n * permit persons to whom the Software is furnished to do so, subject to\r\n * the following conditions:\r\n\r\n * The above copyright notice and this permission notice shall be\r\n * included in all copies or substantial portions of the Software.\r\n\r\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\r\n * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\r\n * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\r\n * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\r\n * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\r\n * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\r\n * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\r\n */\r\n\r\nfunction ucs2decode(string) {\r\n  const output = [];\r\n  let counter = 0;\r\n  const length = string.length;\r\n  while (counter < length) {\r\n    const value = string.charCodeAt(counter++);\r\n    if (value >= 0xD800 && value <= 0xDBFF && counter < length) {\r\n      const extra = string.charCodeAt(counter++);\r\n      if ((extra & 0xFC00) == 0xDC00) {\r\n        // eslint-disable-line eqeqeq\r\n        output.push(((value & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000);\r\n      } else {\r\n        output.push(value);\r\n        counter--;\r\n      }\r\n    } else {\r\n      output.push(value);\r\n    }\r\n  }\r\n  return output;\r\n}\r\nfunction toHex(unicode) {\r\n  const decoded = ucs2decode(unicode);\r\n  return decoded.length === 1 ? decoded[0].toString(16) : null;\r\n}\r\nfunction codePointAt(string, index) {\r\n  const size = string.length;\r\n  let first = string.charCodeAt(index);\r\n  let second;\r\n  if (first >= 0xD800 && first <= 0xDBFF && size > index + 1) {\r\n    second = string.charCodeAt(index + 1);\r\n    if (second >= 0xDC00 && second <= 0xDFFF) {\r\n      return (first - 0xD800) * 0x400 + second - 0xDC00 + 0x10000;\r\n    }\r\n  }\r\n  return first;\r\n}\r\n\r\nfunction normalizeIcons(icons) {\r\n  return Object.keys(icons).reduce((acc, iconName) => {\r\n    const icon = icons[iconName];\r\n    const expanded = !!icon.icon;\r\n    if (expanded) {\r\n      acc[icon.iconName] = icon.icon;\r\n    } else {\r\n      acc[iconName] = icon;\r\n    }\r\n    return acc;\r\n  }, {});\r\n}\r\nfunction defineIcons(prefix, icons) {\r\n  let params = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\r\n  const {\r\n    skipHooks = false\r\n  } = params;\r\n  const normalized = normalizeIcons(icons);\r\n  if (typeof namespace.hooks.addPack === 'function' && !skipHooks) {\r\n    namespace.hooks.addPack(prefix, normalizeIcons(icons));\r\n  } else {\r\n    namespace.styles[prefix] = _objectSpread2(_objectSpread2({}, namespace.styles[prefix] || {}), normalized);\r\n  }\r\n\r\n  /**\r\n   * Font Awesome 4 used the prefix of `fa` for all icons. With the introduction\r\n   * of new styles we needed to differentiate between them. Prefix `fa` is now an alias\r\n   * for `fas` so we'll ease the upgrade process for our users by automatically defining\r\n   * this as well.\r\n   */\r\n  if (prefix === 'fas') {\r\n    defineIcons('fa', icons);\r\n  }\r\n}\r\n\r\nconst duotonePathRe = [/*#__PURE__*/_wrapRegExp(/path d=\"([^\"]+)\".*path d=\"([^\"]+)\"/, {\r\n  d1: 1,\r\n  d2: 2\r\n}), /*#__PURE__*/_wrapRegExp(/path class=\"([^\"]+)\".*d=\"([^\"]+)\".*path class=\"([^\"]+)\".*d=\"([^\"]+)\"/, {\r\n  cls1: 1,\r\n  d1: 2,\r\n  cls2: 3,\r\n  d2: 4\r\n}), /*#__PURE__*/_wrapRegExp(/path class=\"([^\"]+)\".*d=\"([^\"]+)\"/, {\r\n  cls1: 1,\r\n  d1: 2\r\n})];\r\n\r\nconst {\r\n  styles,\r\n  shims\r\n} = namespace;\r\nconst FAMILY_NAMES = Object.keys(PREFIX_TO_LONG_STYLE);\r\nconst PREFIXES_FOR_FAMILY = FAMILY_NAMES.reduce((acc, familyId) => {\r\n  acc[familyId] = Object.keys(PREFIX_TO_LONG_STYLE[familyId]);\r\n  return acc;\r\n}, {});\r\nlet _defaultUsablePrefix = null;\r\nlet _byUnicode = {};\r\nlet _byLigature = {};\r\nlet _byOldName = {};\r\nlet _byOldUnicode = {};\r\nlet _byAlias = {};\r\nfunction isReserved(name) {\r\n  return ~RESERVED_CLASSES.indexOf(name);\r\n}\r\nfunction getIconName(cssPrefix, cls) {\r\n  const parts = cls.split('-');\r\n  const prefix = parts[0];\r\n  const iconName = parts.slice(1).join('-');\r\n  if (prefix === cssPrefix && iconName !== '' && !isReserved(iconName)) {\r\n    return iconName;\r\n  } else {\r\n    return null;\r\n  }\r\n}\r\nconst build = () => {\r\n  const lookup = reducer => {\r\n    return reduce(styles, (o$$1, style, prefix) => {\r\n      o$$1[prefix] = reduce(style, reducer, {});\r\n      return o$$1;\r\n    }, {});\r\n  };\r\n  _byUnicode = lookup((acc, icon, iconName) => {\r\n    if (icon[3]) {\r\n      acc[icon[3]] = iconName;\r\n    }\r\n    if (icon[2]) {\r\n      const aliases = icon[2].filter(a$$1 => {\r\n        return typeof a$$1 === 'number';\r\n      });\r\n      aliases.forEach(alias => {\r\n        acc[alias.toString(16)] = iconName;\r\n      });\r\n    }\r\n    return acc;\r\n  });\r\n  _byLigature = lookup((acc, icon, iconName) => {\r\n    acc[iconName] = iconName;\r\n    if (icon[2]) {\r\n      const aliases = icon[2].filter(a$$1 => {\r\n        return typeof a$$1 === 'string';\r\n      });\r\n      aliases.forEach(alias => {\r\n        acc[alias] = iconName;\r\n      });\r\n    }\r\n    return acc;\r\n  });\r\n  _byAlias = lookup((acc, icon, iconName) => {\r\n    const aliases = icon[2];\r\n    acc[iconName] = iconName;\r\n    aliases.forEach(alias => {\r\n      acc[alias] = iconName;\r\n    });\r\n    return acc;\r\n  });\r\n\r\n  // If we have a Kit, we can't determine if regular is available since we\r\n  // could be auto-fetching it. We'll have to assume that it is available.\r\n  const hasRegular = 'far' in styles || config.autoFetchSvg;\r\n  const shimLookups = reduce(shims, (acc, shim) => {\r\n    const maybeNameMaybeUnicode = shim[0];\r\n    let prefix = shim[1];\r\n    const iconName = shim[2];\r\n    if (prefix === 'far' && !hasRegular) {\r\n      prefix = 'fas';\r\n    }\r\n    if (typeof maybeNameMaybeUnicode === 'string') {\r\n      acc.names[maybeNameMaybeUnicode] = {\r\n        prefix,\r\n        iconName\r\n      };\r\n    }\r\n    if (typeof maybeNameMaybeUnicode === 'number') {\r\n      acc.unicodes[maybeNameMaybeUnicode.toString(16)] = {\r\n        prefix,\r\n        iconName\r\n      };\r\n    }\r\n    return acc;\r\n  }, {\r\n    names: {},\r\n    unicodes: {}\r\n  });\r\n  _byOldName = shimLookups.names;\r\n  _byOldUnicode = shimLookups.unicodes;\r\n  _defaultUsablePrefix = getCanonicalPrefix(config.styleDefault, {\r\n    family: config.familyDefault\r\n  });\r\n};\r\nonChange(c$$1 => {\r\n  _defaultUsablePrefix = getCanonicalPrefix(c$$1.styleDefault, {\r\n    family: config.familyDefault\r\n  });\r\n});\r\nbuild();\r\nfunction byUnicode(prefix, unicode) {\r\n  return (_byUnicode[prefix] || {})[unicode];\r\n}\r\nfunction byLigature(prefix, ligature) {\r\n  return (_byLigature[prefix] || {})[ligature];\r\n}\r\nfunction byAlias(prefix, alias) {\r\n  return (_byAlias[prefix] || {})[alias];\r\n}\r\nfunction byOldName(name) {\r\n  return _byOldName[name] || {\r\n    prefix: null,\r\n    iconName: null\r\n  };\r\n}\r\nfunction byOldUnicode(unicode) {\r\n  const oldUnicode = _byOldUnicode[unicode];\r\n  const newUnicode = byUnicode('fas', unicode);\r\n  return oldUnicode || (newUnicode ? {\r\n    prefix: 'fas',\r\n    iconName: newUnicode\r\n  } : null) || {\r\n    prefix: null,\r\n    iconName: null\r\n  };\r\n}\r\nfunction getDefaultUsablePrefix() {\r\n  return _defaultUsablePrefix;\r\n}\r\nconst emptyCanonicalIcon = () => {\r\n  return {\r\n    prefix: null,\r\n    iconName: null,\r\n    rest: []\r\n  };\r\n};\r\nfunction getFamilyId(values) {\r\n  let family = s;\r\n  const famProps = FAMILY_NAMES.reduce((acc, familyId) => {\r\n    acc[familyId] = \"\".concat(config.cssPrefix, \"-\").concat(familyId);\r\n    return acc;\r\n  }, {});\r\n  L.forEach(familyId => {\r\n    if (values.includes(famProps[familyId]) || values.some(v$$1 => PREFIXES_FOR_FAMILY[familyId].includes(v$$1))) {\r\n      family = familyId;\r\n    }\r\n  });\r\n  return family;\r\n}\r\nfunction getCanonicalPrefix(styleOrPrefix) {\r\n  let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\r\n  const {\r\n    family = s\r\n  } = params;\r\n  const style = PREFIX_TO_STYLE[family][styleOrPrefix];\r\n\r\n  // handles the exception of passing in only a family of 'duotone' with no style\r\n  if (family === t && !styleOrPrefix) {\r\n    return 'fad';\r\n  }\r\n  const prefix = STYLE_TO_PREFIX[family][styleOrPrefix] || STYLE_TO_PREFIX[family][style];\r\n  const defined = styleOrPrefix in namespace.styles ? styleOrPrefix : null;\r\n  const result = prefix || defined || null;\r\n  return result;\r\n}\r\nfunction moveNonFaClassesToRest(classNames) {\r\n  let rest = [];\r\n  let iconName = null;\r\n  classNames.forEach(cls => {\r\n    const result = getIconName(config.cssPrefix, cls);\r\n    if (result) {\r\n      iconName = result;\r\n    } else if (cls) {\r\n      rest.push(cls);\r\n    }\r\n  });\r\n  return {\r\n    iconName,\r\n    rest\r\n  };\r\n}\r\nfunction sortedUniqueValues(arr) {\r\n  return arr.sort().filter((value, index, arr) => {\r\n    return arr.indexOf(value) === index;\r\n  });\r\n}\r\nfunction getCanonicalIcon(values) {\r\n  let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\r\n  const {\r\n    skipLookups = false\r\n  } = params;\r\n  let givenPrefix = null;\r\n  const faCombinedClasses = Ia.concat(bt$1);\r\n  const faStyleOrFamilyClasses = sortedUniqueValues(values.filter(cls => faCombinedClasses.includes(cls)));\r\n  const nonStyleOrFamilyClasses = sortedUniqueValues(values.filter(cls => !Ia.includes(cls)));\r\n  const faStyles = faStyleOrFamilyClasses.filter(cls => {\r\n    givenPrefix = cls;\r\n    return !P.includes(cls);\r\n  });\r\n  const [styleFromValues = null] = faStyles;\r\n  const family = getFamilyId(faStyleOrFamilyClasses);\r\n  const canonical = _objectSpread2(_objectSpread2({}, moveNonFaClassesToRest(nonStyleOrFamilyClasses)), {}, {\r\n    prefix: getCanonicalPrefix(styleFromValues, {\r\n      family\r\n    })\r\n  });\r\n  return _objectSpread2(_objectSpread2(_objectSpread2({}, canonical), getDefaultCanonicalPrefix({\r\n    values,\r\n    family,\r\n    styles,\r\n    config,\r\n    canonical,\r\n    givenPrefix\r\n  })), applyShimAndAlias(skipLookups, givenPrefix, canonical));\r\n}\r\nfunction applyShimAndAlias(skipLookups, givenPrefix, canonical) {\r\n  let {\r\n    prefix,\r\n    iconName\r\n  } = canonical;\r\n  if (skipLookups || !prefix || !iconName) {\r\n    return {\r\n      prefix,\r\n      iconName\r\n    };\r\n  }\r\n  const shim = givenPrefix === 'fa' ? byOldName(iconName) : {};\r\n  const aliasIconName = byAlias(prefix, iconName);\r\n  iconName = shim.iconName || aliasIconName || iconName;\r\n  prefix = shim.prefix || prefix;\r\n  if (prefix === 'far' && !styles['far'] && styles['fas'] && !config.autoFetchSvg) {\r\n    // Allow a fallback from the regular style to solid if regular is not available\r\n    // but only if we aren't auto-fetching SVGs\r\n    prefix = 'fas';\r\n  }\r\n  return {\r\n    prefix,\r\n    iconName\r\n  };\r\n}\r\nconst newCanonicalFamilies = L.filter(familyId => {\r\n  return familyId !== s || familyId !== t;\r\n});\r\nconst newCanonicalStyles = Object.keys(ga).filter(key => key !== s).map(key => Object.keys(ga[key])).flat();\r\nfunction getDefaultCanonicalPrefix(prefixOptions) {\r\n  const {\r\n    values,\r\n    family,\r\n    canonical,\r\n    givenPrefix = '',\r\n    styles = {},\r\n    config: config$$1 = {}\r\n  } = prefixOptions;\r\n  const isDuotoneFamily = family === t;\r\n  const valuesHasDuotone = values.includes('fa-duotone') || values.includes('fad');\r\n  const defaultFamilyIsDuotone = config$$1.familyDefault === 'duotone';\r\n  const canonicalPrefixIsDuotone = canonical.prefix === 'fad' || canonical.prefix === 'fa-duotone';\r\n  if (!isDuotoneFamily && (valuesHasDuotone || defaultFamilyIsDuotone || canonicalPrefixIsDuotone)) {\r\n    canonical.prefix = 'fad';\r\n  }\r\n  if (values.includes('fa-brands') || values.includes('fab')) {\r\n    canonical.prefix = 'fab';\r\n  }\r\n  if (!canonical.prefix && newCanonicalFamilies.includes(family)) {\r\n    const validPrefix = Object.keys(styles).find(key => newCanonicalStyles.includes(key));\r\n    if (validPrefix || config$$1.autoFetchSvg) {\r\n      const defaultPrefix = pt.get(family).defaultShortPrefixId;\r\n      canonical.prefix = defaultPrefix;\r\n      canonical.iconName = byAlias(canonical.prefix, canonical.iconName) || canonical.iconName;\r\n    }\r\n  }\r\n  if (canonical.prefix === 'fa' || givenPrefix === 'fa') {\r\n    // The fa prefix is not canonical. So if it has made it through until this point\r\n    // we will shift it to the correct prefix.\r\n    canonical.prefix = getDefaultUsablePrefix() || 'fas';\r\n  }\r\n  return canonical;\r\n}\r\n\r\nclass Library {\r\n  constructor() {\r\n    this.definitions = {};\r\n  }\r\n  add() {\r\n    for (var _len = arguments.length, definitions = new Array(_len), _key = 0; _key < _len; _key++) {\r\n      definitions[_key] = arguments[_key];\r\n    }\r\n    const additions = definitions.reduce(this._pullDefinitions, {});\r\n    Object.keys(additions).forEach(key => {\r\n      this.definitions[key] = _objectSpread2(_objectSpread2({}, this.definitions[key] || {}), additions[key]);\r\n      defineIcons(key, additions[key]);\r\n\r\n      // TODO can we stop doing this? We can't get the icons by 'fa-solid' any longer so this probably needs to change\r\n      const longPrefix = PREFIX_TO_LONG_STYLE[s][key];\r\n      if (longPrefix) defineIcons(longPrefix, additions[key]);\r\n      build();\r\n    });\r\n  }\r\n  reset() {\r\n    this.definitions = {};\r\n  }\r\n  _pullDefinitions(additions, definition) {\r\n    const normalized = definition.prefix && definition.iconName && definition.icon ? {\r\n      0: definition\r\n    } : definition;\r\n    Object.keys(normalized).map(key => {\r\n      const {\r\n        prefix,\r\n        iconName,\r\n        icon\r\n      } = normalized[key];\r\n      const aliases = icon[2];\r\n      if (!additions[prefix]) additions[prefix] = {};\r\n      if (aliases.length > 0) {\r\n        aliases.forEach(alias => {\r\n          if (typeof alias === 'string') {\r\n            additions[prefix][alias] = icon;\r\n          }\r\n        });\r\n      }\r\n      additions[prefix][iconName] = icon;\r\n    });\r\n    return additions;\r\n  }\r\n}\r\n\r\nlet _plugins = [];\r\nlet _hooks = {};\r\nconst providers = {};\r\nconst defaultProviderKeys = Object.keys(providers);\r\nfunction registerPlugins(nextPlugins, _ref) {\r\n  let {\r\n    mixoutsTo: obj\r\n  } = _ref;\r\n  _plugins = nextPlugins;\r\n  _hooks = {};\r\n  Object.keys(providers).forEach(k => {\r\n    if (defaultProviderKeys.indexOf(k) === -1) {\r\n      delete providers[k];\r\n    }\r\n  });\r\n  _plugins.forEach(plugin => {\r\n    const mixout = plugin.mixout ? plugin.mixout() : {};\r\n    Object.keys(mixout).forEach(tk => {\r\n      if (typeof mixout[tk] === 'function') {\r\n        obj[tk] = mixout[tk];\r\n      }\r\n      if (typeof mixout[tk] === 'object') {\r\n        Object.keys(mixout[tk]).forEach(sk => {\r\n          if (!obj[tk]) {\r\n            obj[tk] = {};\r\n          }\r\n          obj[tk][sk] = mixout[tk][sk];\r\n        });\r\n      }\r\n    });\r\n    if (plugin.hooks) {\r\n      const hooks = plugin.hooks();\r\n      Object.keys(hooks).forEach(hook => {\r\n        if (!_hooks[hook]) {\r\n          _hooks[hook] = [];\r\n        }\r\n        _hooks[hook].push(hooks[hook]);\r\n      });\r\n    }\r\n    if (plugin.provides) {\r\n      plugin.provides(providers);\r\n    }\r\n  });\r\n  return obj;\r\n}\r\nfunction chainHooks(hook, accumulator) {\r\n  for (var _len = arguments.length, args = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\r\n    args[_key - 2] = arguments[_key];\r\n  }\r\n  const hookFns = _hooks[hook] || [];\r\n  hookFns.forEach(hookFn => {\r\n    accumulator = hookFn.apply(null, [accumulator, ...args]); // eslint-disable-line no-useless-call\r\n  });\r\n  return accumulator;\r\n}\r\nfunction callHooks(hook) {\r\n  for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\r\n    args[_key2 - 1] = arguments[_key2];\r\n  }\r\n  const hookFns = _hooks[hook] || [];\r\n  hookFns.forEach(hookFn => {\r\n    hookFn.apply(null, args);\r\n  });\r\n  return undefined;\r\n}\r\nfunction callProvided() {\r\n  const hook = arguments[0];\r\n  const args = Array.prototype.slice.call(arguments, 1);\r\n  return providers[hook] ? providers[hook].apply(null, args) : undefined;\r\n}\r\n\r\nfunction findIconDefinition(iconLookup) {\r\n  if (iconLookup.prefix === 'fa') {\r\n    iconLookup.prefix = 'fas';\r\n  }\r\n  let {\r\n    iconName\r\n  } = iconLookup;\r\n  const prefix = iconLookup.prefix || getDefaultUsablePrefix();\r\n  if (!iconName) return;\r\n  iconName = byAlias(prefix, iconName) || iconName;\r\n  return iconFromMapping(library.definitions, prefix, iconName) || iconFromMapping(namespace.styles, prefix, iconName);\r\n}\r\nconst library = new Library();\r\nconst noAuto = () => {\r\n  config.autoReplaceSvg = false;\r\n  config.observeMutations = false;\r\n  callHooks('noAuto');\r\n};\r\nconst dom = {\r\n  i2svg: function () {\r\n    let params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\r\n    if (IS_DOM) {\r\n      callHooks('beforeI2svg', params);\r\n      callProvided('pseudoElements2svg', params);\r\n      return callProvided('i2svg', params);\r\n    } else {\r\n      return Promise.reject(new Error('Operation requires a DOM of some kind.'));\r\n    }\r\n  },\r\n  watch: function () {\r\n    let params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\r\n    const {\r\n      autoReplaceSvgRoot\r\n    } = params;\r\n    if (config.autoReplaceSvg === false) {\r\n      config.autoReplaceSvg = true;\r\n    }\r\n    config.observeMutations = true;\r\n    domready(() => {\r\n      autoReplace({\r\n        autoReplaceSvgRoot\r\n      });\r\n      callHooks('watch', params);\r\n    });\r\n  }\r\n};\r\nconst parse = {\r\n  icon: icon => {\r\n    if (icon === null) {\r\n      return null;\r\n    }\r\n    if (typeof icon === 'object' && icon.prefix && icon.iconName) {\r\n      return {\r\n        prefix: icon.prefix,\r\n        iconName: byAlias(icon.prefix, icon.iconName) || icon.iconName\r\n      };\r\n    }\r\n    if (Array.isArray(icon) && icon.length === 2) {\r\n      const iconName = icon[1].indexOf('fa-') === 0 ? icon[1].slice(3) : icon[1];\r\n      const prefix = getCanonicalPrefix(icon[0]);\r\n      return {\r\n        prefix,\r\n        iconName: byAlias(prefix, iconName) || iconName\r\n      };\r\n    }\r\n    if (typeof icon === 'string' && (icon.indexOf(\"\".concat(config.cssPrefix, \"-\")) > -1 || icon.match(ICON_SELECTION_SYNTAX_PATTERN))) {\r\n      const canonicalIcon = getCanonicalIcon(icon.split(' '), {\r\n        skipLookups: true\r\n      });\r\n      return {\r\n        prefix: canonicalIcon.prefix || getDefaultUsablePrefix(),\r\n        iconName: byAlias(canonicalIcon.prefix, canonicalIcon.iconName) || canonicalIcon.iconName\r\n      };\r\n    }\r\n    if (typeof icon === 'string') {\r\n      const prefix = getDefaultUsablePrefix();\r\n      return {\r\n        prefix,\r\n        iconName: byAlias(prefix, icon) || icon\r\n      };\r\n    }\r\n  }\r\n};\r\nconst api = {\r\n  noAuto,\r\n  config,\r\n  dom,\r\n  parse,\r\n  library,\r\n  findIconDefinition,\r\n  toHtml\r\n};\r\nconst autoReplace = function () {\r\n  let params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\r\n  const {\r\n    autoReplaceSvgRoot = DOCUMENT\r\n  } = params;\r\n  if ((Object.keys(namespace.styles).length > 0 || config.autoFetchSvg) && IS_DOM && config.autoReplaceSvg) api.dom.i2svg({\r\n    node: autoReplaceSvgRoot\r\n  });\r\n};\r\n\r\nfunction domVariants(val, abstractCreator) {\r\n  Object.defineProperty(val, 'abstract', {\r\n    get: abstractCreator\r\n  });\r\n  Object.defineProperty(val, 'html', {\r\n    get: function () {\r\n      return val.abstract.map(a => toHtml(a));\r\n    }\r\n  });\r\n  Object.defineProperty(val, 'node', {\r\n    get: function () {\r\n      if (!IS_DOM) return;\r\n      const container = DOCUMENT.createElement('div');\r\n      container.innerHTML = val.html;\r\n      return container.children;\r\n    }\r\n  });\r\n  return val;\r\n}\r\n\r\nfunction asIcon (_ref) {\r\n  let {\r\n    children,\r\n    main,\r\n    mask,\r\n    attributes,\r\n    styles,\r\n    transform\r\n  } = _ref;\r\n  if (transformIsMeaningful(transform) && main.found && !mask.found) {\r\n    const {\r\n      width,\r\n      height\r\n    } = main;\r\n    const offset = {\r\n      x: width / height / 2,\r\n      y: 0.5\r\n    };\r\n    attributes['style'] = joinStyles(_objectSpread2(_objectSpread2({}, styles), {}, {\r\n      'transform-origin': \"\".concat(offset.x + transform.x / 16, \"em \").concat(offset.y + transform.y / 16, \"em\")\r\n    }));\r\n  }\r\n  return [{\r\n    tag: 'svg',\r\n    attributes,\r\n    children\r\n  }];\r\n}\r\n\r\nfunction asSymbol (_ref) {\r\n  let {\r\n    prefix,\r\n    iconName,\r\n    children,\r\n    attributes,\r\n    symbol\r\n  } = _ref;\r\n  const id = symbol === true ? \"\".concat(prefix, \"-\").concat(config.cssPrefix, \"-\").concat(iconName) : symbol;\r\n  return [{\r\n    tag: 'svg',\r\n    attributes: {\r\n      style: 'display: none;'\r\n    },\r\n    children: [{\r\n      tag: 'symbol',\r\n      attributes: _objectSpread2(_objectSpread2({}, attributes), {}, {\r\n        id\r\n      }),\r\n      children\r\n    }]\r\n  }];\r\n}\r\n\r\nfunction makeInlineSvgAbstract(params) {\r\n  const {\r\n    icons: {\r\n      main,\r\n      mask\r\n    },\r\n    prefix,\r\n    iconName,\r\n    transform,\r\n    symbol,\r\n    title,\r\n    maskId,\r\n    titleId,\r\n    extra,\r\n    watchable = false\r\n  } = params;\r\n  const {\r\n    width,\r\n    height\r\n  } = mask.found ? mask : main;\r\n  const isUploadedIcon = Lt.includes(prefix);\r\n  const attrClass = [config.replacementClass, iconName ? \"\".concat(config.cssPrefix, \"-\").concat(iconName) : ''].filter(c$$1 => extra.classes.indexOf(c$$1) === -1).filter(c$$1 => c$$1 !== '' || !!c$$1).concat(extra.classes).join(' ');\r\n  let content = {\r\n    children: [],\r\n    attributes: _objectSpread2(_objectSpread2({}, extra.attributes), {}, {\r\n      'data-prefix': prefix,\r\n      'data-icon': iconName,\r\n      'class': attrClass,\r\n      'role': extra.attributes.role || 'img',\r\n      'xmlns': 'http://www.w3.org/2000/svg',\r\n      'viewBox': \"0 0 \".concat(width, \" \").concat(height)\r\n    })\r\n  };\r\n  const uploadedIconWidthStyle = isUploadedIcon && !~extra.classes.indexOf('fa-fw') ? {\r\n    width: \"\".concat(width / height * 16 * 0.0625, \"em\")\r\n  } : {};\r\n  if (watchable) {\r\n    content.attributes[DATA_FA_I2SVG] = '';\r\n  }\r\n  if (title) {\r\n    content.children.push({\r\n      tag: 'title',\r\n      attributes: {\r\n        id: content.attributes['aria-labelledby'] || \"title-\".concat(titleId || nextUniqueId())\r\n      },\r\n      children: [title]\r\n    });\r\n    delete content.attributes.title;\r\n  }\r\n  const args = _objectSpread2(_objectSpread2({}, content), {}, {\r\n    prefix,\r\n    iconName,\r\n    main,\r\n    mask,\r\n    maskId,\r\n    transform,\r\n    symbol,\r\n    styles: _objectSpread2(_objectSpread2({}, uploadedIconWidthStyle), extra.styles)\r\n  });\r\n  const {\r\n    children,\r\n    attributes\r\n  } = mask.found && main.found ? callProvided('generateAbstractMask', args) || {\r\n    children: [],\r\n    attributes: {}\r\n  } : callProvided('generateAbstractIcon', args) || {\r\n    children: [],\r\n    attributes: {}\r\n  };\r\n  args.children = children;\r\n  args.attributes = attributes;\r\n  if (symbol) {\r\n    return asSymbol(args);\r\n  } else {\r\n    return asIcon(args);\r\n  }\r\n}\r\nfunction makeLayersTextAbstract(params) {\r\n  const {\r\n    content,\r\n    width,\r\n    height,\r\n    transform,\r\n    title,\r\n    extra,\r\n    watchable = false\r\n  } = params;\r\n  const attributes = _objectSpread2(_objectSpread2(_objectSpread2({}, extra.attributes), title ? {\r\n    'title': title\r\n  } : {}), {}, {\r\n    'class': extra.classes.join(' ')\r\n  });\r\n  if (watchable) {\r\n    attributes[DATA_FA_I2SVG] = '';\r\n  }\r\n  const styles = _objectSpread2({}, extra.styles);\r\n  if (transformIsMeaningful(transform)) {\r\n    styles['transform'] = transformForCss({\r\n      transform,\r\n      startCentered: true,\r\n      width,\r\n      height\r\n    });\r\n    styles['-webkit-transform'] = styles['transform'];\r\n  }\r\n  const styleString = joinStyles(styles);\r\n  if (styleString.length > 0) {\r\n    attributes['style'] = styleString;\r\n  }\r\n  const val = [];\r\n  val.push({\r\n    tag: 'span',\r\n    attributes,\r\n    children: [content]\r\n  });\r\n  if (title) {\r\n    val.push({\r\n      tag: 'span',\r\n      attributes: {\r\n        class: 'sr-only'\r\n      },\r\n      children: [title]\r\n    });\r\n  }\r\n  return val;\r\n}\r\nfunction makeLayersCounterAbstract(params) {\r\n  const {\r\n    content,\r\n    title,\r\n    extra\r\n  } = params;\r\n  const attributes = _objectSpread2(_objectSpread2(_objectSpread2({}, extra.attributes), title ? {\r\n    'title': title\r\n  } : {}), {}, {\r\n    'class': extra.classes.join(' ')\r\n  });\r\n  const styleString = joinStyles(extra.styles);\r\n  if (styleString.length > 0) {\r\n    attributes['style'] = styleString;\r\n  }\r\n  const val = [];\r\n  val.push({\r\n    tag: 'span',\r\n    attributes,\r\n    children: [content]\r\n  });\r\n  if (title) {\r\n    val.push({\r\n      tag: 'span',\r\n      attributes: {\r\n        class: 'sr-only'\r\n      },\r\n      children: [title]\r\n    });\r\n  }\r\n  return val;\r\n}\r\n\r\nconst {\r\n  styles: styles$1\r\n} = namespace;\r\nfunction asFoundIcon(icon) {\r\n  const width = icon[0];\r\n  const height = icon[1];\r\n  const [vectorData] = icon.slice(4);\r\n  let element = null;\r\n  if (Array.isArray(vectorData)) {\r\n    element = {\r\n      tag: 'g',\r\n      attributes: {\r\n        class: \"\".concat(config.cssPrefix, \"-\").concat(DUOTONE_CLASSES.GROUP)\r\n      },\r\n      children: [{\r\n        tag: 'path',\r\n        attributes: {\r\n          class: \"\".concat(config.cssPrefix, \"-\").concat(DUOTONE_CLASSES.SECONDARY),\r\n          fill: 'currentColor',\r\n          d: vectorData[0]\r\n        }\r\n      }, {\r\n        tag: 'path',\r\n        attributes: {\r\n          class: \"\".concat(config.cssPrefix, \"-\").concat(DUOTONE_CLASSES.PRIMARY),\r\n          fill: 'currentColor',\r\n          d: vectorData[1]\r\n        }\r\n      }]\r\n    };\r\n  } else {\r\n    element = {\r\n      tag: 'path',\r\n      attributes: {\r\n        fill: 'currentColor',\r\n        d: vectorData\r\n      }\r\n    };\r\n  }\r\n  return {\r\n    found: true,\r\n    width,\r\n    height,\r\n    icon: element\r\n  };\r\n}\r\nconst missingIconResolutionMixin = {\r\n  found: false,\r\n  width: 512,\r\n  height: 512\r\n};\r\nfunction maybeNotifyMissing(iconName, prefix) {\r\n  if (!PRODUCTION && !config.showMissingIcons && iconName) {\r\n    console.error(\"Icon with name \\\"\".concat(iconName, \"\\\" and prefix \\\"\").concat(prefix, \"\\\" is missing.\"));\r\n  }\r\n}\r\nfunction findIcon(iconName, prefix) {\r\n  let givenPrefix = prefix;\r\n  if (prefix === 'fa' && config.styleDefault !== null) {\r\n    prefix = getDefaultUsablePrefix();\r\n  }\r\n  return new Promise((resolve, reject) => {\r\n    if (givenPrefix === 'fa') {\r\n      const shim = byOldName(iconName) || {};\r\n      iconName = shim.iconName || iconName;\r\n      prefix = shim.prefix || prefix;\r\n    }\r\n    if (iconName && prefix && styles$1[prefix] && styles$1[prefix][iconName]) {\r\n      const icon = styles$1[prefix][iconName];\r\n      return resolve(asFoundIcon(icon));\r\n    }\r\n    maybeNotifyMissing(iconName, prefix);\r\n    resolve(_objectSpread2(_objectSpread2({}, missingIconResolutionMixin), {}, {\r\n      icon: config.showMissingIcons && iconName ? callProvided('missingIconAbstract') || {} : {}\r\n    }));\r\n  });\r\n}\r\n\r\nconst noop$1 = () => {};\r\nconst p$2 = config.measurePerformance && PERFORMANCE && PERFORMANCE.mark && PERFORMANCE.measure ? PERFORMANCE : {\r\n  mark: noop$1,\r\n  measure: noop$1\r\n};\r\nconst preamble = \"FA \\\"6.7.2\\\"\";\r\nconst begin = name => {\r\n  p$2.mark(\"\".concat(preamble, \" \").concat(name, \" begins\"));\r\n  return () => end(name);\r\n};\r\nconst end = name => {\r\n  p$2.mark(\"\".concat(preamble, \" \").concat(name, \" ends\"));\r\n  p$2.measure(\"\".concat(preamble, \" \").concat(name), \"\".concat(preamble, \" \").concat(name, \" begins\"), \"\".concat(preamble, \" \").concat(name, \" ends\"));\r\n};\r\nvar perf = {\r\n  begin,\r\n  end\r\n};\r\n\r\nconst noop$2 = () => {};\r\nfunction isWatched(node) {\r\n  const i2svg = node.getAttribute ? node.getAttribute(DATA_FA_I2SVG) : null;\r\n  return typeof i2svg === 'string';\r\n}\r\nfunction hasPrefixAndIcon(node) {\r\n  const prefix = node.getAttribute ? node.getAttribute(DATA_PREFIX) : null;\r\n  const icon = node.getAttribute ? node.getAttribute(DATA_ICON) : null;\r\n  return prefix && icon;\r\n}\r\nfunction hasBeenReplaced(node) {\r\n  return node && node.classList && node.classList.contains && node.classList.contains(config.replacementClass);\r\n}\r\nfunction getMutator() {\r\n  if (config.autoReplaceSvg === true) {\r\n    return mutators.replace;\r\n  }\r\n  const mutator = mutators[config.autoReplaceSvg];\r\n  return mutator || mutators.replace;\r\n}\r\nfunction createElementNS(tag) {\r\n  return DOCUMENT.createElementNS('http://www.w3.org/2000/svg', tag);\r\n}\r\nfunction createElement(tag) {\r\n  return DOCUMENT.createElement(tag);\r\n}\r\nfunction convertSVG(abstractObj) {\r\n  let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\r\n  const {\r\n    ceFn = abstractObj.tag === 'svg' ? createElementNS : createElement\r\n  } = params;\r\n  if (typeof abstractObj === 'string') {\r\n    return DOCUMENT.createTextNode(abstractObj);\r\n  }\r\n  const tag = ceFn(abstractObj.tag);\r\n  Object.keys(abstractObj.attributes || []).forEach(function (key) {\r\n    tag.setAttribute(key, abstractObj.attributes[key]);\r\n  });\r\n  const children = abstractObj.children || [];\r\n  children.forEach(function (child) {\r\n    tag.appendChild(convertSVG(child, {\r\n      ceFn\r\n    }));\r\n  });\r\n  return tag;\r\n}\r\nfunction nodeAsComment(node) {\r\n  let comment = \" \".concat(node.outerHTML, \" \");\r\n  /* BEGIN.ATTRIBUTION */\r\n  comment = \"\".concat(comment, \"Font Awesome fontawesome.com \");\r\n  /* END.ATTRIBUTION */\r\n  return comment;\r\n}\r\nconst mutators = {\r\n  replace: function (mutation) {\r\n    const node = mutation[0];\r\n    if (node.parentNode) {\r\n      mutation[1].forEach(abstract => {\r\n        node.parentNode.insertBefore(convertSVG(abstract), node);\r\n      });\r\n      if (node.getAttribute(DATA_FA_I2SVG) === null && config.keepOriginalSource) {\r\n        let comment = DOCUMENT.createComment(nodeAsComment(node));\r\n        node.parentNode.replaceChild(comment, node);\r\n      } else {\r\n        node.remove();\r\n      }\r\n    }\r\n  },\r\n  nest: function (mutation) {\r\n    const node = mutation[0];\r\n    const abstract = mutation[1];\r\n\r\n    // If we already have a replaced node we do not want to continue nesting within it.\r\n    // Short-circuit to the standard replacement\r\n    if (~classArray(node).indexOf(config.replacementClass)) {\r\n      return mutators.replace(mutation);\r\n    }\r\n    const forSvg = new RegExp(\"\".concat(config.cssPrefix, \"-.*\"));\r\n    delete abstract[0].attributes.id;\r\n    if (abstract[0].attributes.class) {\r\n      const splitClasses = abstract[0].attributes.class.split(' ').reduce((acc, cls) => {\r\n        if (cls === config.replacementClass || cls.match(forSvg)) {\r\n          acc.toSvg.push(cls);\r\n        } else {\r\n          acc.toNode.push(cls);\r\n        }\r\n        return acc;\r\n      }, {\r\n        toNode: [],\r\n        toSvg: []\r\n      });\r\n      abstract[0].attributes.class = splitClasses.toSvg.join(' ');\r\n      if (splitClasses.toNode.length === 0) {\r\n        node.removeAttribute('class');\r\n      } else {\r\n        node.setAttribute('class', splitClasses.toNode.join(' '));\r\n      }\r\n    }\r\n    const newInnerHTML = abstract.map(a => toHtml(a)).join('\\n');\r\n    node.setAttribute(DATA_FA_I2SVG, '');\r\n    node.innerHTML = newInnerHTML;\r\n  }\r\n};\r\nfunction performOperationSync(op) {\r\n  op();\r\n}\r\nfunction perform(mutations, callback) {\r\n  const callbackFunction = typeof callback === 'function' ? callback : noop$2;\r\n  if (mutations.length === 0) {\r\n    callbackFunction();\r\n  } else {\r\n    let frame = performOperationSync;\r\n    if (config.mutateApproach === MUTATION_APPROACH_ASYNC) {\r\n      frame = WINDOW.requestAnimationFrame || performOperationSync;\r\n    }\r\n    frame(() => {\r\n      const mutator = getMutator();\r\n      const mark = perf.begin('mutate');\r\n      mutations.map(mutator);\r\n      mark();\r\n      callbackFunction();\r\n    });\r\n  }\r\n}\r\nlet disabled = false;\r\nfunction disableObservation() {\r\n  disabled = true;\r\n}\r\nfunction enableObservation() {\r\n  disabled = false;\r\n}\r\nlet mo = null;\r\nfunction observe(options) {\r\n  if (!MUTATION_OBSERVER) {\r\n    return;\r\n  }\r\n  if (!config.observeMutations) {\r\n    return;\r\n  }\r\n  const {\r\n    treeCallback = noop$2,\r\n    nodeCallback = noop$2,\r\n    pseudoElementsCallback = noop$2,\r\n    observeMutationsRoot = DOCUMENT\r\n  } = options;\r\n  mo = new MUTATION_OBSERVER(objects => {\r\n    if (disabled) return;\r\n    const defaultPrefix = getDefaultUsablePrefix();\r\n    toArray(objects).forEach(mutationRecord => {\r\n      if (mutationRecord.type === 'childList' && mutationRecord.addedNodes.length > 0 && !isWatched(mutationRecord.addedNodes[0])) {\r\n        if (config.searchPseudoElements) {\r\n          pseudoElementsCallback(mutationRecord.target);\r\n        }\r\n        treeCallback(mutationRecord.target);\r\n      }\r\n      if (mutationRecord.type === 'attributes' && mutationRecord.target.parentNode && config.searchPseudoElements) {\r\n        pseudoElementsCallback(mutationRecord.target.parentNode);\r\n      }\r\n      if (mutationRecord.type === 'attributes' && isWatched(mutationRecord.target) && ~ATTRIBUTES_WATCHED_FOR_MUTATION.indexOf(mutationRecord.attributeName)) {\r\n        if (mutationRecord.attributeName === 'class' && hasPrefixAndIcon(mutationRecord.target)) {\r\n          const {\r\n            prefix,\r\n            iconName\r\n          } = getCanonicalIcon(classArray(mutationRecord.target));\r\n          mutationRecord.target.setAttribute(DATA_PREFIX, prefix || defaultPrefix);\r\n          if (iconName) mutationRecord.target.setAttribute(DATA_ICON, iconName);\r\n        } else if (hasBeenReplaced(mutationRecord.target)) {\r\n          nodeCallback(mutationRecord.target);\r\n        }\r\n      }\r\n    });\r\n  });\r\n  if (!IS_DOM) return;\r\n  mo.observe(observeMutationsRoot, {\r\n    childList: true,\r\n    attributes: true,\r\n    characterData: true,\r\n    subtree: true\r\n  });\r\n}\r\nfunction disconnect() {\r\n  if (!mo) return;\r\n  mo.disconnect();\r\n}\r\n\r\nfunction styleParser (node) {\r\n  const style = node.getAttribute('style');\r\n  let val = [];\r\n  if (style) {\r\n    val = style.split(';').reduce((acc, style) => {\r\n      const styles = style.split(':');\r\n      const prop = styles[0];\r\n      const value = styles.slice(1);\r\n      if (prop && value.length > 0) {\r\n        acc[prop] = value.join(':').trim();\r\n      }\r\n      return acc;\r\n    }, {});\r\n  }\r\n  return val;\r\n}\r\n\r\nfunction classParser (node) {\r\n  const existingPrefix = node.getAttribute('data-prefix');\r\n  const existingIconName = node.getAttribute('data-icon');\r\n  const innerText = node.innerText !== undefined ? node.innerText.trim() : '';\r\n  let val = getCanonicalIcon(classArray(node));\r\n  if (!val.prefix) {\r\n    val.prefix = getDefaultUsablePrefix();\r\n  }\r\n  if (existingPrefix && existingIconName) {\r\n    val.prefix = existingPrefix;\r\n    val.iconName = existingIconName;\r\n  }\r\n  if (val.iconName && val.prefix) {\r\n    return val;\r\n  }\r\n  if (val.prefix && innerText.length > 0) {\r\n    val.iconName = byLigature(val.prefix, node.innerText) || byUnicode(val.prefix, toHex(node.innerText));\r\n  }\r\n  if (!val.iconName && config.autoFetchSvg && node.firstChild && node.firstChild.nodeType === Node.TEXT_NODE) {\r\n    val.iconName = node.firstChild.data;\r\n  }\r\n  return val;\r\n}\r\n\r\nfunction attributesParser (node) {\r\n  const extraAttributes = toArray(node.attributes).reduce((acc, attr) => {\r\n    if (acc.name !== 'class' && acc.name !== 'style') {\r\n      acc[attr.name] = attr.value;\r\n    }\r\n    return acc;\r\n  }, {});\r\n  const title = node.getAttribute('title');\r\n  const titleId = node.getAttribute('data-fa-title-id');\r\n  if (config.autoA11y) {\r\n    if (title) {\r\n      extraAttributes['aria-labelledby'] = \"\".concat(config.replacementClass, \"-title-\").concat(titleId || nextUniqueId());\r\n    } else {\r\n      extraAttributes['aria-hidden'] = 'true';\r\n      extraAttributes['focusable'] = 'false';\r\n    }\r\n  }\r\n  return extraAttributes;\r\n}\r\n\r\nfunction blankMeta() {\r\n  return {\r\n    iconName: null,\r\n    title: null,\r\n    titleId: null,\r\n    prefix: null,\r\n    transform: meaninglessTransform,\r\n    symbol: false,\r\n    mask: {\r\n      iconName: null,\r\n      prefix: null,\r\n      rest: []\r\n    },\r\n    maskId: null,\r\n    extra: {\r\n      classes: [],\r\n      styles: {},\r\n      attributes: {}\r\n    }\r\n  };\r\n}\r\nfunction parseMeta(node) {\r\n  let parser = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\r\n    styleParser: true\r\n  };\r\n  const {\r\n    iconName,\r\n    prefix,\r\n    rest: extraClasses\r\n  } = classParser(node);\r\n  const extraAttributes = attributesParser(node);\r\n  const pluginMeta = chainHooks('parseNodeAttributes', {}, node);\r\n  let extraStyles = parser.styleParser ? styleParser(node) : [];\r\n  return _objectSpread2({\r\n    iconName,\r\n    title: node.getAttribute('title'),\r\n    titleId: node.getAttribute('data-fa-title-id'),\r\n    prefix,\r\n    transform: meaninglessTransform,\r\n    mask: {\r\n      iconName: null,\r\n      prefix: null,\r\n      rest: []\r\n    },\r\n    maskId: null,\r\n    symbol: false,\r\n    extra: {\r\n      classes: extraClasses,\r\n      styles: extraStyles,\r\n      attributes: extraAttributes\r\n    }\r\n  }, pluginMeta);\r\n}\r\n\r\nconst {\r\n  styles: styles$2\r\n} = namespace;\r\nfunction generateMutation(node) {\r\n  const nodeMeta = config.autoReplaceSvg === 'nest' ? parseMeta(node, {\r\n    styleParser: false\r\n  }) : parseMeta(node);\r\n  if (~nodeMeta.extra.classes.indexOf(LAYERS_TEXT_CLASSNAME)) {\r\n    return callProvided('generateLayersText', node, nodeMeta);\r\n  } else {\r\n    return callProvided('generateSvgReplacementMutation', node, nodeMeta);\r\n  }\r\n}\r\nfunction getKnownPrefixes() {\r\n  return [...Ft, ...Ia];\r\n}\r\nfunction onTree(root) {\r\n  let callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\r\n  if (!IS_DOM) return Promise.resolve();\r\n  const htmlClassList = DOCUMENT.documentElement.classList;\r\n  const hclAdd = suffix => htmlClassList.add(\"\".concat(HTML_CLASS_I2SVG_BASE_CLASS, \"-\").concat(suffix));\r\n  const hclRemove = suffix => htmlClassList.remove(\"\".concat(HTML_CLASS_I2SVG_BASE_CLASS, \"-\").concat(suffix));\r\n  const prefixes = config.autoFetchSvg ? getKnownPrefixes() : P.concat(Object.keys(styles$2));\r\n  if (!prefixes.includes('fa')) {\r\n    prefixes.push('fa');\r\n  }\r\n  const prefixesDomQuery = [\".\".concat(LAYERS_TEXT_CLASSNAME, \":not([\").concat(DATA_FA_I2SVG, \"])\")].concat(prefixes.map(p$$1 => \".\".concat(p$$1, \":not([\").concat(DATA_FA_I2SVG, \"])\"))).join(', ');\r\n  if (prefixesDomQuery.length === 0) {\r\n    return Promise.resolve();\r\n  }\r\n  let candidates = [];\r\n  try {\r\n    candidates = toArray(root.querySelectorAll(prefixesDomQuery));\r\n  } catch (e$$1) {\r\n    // noop\r\n  }\r\n  if (candidates.length > 0) {\r\n    hclAdd('pending');\r\n    hclRemove('complete');\r\n  } else {\r\n    return Promise.resolve();\r\n  }\r\n  const mark = perf.begin('onTree');\r\n  const mutations = candidates.reduce((acc, node) => {\r\n    try {\r\n      const mutation = generateMutation(node);\r\n      if (mutation) {\r\n        acc.push(mutation);\r\n      }\r\n    } catch (e$$1) {\r\n      if (!PRODUCTION) {\r\n        if (e$$1.name === 'MissingIcon') {\r\n          console.error(e$$1);\r\n        }\r\n      }\r\n    }\r\n    return acc;\r\n  }, []);\r\n  return new Promise((resolve, reject) => {\r\n    Promise.all(mutations).then(resolvedMutations => {\r\n      perform(resolvedMutations, () => {\r\n        hclAdd('active');\r\n        hclAdd('complete');\r\n        hclRemove('pending');\r\n        if (typeof callback === 'function') callback();\r\n        mark();\r\n        resolve();\r\n      });\r\n    }).catch(e$$1 => {\r\n      mark();\r\n      reject(e$$1);\r\n    });\r\n  });\r\n}\r\nfunction onNode(node) {\r\n  let callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\r\n  generateMutation(node).then(mutation => {\r\n    if (mutation) {\r\n      perform([mutation], callback);\r\n    }\r\n  });\r\n}\r\nfunction resolveIcons(next) {\r\n  return function (maybeIconDefinition) {\r\n    let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\r\n    const iconDefinition = (maybeIconDefinition || {}).icon ? maybeIconDefinition : findIconDefinition(maybeIconDefinition || {});\r\n    let {\r\n      mask\r\n    } = params;\r\n    if (mask) {\r\n      mask = (mask || {}).icon ? mask : findIconDefinition(mask || {});\r\n    }\r\n    return next(iconDefinition, _objectSpread2(_objectSpread2({}, params), {}, {\r\n      mask\r\n    }));\r\n  };\r\n}\r\nconst render = function (iconDefinition) {\r\n  let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\r\n  const {\r\n    transform = meaninglessTransform,\r\n    symbol = false,\r\n    mask = null,\r\n    maskId = null,\r\n    title = null,\r\n    titleId = null,\r\n    classes = [],\r\n    attributes = {},\r\n    styles = {}\r\n  } = params;\r\n  if (!iconDefinition) return;\r\n  const {\r\n    prefix,\r\n    iconName,\r\n    icon\r\n  } = iconDefinition;\r\n  return domVariants(_objectSpread2({\r\n    type: 'icon'\r\n  }, iconDefinition), () => {\r\n    callHooks('beforeDOMElementCreation', {\r\n      iconDefinition,\r\n      params\r\n    });\r\n    if (config.autoA11y) {\r\n      if (title) {\r\n        attributes['aria-labelledby'] = \"\".concat(config.replacementClass, \"-title-\").concat(titleId || nextUniqueId());\r\n      } else {\r\n        attributes['aria-hidden'] = 'true';\r\n        attributes['focusable'] = 'false';\r\n      }\r\n    }\r\n    return makeInlineSvgAbstract({\r\n      icons: {\r\n        main: asFoundIcon(icon),\r\n        mask: mask ? asFoundIcon(mask.icon) : {\r\n          found: false,\r\n          width: null,\r\n          height: null,\r\n          icon: {}\r\n        }\r\n      },\r\n      prefix,\r\n      iconName,\r\n      transform: _objectSpread2(_objectSpread2({}, meaninglessTransform), transform),\r\n      symbol,\r\n      title,\r\n      maskId,\r\n      titleId,\r\n      extra: {\r\n        attributes,\r\n        styles,\r\n        classes\r\n      }\r\n    });\r\n  });\r\n};\r\nvar ReplaceElements = {\r\n  mixout() {\r\n    return {\r\n      icon: resolveIcons(render)\r\n    };\r\n  },\r\n  hooks() {\r\n    return {\r\n      mutationObserverCallbacks(accumulator) {\r\n        accumulator.treeCallback = onTree;\r\n        accumulator.nodeCallback = onNode;\r\n        return accumulator;\r\n      }\r\n    };\r\n  },\r\n  provides(providers$$1) {\r\n    providers$$1.i2svg = function (params) {\r\n      const {\r\n        node = DOCUMENT,\r\n        callback = () => {}\r\n      } = params;\r\n      return onTree(node, callback);\r\n    };\r\n    providers$$1.generateSvgReplacementMutation = function (node, nodeMeta) {\r\n      const {\r\n        iconName,\r\n        title,\r\n        titleId,\r\n        prefix,\r\n        transform,\r\n        symbol,\r\n        mask,\r\n        maskId,\r\n        extra\r\n      } = nodeMeta;\r\n      return new Promise((resolve, reject) => {\r\n        Promise.all([findIcon(iconName, prefix), mask.iconName ? findIcon(mask.iconName, mask.prefix) : Promise.resolve({\r\n          found: false,\r\n          width: 512,\r\n          height: 512,\r\n          icon: {}\r\n        })]).then(_ref => {\r\n          let [main, mask] = _ref;\r\n          resolve([node, makeInlineSvgAbstract({\r\n            icons: {\r\n              main,\r\n              mask\r\n            },\r\n            prefix,\r\n            iconName,\r\n            transform,\r\n            symbol,\r\n            maskId,\r\n            title,\r\n            titleId,\r\n            extra,\r\n            watchable: true\r\n          })]);\r\n        }).catch(reject);\r\n      });\r\n    };\r\n    providers$$1.generateAbstractIcon = function (_ref2) {\r\n      let {\r\n        children,\r\n        attributes,\r\n        main,\r\n        transform,\r\n        styles\r\n      } = _ref2;\r\n      const styleString = joinStyles(styles);\r\n      if (styleString.length > 0) {\r\n        attributes['style'] = styleString;\r\n      }\r\n      let nextChild;\r\n      if (transformIsMeaningful(transform)) {\r\n        nextChild = callProvided('generateAbstractTransformGrouping', {\r\n          main,\r\n          transform,\r\n          containerWidth: main.width,\r\n          iconWidth: main.width\r\n        });\r\n      }\r\n      children.push(nextChild || main.icon);\r\n      return {\r\n        children,\r\n        attributes\r\n      };\r\n    };\r\n  }\r\n};\r\n\r\nvar Layers = {\r\n  mixout() {\r\n    return {\r\n      layer(assembler) {\r\n        let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\r\n        const {\r\n          classes = []\r\n        } = params;\r\n        return domVariants({\r\n          type: 'layer'\r\n        }, () => {\r\n          callHooks('beforeDOMElementCreation', {\r\n            assembler,\r\n            params\r\n          });\r\n          let children = [];\r\n          assembler(args => {\r\n            Array.isArray(args) ? args.map(a => {\r\n              children = children.concat(a.abstract);\r\n            }) : children = children.concat(args.abstract);\r\n          });\r\n          return [{\r\n            tag: 'span',\r\n            attributes: {\r\n              class: [\"\".concat(config.cssPrefix, \"-layers\"), ...classes].join(' ')\r\n            },\r\n            children\r\n          }];\r\n        });\r\n      }\r\n    };\r\n  }\r\n};\r\n\r\nvar LayersCounter = {\r\n  mixout() {\r\n    return {\r\n      counter(content) {\r\n        let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\r\n        const {\r\n          title = null,\r\n          classes = [],\r\n          attributes = {},\r\n          styles = {}\r\n        } = params;\r\n        return domVariants({\r\n          type: 'counter',\r\n          content\r\n        }, () => {\r\n          callHooks('beforeDOMElementCreation', {\r\n            content,\r\n            params\r\n          });\r\n          return makeLayersCounterAbstract({\r\n            content: content.toString(),\r\n            title,\r\n            extra: {\r\n              attributes,\r\n              styles,\r\n              classes: [\"\".concat(config.cssPrefix, \"-layers-counter\"), ...classes]\r\n            }\r\n          });\r\n        });\r\n      }\r\n    };\r\n  }\r\n};\r\n\r\nvar LayersText = {\r\n  mixout() {\r\n    return {\r\n      text(content) {\r\n        let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\r\n        const {\r\n          transform = meaninglessTransform,\r\n          title = null,\r\n          classes = [],\r\n          attributes = {},\r\n          styles = {}\r\n        } = params;\r\n        return domVariants({\r\n          type: 'text',\r\n          content\r\n        }, () => {\r\n          callHooks('beforeDOMElementCreation', {\r\n            content,\r\n            params\r\n          });\r\n          return makeLayersTextAbstract({\r\n            content,\r\n            transform: _objectSpread2(_objectSpread2({}, meaninglessTransform), transform),\r\n            title,\r\n            extra: {\r\n              attributes,\r\n              styles,\r\n              classes: [\"\".concat(config.cssPrefix, \"-layers-text\"), ...classes]\r\n            }\r\n          });\r\n        });\r\n      }\r\n    };\r\n  },\r\n  provides(providers$$1) {\r\n    providers$$1.generateLayersText = function (node, nodeMeta) {\r\n      const {\r\n        title,\r\n        transform,\r\n        extra\r\n      } = nodeMeta;\r\n      let width = null;\r\n      let height = null;\r\n      if (IS_IE) {\r\n        const computedFontSize = parseInt(getComputedStyle(node).fontSize, 10);\r\n        const boundingClientRect = node.getBoundingClientRect();\r\n        width = boundingClientRect.width / computedFontSize;\r\n        height = boundingClientRect.height / computedFontSize;\r\n      }\r\n      if (config.autoA11y && !title) {\r\n        extra.attributes['aria-hidden'] = 'true';\r\n      }\r\n      return Promise.resolve([node, makeLayersTextAbstract({\r\n        content: node.innerHTML,\r\n        width,\r\n        height,\r\n        transform,\r\n        title,\r\n        extra,\r\n        watchable: true\r\n      })]);\r\n    };\r\n  }\r\n};\r\n\r\nconst CLEAN_CONTENT_PATTERN = new RegExp('\\u{22}', 'ug');\r\nconst SECONDARY_UNICODE_RANGE = [1105920, 1112319];\r\nconst _FONT_FAMILY_WEIGHT_TO_PREFIX = _objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({}, {\r\n  FontAwesome: {\r\n    normal: 'fas',\r\n    400: 'fas'\r\n  }\r\n}), lt), wa), Yt);\r\nconst FONT_FAMILY_WEIGHT_TO_PREFIX = Object.keys(_FONT_FAMILY_WEIGHT_TO_PREFIX).reduce((acc, key) => {\r\n  acc[key.toLowerCase()] = _FONT_FAMILY_WEIGHT_TO_PREFIX[key];\r\n  return acc;\r\n}, {});\r\nconst FONT_FAMILY_WEIGHT_FALLBACK = Object.keys(FONT_FAMILY_WEIGHT_TO_PREFIX).reduce((acc, fontFamily) => {\r\n  const weights = FONT_FAMILY_WEIGHT_TO_PREFIX[fontFamily];\r\n  acc[fontFamily] = weights[900] || [...Object.entries(weights)][0][1];\r\n  return acc;\r\n}, {});\r\nfunction hexValueFromContent(content) {\r\n  const cleaned = content.replace(CLEAN_CONTENT_PATTERN, '');\r\n  const codePoint = codePointAt(cleaned, 0);\r\n  const isPrependTen = codePoint >= SECONDARY_UNICODE_RANGE[0] && codePoint <= SECONDARY_UNICODE_RANGE[1];\r\n  const isDoubled = cleaned.length === 2 ? cleaned[0] === cleaned[1] : false;\r\n  return {\r\n    value: isDoubled ? toHex(cleaned[0]) : toHex(cleaned),\r\n    isSecondary: isPrependTen || isDoubled\r\n  };\r\n}\r\nfunction getPrefix(fontFamily, fontWeight) {\r\n  const fontFamilySanitized = fontFamily.replace(/^['\"]|['\"]$/g, '').toLowerCase();\r\n  const fontWeightInteger = parseInt(fontWeight);\r\n  const fontWeightSanitized = isNaN(fontWeightInteger) ? 'normal' : fontWeightInteger;\r\n  return (FONT_FAMILY_WEIGHT_TO_PREFIX[fontFamilySanitized] || {})[fontWeightSanitized] || FONT_FAMILY_WEIGHT_FALLBACK[fontFamilySanitized];\r\n}\r\nfunction replaceForPosition(node, position) {\r\n  const pendingAttribute = \"\".concat(DATA_FA_PSEUDO_ELEMENT_PENDING).concat(position.replace(':', '-'));\r\n  return new Promise((resolve, reject) => {\r\n    if (node.getAttribute(pendingAttribute) !== null) {\r\n      // This node is already being processed\r\n      return resolve();\r\n    }\r\n    const children = toArray(node.children);\r\n    const alreadyProcessedPseudoElement = children.filter(c$$1 => c$$1.getAttribute(DATA_FA_PSEUDO_ELEMENT) === position)[0];\r\n    const styles = WINDOW.getComputedStyle(node, position);\r\n    const fontFamily = styles.getPropertyValue('font-family');\r\n    const fontFamilyMatch = fontFamily.match(FONT_FAMILY_PATTERN);\r\n    const fontWeight = styles.getPropertyValue('font-weight');\r\n    const content = styles.getPropertyValue('content');\r\n    if (alreadyProcessedPseudoElement && !fontFamilyMatch) {\r\n      // If we've already processed it but the current computed style does not result in a font-family,\r\n      // that probably means that a class name that was previously present to make the icon has been\r\n      // removed. So we now should delete the icon.\r\n      node.removeChild(alreadyProcessedPseudoElement);\r\n      return resolve();\r\n    } else if (fontFamilyMatch && content !== 'none' && content !== '') {\r\n      const content = styles.getPropertyValue('content');\r\n      let prefix = getPrefix(fontFamily, fontWeight);\r\n      const {\r\n        value: hexValue,\r\n        isSecondary\r\n      } = hexValueFromContent(content);\r\n      const isV4 = fontFamilyMatch[0].startsWith('FontAwesome');\r\n      let iconName = byUnicode(prefix, hexValue);\r\n      let iconIdentifier = iconName;\r\n      if (isV4) {\r\n        const iconName4 = byOldUnicode(hexValue);\r\n        if (iconName4.iconName && iconName4.prefix) {\r\n          iconName = iconName4.iconName;\r\n          prefix = iconName4.prefix;\r\n        }\r\n      }\r\n\r\n      // Only convert the pseudo element in this ::before/::after position into an icon if we haven't\r\n      // already done so with the same prefix and iconName\r\n      if (iconName && !isSecondary && (!alreadyProcessedPseudoElement || alreadyProcessedPseudoElement.getAttribute(DATA_PREFIX) !== prefix || alreadyProcessedPseudoElement.getAttribute(DATA_ICON) !== iconIdentifier)) {\r\n        node.setAttribute(pendingAttribute, iconIdentifier);\r\n        if (alreadyProcessedPseudoElement) {\r\n          // Delete the old one, since we're replacing it with a new one\r\n          node.removeChild(alreadyProcessedPseudoElement);\r\n        }\r\n        const meta = blankMeta();\r\n        const {\r\n          extra\r\n        } = meta;\r\n        extra.attributes[DATA_FA_PSEUDO_ELEMENT] = position;\r\n        findIcon(iconName, prefix).then(main => {\r\n          const abstract = makeInlineSvgAbstract(_objectSpread2(_objectSpread2({}, meta), {}, {\r\n            icons: {\r\n              main,\r\n              mask: emptyCanonicalIcon()\r\n            },\r\n            prefix,\r\n            iconName: iconIdentifier,\r\n            extra,\r\n            watchable: true\r\n          }));\r\n          const element = DOCUMENT.createElementNS('http://www.w3.org/2000/svg', 'svg');\r\n          if (position === '::before') {\r\n            node.insertBefore(element, node.firstChild);\r\n          } else {\r\n            node.appendChild(element);\r\n          }\r\n          element.outerHTML = abstract.map(a$$1 => toHtml(a$$1)).join('\\n');\r\n          node.removeAttribute(pendingAttribute);\r\n          resolve();\r\n        }).catch(reject);\r\n      } else {\r\n        resolve();\r\n      }\r\n    } else {\r\n      resolve();\r\n    }\r\n  });\r\n}\r\nfunction replace(node) {\r\n  return Promise.all([replaceForPosition(node, '::before'), replaceForPosition(node, '::after')]);\r\n}\r\nfunction processable(node) {\r\n  return node.parentNode !== document.head && !~TAGNAMES_TO_SKIP_FOR_PSEUDOELEMENTS.indexOf(node.tagName.toUpperCase()) && !node.getAttribute(DATA_FA_PSEUDO_ELEMENT) && (!node.parentNode || node.parentNode.tagName !== 'svg');\r\n}\r\nfunction searchPseudoElements(root) {\r\n  if (!IS_DOM) return;\r\n  return new Promise((resolve, reject) => {\r\n    const operations = toArray(root.querySelectorAll('*')).filter(processable).map(replace);\r\n    const end = perf.begin('searchPseudoElements');\r\n    disableObservation();\r\n    Promise.all(operations).then(() => {\r\n      end();\r\n      enableObservation();\r\n      resolve();\r\n    }).catch(() => {\r\n      end();\r\n      enableObservation();\r\n      reject();\r\n    });\r\n  });\r\n}\r\nvar PseudoElements = {\r\n  hooks() {\r\n    return {\r\n      mutationObserverCallbacks(accumulator) {\r\n        accumulator.pseudoElementsCallback = searchPseudoElements;\r\n        return accumulator;\r\n      }\r\n    };\r\n  },\r\n  provides(providers) {\r\n    providers.pseudoElements2svg = function (params) {\r\n      const {\r\n        node = DOCUMENT\r\n      } = params;\r\n      if (config.searchPseudoElements) {\r\n        searchPseudoElements(node);\r\n      }\r\n    };\r\n  }\r\n};\r\n\r\nlet _unwatched = false;\r\nvar MutationObserver$1 = {\r\n  mixout() {\r\n    return {\r\n      dom: {\r\n        unwatch() {\r\n          disableObservation();\r\n          _unwatched = true;\r\n        }\r\n      }\r\n    };\r\n  },\r\n  hooks() {\r\n    return {\r\n      bootstrap() {\r\n        observe(chainHooks('mutationObserverCallbacks', {}));\r\n      },\r\n      noAuto() {\r\n        disconnect();\r\n      },\r\n      watch(params) {\r\n        const {\r\n          observeMutationsRoot\r\n        } = params;\r\n        if (_unwatched) {\r\n          enableObservation();\r\n        } else {\r\n          observe(chainHooks('mutationObserverCallbacks', {\r\n            observeMutationsRoot\r\n          }));\r\n        }\r\n      }\r\n    };\r\n  }\r\n};\r\n\r\nconst parseTransformString = transformString => {\r\n  let transform = {\r\n    size: 16,\r\n    x: 0,\r\n    y: 0,\r\n    flipX: false,\r\n    flipY: false,\r\n    rotate: 0\r\n  };\r\n  return transformString.toLowerCase().split(' ').reduce((acc, n) => {\r\n    const parts = n.toLowerCase().split('-');\r\n    const first = parts[0];\r\n    let rest = parts.slice(1).join('-');\r\n    if (first && rest === 'h') {\r\n      acc.flipX = true;\r\n      return acc;\r\n    }\r\n    if (first && rest === 'v') {\r\n      acc.flipY = true;\r\n      return acc;\r\n    }\r\n    rest = parseFloat(rest);\r\n    if (isNaN(rest)) {\r\n      return acc;\r\n    }\r\n    switch (first) {\r\n      case 'grow':\r\n        acc.size = acc.size + rest;\r\n        break;\r\n      case 'shrink':\r\n        acc.size = acc.size - rest;\r\n        break;\r\n      case 'left':\r\n        acc.x = acc.x - rest;\r\n        break;\r\n      case 'right':\r\n        acc.x = acc.x + rest;\r\n        break;\r\n      case 'up':\r\n        acc.y = acc.y - rest;\r\n        break;\r\n      case 'down':\r\n        acc.y = acc.y + rest;\r\n        break;\r\n      case 'rotate':\r\n        acc.rotate = acc.rotate + rest;\r\n        break;\r\n    }\r\n    return acc;\r\n  }, transform);\r\n};\r\nvar PowerTransforms = {\r\n  mixout() {\r\n    return {\r\n      parse: {\r\n        transform: transformString => {\r\n          return parseTransformString(transformString);\r\n        }\r\n      }\r\n    };\r\n  },\r\n  hooks() {\r\n    return {\r\n      parseNodeAttributes(accumulator, node) {\r\n        const transformString = node.getAttribute('data-fa-transform');\r\n        if (transformString) {\r\n          accumulator.transform = parseTransformString(transformString);\r\n        }\r\n        return accumulator;\r\n      }\r\n    };\r\n  },\r\n  provides(providers) {\r\n    providers.generateAbstractTransformGrouping = function (_ref) {\r\n      let {\r\n        main,\r\n        transform,\r\n        containerWidth,\r\n        iconWidth\r\n      } = _ref;\r\n      const outer = {\r\n        transform: \"translate(\".concat(containerWidth / 2, \" 256)\")\r\n      };\r\n      const innerTranslate = \"translate(\".concat(transform.x * 32, \", \").concat(transform.y * 32, \") \");\r\n      const innerScale = \"scale(\".concat(transform.size / 16 * (transform.flipX ? -1 : 1), \", \").concat(transform.size / 16 * (transform.flipY ? -1 : 1), \") \");\r\n      const innerRotate = \"rotate(\".concat(transform.rotate, \" 0 0)\");\r\n      const inner = {\r\n        transform: \"\".concat(innerTranslate, \" \").concat(innerScale, \" \").concat(innerRotate)\r\n      };\r\n      const path = {\r\n        transform: \"translate(\".concat(iconWidth / 2 * -1, \" -256)\")\r\n      };\r\n      const operations = {\r\n        outer,\r\n        inner,\r\n        path\r\n      };\r\n      return {\r\n        tag: 'g',\r\n        attributes: _objectSpread2({}, operations.outer),\r\n        children: [{\r\n          tag: 'g',\r\n          attributes: _objectSpread2({}, operations.inner),\r\n          children: [{\r\n            tag: main.icon.tag,\r\n            children: main.icon.children,\r\n            attributes: _objectSpread2(_objectSpread2({}, main.icon.attributes), operations.path)\r\n          }]\r\n        }]\r\n      };\r\n    };\r\n  }\r\n};\r\n\r\nconst ALL_SPACE = {\r\n  x: 0,\r\n  y: 0,\r\n  width: '100%',\r\n  height: '100%'\r\n};\r\nfunction fillBlack(abstract) {\r\n  let force = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\r\n  if (abstract.attributes && (abstract.attributes.fill || force)) {\r\n    abstract.attributes.fill = 'black';\r\n  }\r\n  return abstract;\r\n}\r\nfunction deGroup(abstract) {\r\n  if (abstract.tag === 'g') {\r\n    return abstract.children;\r\n  } else {\r\n    return [abstract];\r\n  }\r\n}\r\nvar Masks = {\r\n  hooks() {\r\n    return {\r\n      parseNodeAttributes(accumulator, node) {\r\n        const maskData = node.getAttribute('data-fa-mask');\r\n        const mask = !maskData ? emptyCanonicalIcon() : getCanonicalIcon(maskData.split(' ').map(i => i.trim()));\r\n        if (!mask.prefix) {\r\n          mask.prefix = getDefaultUsablePrefix();\r\n        }\r\n        accumulator.mask = mask;\r\n        accumulator.maskId = node.getAttribute('data-fa-mask-id');\r\n        return accumulator;\r\n      }\r\n    };\r\n  },\r\n  provides(providers) {\r\n    providers.generateAbstractMask = function (_ref) {\r\n      let {\r\n        children,\r\n        attributes,\r\n        main,\r\n        mask,\r\n        maskId: explicitMaskId,\r\n        transform\r\n      } = _ref;\r\n      const {\r\n        width: mainWidth,\r\n        icon: mainPath\r\n      } = main;\r\n      const {\r\n        width: maskWidth,\r\n        icon: maskPath\r\n      } = mask;\r\n      const trans = transformForSvg({\r\n        transform,\r\n        containerWidth: maskWidth,\r\n        iconWidth: mainWidth\r\n      });\r\n      const maskRect = {\r\n        tag: 'rect',\r\n        attributes: _objectSpread2(_objectSpread2({}, ALL_SPACE), {}, {\r\n          fill: 'white'\r\n        })\r\n      };\r\n      const maskInnerGroupChildrenMixin = mainPath.children ? {\r\n        children: mainPath.children.map(fillBlack)\r\n      } : {};\r\n      const maskInnerGroup = {\r\n        tag: 'g',\r\n        attributes: _objectSpread2({}, trans.inner),\r\n        children: [fillBlack(_objectSpread2({\r\n          tag: mainPath.tag,\r\n          attributes: _objectSpread2(_objectSpread2({}, mainPath.attributes), trans.path)\r\n        }, maskInnerGroupChildrenMixin))]\r\n      };\r\n      const maskOuterGroup = {\r\n        tag: 'g',\r\n        attributes: _objectSpread2({}, trans.outer),\r\n        children: [maskInnerGroup]\r\n      };\r\n      const maskId = \"mask-\".concat(explicitMaskId || nextUniqueId());\r\n      const clipId = \"clip-\".concat(explicitMaskId || nextUniqueId());\r\n      const maskTag = {\r\n        tag: 'mask',\r\n        attributes: _objectSpread2(_objectSpread2({}, ALL_SPACE), {}, {\r\n          id: maskId,\r\n          maskUnits: 'userSpaceOnUse',\r\n          maskContentUnits: 'userSpaceOnUse'\r\n        }),\r\n        children: [maskRect, maskOuterGroup]\r\n      };\r\n      const defs = {\r\n        tag: 'defs',\r\n        children: [{\r\n          tag: 'clipPath',\r\n          attributes: {\r\n            id: clipId\r\n          },\r\n          children: deGroup(maskPath)\r\n        }, maskTag]\r\n      };\r\n      children.push(defs, {\r\n        tag: 'rect',\r\n        attributes: _objectSpread2({\r\n          fill: 'currentColor',\r\n          'clip-path': \"url(#\".concat(clipId, \")\"),\r\n          mask: \"url(#\".concat(maskId, \")\")\r\n        }, ALL_SPACE)\r\n      });\r\n      return {\r\n        children,\r\n        attributes\r\n      };\r\n    };\r\n  }\r\n};\r\n\r\nvar MissingIconIndicator = {\r\n  provides(providers) {\r\n    let reduceMotion = false;\r\n    if (WINDOW.matchMedia) {\r\n      reduceMotion = WINDOW.matchMedia('(prefers-reduced-motion: reduce)').matches;\r\n    }\r\n    providers.missingIconAbstract = function () {\r\n      const gChildren = [];\r\n      const FILL = {\r\n        fill: 'currentColor'\r\n      };\r\n      const ANIMATION_BASE = {\r\n        attributeType: 'XML',\r\n        repeatCount: 'indefinite',\r\n        dur: '2s'\r\n      };\r\n\r\n      // Ring\r\n      gChildren.push({\r\n        tag: 'path',\r\n        attributes: _objectSpread2(_objectSpread2({}, FILL), {}, {\r\n          d: 'M156.5,447.7l-12.6,29.5c-18.7-9.5-35.9-21.2-51.5-34.9l22.7-22.7C127.6,430.5,141.5,440,156.5,447.7z M40.6,272H8.5 c1.4,21.2,5.4,41.7,11.7,61.1L50,321.2C45.1,305.5,41.8,289,40.6,272z M40.6,240c1.4-18.8,5.2-37,11.1-54.1l-29.5-12.6 C14.7,194.3,10,216.7,8.5,240H40.6z M64.3,156.5c7.8-14.9,17.2-28.8,28.1-41.5L69.7,92.3c-13.7,15.6-25.5,32.8-34.9,51.5 L64.3,156.5z M397,419.6c-13.9,12-29.4,22.3-46.1,30.4l11.9,29.8c20.7-9.9,39.8-22.6,56.9-37.6L397,419.6z M115,92.4 c13.9-12,29.4-22.3,46.1-30.4l-11.9-29.8c-20.7,9.9-39.8,22.6-56.8,37.6L115,92.4z M447.7,355.5c-7.8,14.9-17.2,28.8-28.1,41.5 l22.7,22.7c13.7-15.6,25.5-32.9,34.9-51.5L447.7,355.5z M471.4,272c-1.4,18.8-5.2,37-11.1,54.1l29.5,12.6 c7.5-21.1,12.2-43.5,13.6-66.8H471.4z M321.2,462c-15.7,5-32.2,8.2-49.2,9.4v32.1c21.2-1.4,41.7-5.4,61.1-11.7L321.2,462z M240,471.4c-18.8-1.4-37-5.2-54.1-11.1l-12.6,29.5c21.1,7.5,43.5,12.2,66.8,13.6V471.4z M462,190.8c5,15.7,8.2,32.2,9.4,49.2h32.1 c-1.4-21.2-5.4-41.7-11.7-61.1L462,190.8z M92.4,397c-12-13.9-22.3-29.4-30.4-46.1l-29.8,11.9c9.9,20.7,22.6,39.8,37.6,56.9 L92.4,397z M272,40.6c18.8,1.4,36.9,5.2,54.1,11.1l12.6-29.5C317.7,14.7,295.3,10,272,8.5V40.6z M190.8,50 c15.7-5,32.2-8.2,49.2-9.4V8.5c-21.2,1.4-41.7,5.4-61.1,11.7L190.8,50z M442.3,92.3L419.6,115c12,13.9,22.3,29.4,30.5,46.1 l29.8-11.9C470,128.5,457.3,109.4,442.3,92.3z M397,92.4l22.7-22.7c-15.6-13.7-32.8-25.5-51.5-34.9l-12.6,29.5 C370.4,72.1,384.4,81.5,397,92.4z'\r\n        })\r\n      });\r\n      const OPACITY_ANIMATE = _objectSpread2(_objectSpread2({}, ANIMATION_BASE), {}, {\r\n        attributeName: 'opacity'\r\n      });\r\n      const dot = {\r\n        tag: 'circle',\r\n        attributes: _objectSpread2(_objectSpread2({}, FILL), {}, {\r\n          cx: '256',\r\n          cy: '364',\r\n          r: '28'\r\n        }),\r\n        children: []\r\n      };\r\n      if (!reduceMotion) {\r\n        dot.children.push({\r\n          tag: 'animate',\r\n          attributes: _objectSpread2(_objectSpread2({}, ANIMATION_BASE), {}, {\r\n            attributeName: 'r',\r\n            values: '28;14;28;28;14;28;'\r\n          })\r\n        }, {\r\n          tag: 'animate',\r\n          attributes: _objectSpread2(_objectSpread2({}, OPACITY_ANIMATE), {}, {\r\n            values: '1;0;1;1;0;1;'\r\n          })\r\n        });\r\n      }\r\n      gChildren.push(dot);\r\n      gChildren.push({\r\n        tag: 'path',\r\n        attributes: _objectSpread2(_objectSpread2({}, FILL), {}, {\r\n          opacity: '1',\r\n          d: 'M263.7,312h-16c-6.6,0-12-5.4-12-12c0-71,77.4-63.9,77.4-107.8c0-20-17.8-40.2-57.4-40.2c-29.1,0-44.3,9.6-59.2,28.7 c-3.9,5-11.1,6-16.2,2.4l-13.1-9.2c-5.6-3.9-6.9-11.8-2.6-17.2c21.2-27.2,46.4-44.7,91.2-44.7c52.3,0,97.4,29.8,97.4,80.2 c0,67.6-77.4,63.5-77.4,107.8C275.7,306.6,270.3,312,263.7,312z'\r\n        }),\r\n        children: reduceMotion ? [] : [{\r\n          tag: 'animate',\r\n          attributes: _objectSpread2(_objectSpread2({}, OPACITY_ANIMATE), {}, {\r\n            values: '1;0;0;0;0;1;'\r\n          })\r\n        }]\r\n      });\r\n      if (!reduceMotion) {\r\n        // Exclamation\r\n        gChildren.push({\r\n          tag: 'path',\r\n          attributes: _objectSpread2(_objectSpread2({}, FILL), {}, {\r\n            opacity: '0',\r\n            d: 'M232.5,134.5l7,168c0.3,6.4,5.6,11.5,12,11.5h9c6.4,0,11.7-5.1,12-11.5l7-168c0.3-6.8-5.2-12.5-12-12.5h-23 C237.7,122,232.2,127.7,232.5,134.5z'\r\n          }),\r\n          children: [{\r\n            tag: 'animate',\r\n            attributes: _objectSpread2(_objectSpread2({}, OPACITY_ANIMATE), {}, {\r\n              values: '0;0;1;1;0;0;'\r\n            })\r\n          }]\r\n        });\r\n      }\r\n      return {\r\n        tag: 'g',\r\n        attributes: {\r\n          'class': 'missing'\r\n        },\r\n        children: gChildren\r\n      };\r\n    };\r\n  }\r\n};\r\n\r\nvar SvgSymbols = {\r\n  hooks() {\r\n    return {\r\n      parseNodeAttributes(accumulator, node) {\r\n        const symbolData = node.getAttribute('data-fa-symbol');\r\n        const symbol = symbolData === null ? false : symbolData === '' ? true : symbolData;\r\n        accumulator['symbol'] = symbol;\r\n        return accumulator;\r\n      }\r\n    };\r\n  }\r\n};\r\n\r\nvar plugins = [InjectCSS, ReplaceElements, Layers, LayersCounter, LayersText, PseudoElements, MutationObserver$1, PowerTransforms, Masks, MissingIconIndicator, SvgSymbols];\r\n\r\nregisterPlugins(plugins, {\r\n  mixoutsTo: api\r\n});\r\nconst noAuto$1 = api.noAuto;\r\nconst config$1 = api.config;\r\nconst library$1 = api.library;\r\nconst dom$1 = api.dom;\r\nconst parse$1 = api.parse;\r\nconst findIconDefinition$1 = api.findIconDefinition;\r\nconst toHtml$1 = api.toHtml;\r\nconst icon = api.icon;\r\nconst layer = api.layer;\r\nconst text = api.text;\r\nconst counter = api.counter;\r\n\r\nexport { noAuto$1 as noAuto, config$1 as config, library$1 as library, dom$1 as dom, parse$1 as parse, findIconDefinition$1 as findIconDefinition, toHtml$1 as toHtml, icon, layer, text, counter, api };\r\n", "import { parse, icon } from '@fortawesome/fontawesome-svg-core';\r\nimport PropTypes from 'prop-types';\r\nimport React from 'react';\r\n\r\nfunction ownKeys(object, enumerableOnly) {\r\n  var keys = Object.keys(object);\r\n\r\n  if (Object.getOwnPropertySymbols) {\r\n    var symbols = Object.getOwnPropertySymbols(object);\r\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\r\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\r\n    })), keys.push.apply(keys, symbols);\r\n  }\r\n\r\n  return keys;\r\n}\r\n\r\nfunction _objectSpread2(target) {\r\n  for (var i = 1; i < arguments.length; i++) {\r\n    var source = null != arguments[i] ? arguments[i] : {};\r\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\r\n      _defineProperty(target, key, source[key]);\r\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\r\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\r\n    });\r\n  }\r\n\r\n  return target;\r\n}\r\n\r\nfunction _typeof(obj) {\r\n  \"@babel/helpers - typeof\";\r\n\r\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\r\n    return typeof obj;\r\n  } : function (obj) {\r\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\r\n  }, _typeof(obj);\r\n}\r\n\r\nfunction _defineProperty(obj, key, value) {\r\n  if (key in obj) {\r\n    Object.defineProperty(obj, key, {\r\n      value: value,\r\n      enumerable: true,\r\n      configurable: true,\r\n      writable: true\r\n    });\r\n  } else {\r\n    obj[key] = value;\r\n  }\r\n\r\n  return obj;\r\n}\r\n\r\nfunction _objectWithoutPropertiesLoose(source, excluded) {\r\n  if (source == null) return {};\r\n  var target = {};\r\n  var sourceKeys = Object.keys(source);\r\n  var key, i;\r\n\r\n  for (i = 0; i < sourceKeys.length; i++) {\r\n    key = sourceKeys[i];\r\n    if (excluded.indexOf(key) >= 0) continue;\r\n    target[key] = source[key];\r\n  }\r\n\r\n  return target;\r\n}\r\n\r\nfunction _objectWithoutProperties(source, excluded) {\r\n  if (source == null) return {};\r\n\r\n  var target = _objectWithoutPropertiesLoose(source, excluded);\r\n\r\n  var key, i;\r\n\r\n  if (Object.getOwnPropertySymbols) {\r\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\r\n\r\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\r\n      key = sourceSymbolKeys[i];\r\n      if (excluded.indexOf(key) >= 0) continue;\r\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\r\n      target[key] = source[key];\r\n    }\r\n  }\r\n\r\n  return target;\r\n}\r\n\r\nfunction _toConsumableArray(arr) {\r\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\r\n}\r\n\r\nfunction _arrayWithoutHoles(arr) {\r\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\r\n}\r\n\r\nfunction _iterableToArray(iter) {\r\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\r\n}\r\n\r\nfunction _unsupportedIterableToArray(o, minLen) {\r\n  if (!o) return;\r\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\r\n  var n = Object.prototype.toString.call(o).slice(8, -1);\r\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\r\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\r\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\r\n}\r\n\r\nfunction _arrayLikeToArray(arr, len) {\r\n  if (len == null || len > arr.length) len = arr.length;\r\n\r\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\r\n\r\n  return arr2;\r\n}\r\n\r\nfunction _nonIterableSpread() {\r\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\r\n}\r\n\r\n// Get CSS class list from a props object\r\nfunction classList(props) {\r\n  var _classes;\r\n\r\n  var beat = props.beat,\r\n      fade = props.fade,\r\n      beatFade = props.beatFade,\r\n      bounce = props.bounce,\r\n      shake = props.shake,\r\n      flash = props.flash,\r\n      spin = props.spin,\r\n      spinPulse = props.spinPulse,\r\n      spinReverse = props.spinReverse,\r\n      pulse = props.pulse,\r\n      fixedWidth = props.fixedWidth,\r\n      inverse = props.inverse,\r\n      border = props.border,\r\n      listItem = props.listItem,\r\n      flip = props.flip,\r\n      size = props.size,\r\n      rotation = props.rotation,\r\n      pull = props.pull; // map of CSS class names to properties\r\n\r\n  var classes = (_classes = {\r\n    'fa-beat': beat,\r\n    'fa-fade': fade,\r\n    'fa-beat-fade': beatFade,\r\n    'fa-bounce': bounce,\r\n    'fa-shake': shake,\r\n    'fa-flash': flash,\r\n    'fa-spin': spin,\r\n    'fa-spin-reverse': spinReverse,\r\n    'fa-spin-pulse': spinPulse,\r\n    'fa-pulse': pulse,\r\n    'fa-fw': fixedWidth,\r\n    'fa-inverse': inverse,\r\n    'fa-border': border,\r\n    'fa-li': listItem,\r\n    'fa-flip': flip === true,\r\n    'fa-flip-horizontal': flip === 'horizontal' || flip === 'both',\r\n    'fa-flip-vertical': flip === 'vertical' || flip === 'both'\r\n  }, _defineProperty(_classes, \"fa-\".concat(size), typeof size !== 'undefined' && size !== null), _defineProperty(_classes, \"fa-rotate-\".concat(rotation), typeof rotation !== 'undefined' && rotation !== null && rotation !== 0), _defineProperty(_classes, \"fa-pull-\".concat(pull), typeof pull !== 'undefined' && pull !== null), _defineProperty(_classes, 'fa-swap-opacity', props.swapOpacity), _classes); // map over all the keys in the classes object\r\n  // return an array of the keys where the value for the key is not null\r\n\r\n  return Object.keys(classes).map(function (key) {\r\n    return classes[key] ? key : null;\r\n  }).filter(function (key) {\r\n    return key;\r\n  });\r\n}\r\n\r\n// Camelize taken from humps\r\n// humps is copyright © 2012+ Dom Christie\r\n// Released under the MIT license.\r\n// Performant way to determine if object coerces to a number\r\nfunction _isNumerical(obj) {\r\n  obj = obj - 0; // eslint-disable-next-line no-self-compare\r\n\r\n  return obj === obj;\r\n}\r\n\r\nfunction camelize(string) {\r\n  if (_isNumerical(string)) {\r\n    return string;\r\n  } // eslint-disable-next-line no-useless-escape\r\n\r\n\r\n  string = string.replace(/[\\-_\\s]+(.)?/g, function (match, chr) {\r\n    return chr ? chr.toUpperCase() : '';\r\n  }); // Ensure 1st char is always lowercase\r\n\r\n  return string.substr(0, 1).toLowerCase() + string.substr(1);\r\n}\r\n\r\nvar _excluded = [\"style\"];\r\n\r\nfunction capitalize(val) {\r\n  return val.charAt(0).toUpperCase() + val.slice(1);\r\n}\r\n\r\nfunction styleToObject(style) {\r\n  return style.split(';').map(function (s) {\r\n    return s.trim();\r\n  }).filter(function (s) {\r\n    return s;\r\n  }).reduce(function (acc, pair) {\r\n    var i = pair.indexOf(':');\r\n    var prop = camelize(pair.slice(0, i));\r\n    var value = pair.slice(i + 1).trim();\r\n    prop.startsWith('webkit') ? acc[capitalize(prop)] = value : acc[prop] = value;\r\n    return acc;\r\n  }, {});\r\n}\r\n\r\nfunction convert(createElement, element) {\r\n  var extraProps = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\r\n\r\n  if (typeof element === 'string') {\r\n    return element;\r\n  }\r\n\r\n  var children = (element.children || []).map(function (child) {\r\n    return convert(createElement, child);\r\n  });\r\n  /* eslint-disable dot-notation */\r\n\r\n  var mixins = Object.keys(element.attributes || {}).reduce(function (acc, key) {\r\n    var val = element.attributes[key];\r\n\r\n    switch (key) {\r\n      case 'class':\r\n        acc.attrs['className'] = val;\r\n        delete element.attributes['class'];\r\n        break;\r\n\r\n      case 'style':\r\n        acc.attrs['style'] = styleToObject(val);\r\n        break;\r\n\r\n      default:\r\n        if (key.indexOf('aria-') === 0 || key.indexOf('data-') === 0) {\r\n          acc.attrs[key.toLowerCase()] = val;\r\n        } else {\r\n          acc.attrs[camelize(key)] = val;\r\n        }\r\n\r\n    }\r\n\r\n    return acc;\r\n  }, {\r\n    attrs: {}\r\n  });\r\n\r\n  var _extraProps$style = extraProps.style,\r\n      existingStyle = _extraProps$style === void 0 ? {} : _extraProps$style,\r\n      remaining = _objectWithoutProperties(extraProps, _excluded);\r\n\r\n  mixins.attrs['style'] = _objectSpread2(_objectSpread2({}, mixins.attrs['style']), existingStyle);\r\n  /* eslint-enable */\r\n\r\n  return createElement.apply(void 0, [element.tag, _objectSpread2(_objectSpread2({}, mixins.attrs), remaining)].concat(_toConsumableArray(children)));\r\n}\r\n\r\nvar PRODUCTION = false;\r\n\r\ntry {\r\n  PRODUCTION = process.env.NODE_ENV === 'production';\r\n} catch (e) {}\r\n\r\nfunction log () {\r\n  if (!PRODUCTION && console && typeof console.error === 'function') {\r\n    var _console;\r\n\r\n    (_console = console).error.apply(_console, arguments);\r\n  }\r\n}\r\n\r\nfunction normalizeIconArgs(icon) {\r\n  // this has everything that it needs to be rendered which means it was probably imported\r\n  // directly from an icon svg package\r\n  if (icon && _typeof(icon) === 'object' && icon.prefix && icon.iconName && icon.icon) {\r\n    return icon;\r\n  }\r\n\r\n  if (parse.icon) {\r\n    return parse.icon(icon);\r\n  } // if the icon is null, there's nothing to do\r\n\r\n\r\n  if (icon === null) {\r\n    return null;\r\n  } // if the icon is an object and has a prefix and an icon name, return it\r\n\r\n\r\n  if (icon && _typeof(icon) === 'object' && icon.prefix && icon.iconName) {\r\n    return icon;\r\n  } // if it's an array with length of two\r\n\r\n\r\n  if (Array.isArray(icon) && icon.length === 2) {\r\n    // use the first item as prefix, second as icon name\r\n    return {\r\n      prefix: icon[0],\r\n      iconName: icon[1]\r\n    };\r\n  } // if it's a string, use it as the icon name\r\n\r\n\r\n  if (typeof icon === 'string') {\r\n    return {\r\n      prefix: 'fas',\r\n      iconName: icon\r\n    };\r\n  }\r\n}\r\n\r\n// creates an object with a key of key\r\n// and a value of value\r\n// if certain conditions are met\r\nfunction objectWithKey(key, value) {\r\n  // if the value is a non-empty array\r\n  // or it's not an array but it is truthy\r\n  // then create the object with the key and the value\r\n  // if not, return an empty array\r\n  return Array.isArray(value) && value.length > 0 || !Array.isArray(value) && value ? _defineProperty({}, key, value) : {};\r\n}\r\n\r\nvar defaultProps = {\r\n  border: false,\r\n  className: '',\r\n  mask: null,\r\n  maskId: null,\r\n  fixedWidth: false,\r\n  inverse: false,\r\n  flip: false,\r\n  icon: null,\r\n  listItem: false,\r\n  pull: null,\r\n  pulse: false,\r\n  rotation: null,\r\n  size: null,\r\n  spin: false,\r\n  spinPulse: false,\r\n  spinReverse: false,\r\n  beat: false,\r\n  fade: false,\r\n  beatFade: false,\r\n  bounce: false,\r\n  shake: false,\r\n  symbol: false,\r\n  title: '',\r\n  titleId: null,\r\n  transform: null,\r\n  swapOpacity: false\r\n};\r\nvar FontAwesomeIcon = /*#__PURE__*/React.forwardRef(function (props, ref) {\r\n  var allProps = _objectSpread2(_objectSpread2({}, defaultProps), props);\r\n\r\n  var iconArgs = allProps.icon,\r\n      maskArgs = allProps.mask,\r\n      symbol = allProps.symbol,\r\n      className = allProps.className,\r\n      title = allProps.title,\r\n      titleId = allProps.titleId,\r\n      maskId = allProps.maskId;\r\n  var iconLookup = normalizeIconArgs(iconArgs);\r\n  var classes = objectWithKey('classes', [].concat(_toConsumableArray(classList(allProps)), _toConsumableArray((className || '').split(' '))));\r\n  var transform = objectWithKey('transform', typeof allProps.transform === 'string' ? parse.transform(allProps.transform) : allProps.transform);\r\n  var mask = objectWithKey('mask', normalizeIconArgs(maskArgs));\r\n  var renderedIcon = icon(iconLookup, _objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({}, classes), transform), mask), {}, {\r\n    symbol: symbol,\r\n    title: title,\r\n    titleId: titleId,\r\n    maskId: maskId\r\n  }));\r\n\r\n  if (!renderedIcon) {\r\n    log('Could not find icon', iconLookup);\r\n    return null;\r\n  }\r\n\r\n  var abstract = renderedIcon.abstract;\r\n  var extraProps = {\r\n    ref: ref\r\n  };\r\n  Object.keys(allProps).forEach(function (key) {\r\n    // eslint-disable-next-line no-prototype-builtins\r\n    if (!defaultProps.hasOwnProperty(key)) {\r\n      extraProps[key] = allProps[key];\r\n    }\r\n  });\r\n  return convertCurry(abstract[0], extraProps);\r\n});\r\nFontAwesomeIcon.displayName = 'FontAwesomeIcon';\r\nFontAwesomeIcon.propTypes = {\r\n  beat: PropTypes.bool,\r\n  border: PropTypes.bool,\r\n  beatFade: PropTypes.bool,\r\n  bounce: PropTypes.bool,\r\n  className: PropTypes.string,\r\n  fade: PropTypes.bool,\r\n  flash: PropTypes.bool,\r\n  mask: PropTypes.oneOfType([PropTypes.object, PropTypes.array, PropTypes.string]),\r\n  maskId: PropTypes.string,\r\n  fixedWidth: PropTypes.bool,\r\n  inverse: PropTypes.bool,\r\n  flip: PropTypes.oneOf([true, false, 'horizontal', 'vertical', 'both']),\r\n  icon: PropTypes.oneOfType([PropTypes.object, PropTypes.array, PropTypes.string]),\r\n  listItem: PropTypes.bool,\r\n  pull: PropTypes.oneOf(['right', 'left']),\r\n  pulse: PropTypes.bool,\r\n  rotation: PropTypes.oneOf([0, 90, 180, 270]),\r\n  shake: PropTypes.bool,\r\n  size: PropTypes.oneOf(['2xs', 'xs', 'sm', 'lg', 'xl', '2xl', '1x', '2x', '3x', '4x', '5x', '6x', '7x', '8x', '9x', '10x']),\r\n  spin: PropTypes.bool,\r\n  spinPulse: PropTypes.bool,\r\n  spinReverse: PropTypes.bool,\r\n  symbol: PropTypes.oneOfType([PropTypes.bool, PropTypes.string]),\r\n  title: PropTypes.string,\r\n  titleId: PropTypes.string,\r\n  transform: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),\r\n  swapOpacity: PropTypes.bool\r\n};\r\nvar convertCurry = convert.bind(null, React.createElement);\r\n\r\nexport { FontAwesomeIcon };\r\n"], "mappings": ";;;;;;;;;;;;AAKA,SAAS,gBAAgB,GAAGA,IAAGC,IAAG;AAChC,UAAQD,KAAI,eAAeA,EAAC,MAAM,IAAI,OAAO,eAAe,GAAGA,IAAG;AAAA,IAChE,OAAOC;AAAA,IACP,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,UAAU;AAAA,EACZ,CAAC,IAAI,EAAED,EAAC,IAAIC,IAAG;AACjB;AACA,SAAS,UAAUA,IAAG,GAAG;AACvB,MAAI,cAAc,OAAO,KAAK,SAAS,EAAG,OAAM,IAAI,UAAU,oDAAoD;AAClH,EAAAA,GAAE,YAAY,OAAO,OAAO,KAAK,EAAE,WAAW;AAAA,IAC5C,aAAa;AAAA,MACX,OAAOA;AAAA,MACP,UAAU;AAAA,MACV,cAAc;AAAA,IAChB;AAAA,EACF,CAAC,GAAG,OAAO,eAAeA,IAAG,aAAa;AAAA,IACxC,UAAU;AAAA,EACZ,CAAC,GAAG,KAAK,gBAAgBA,IAAG,CAAC;AAC/B;AACA,SAAS,QAAQ,GAAGD,IAAG;AACrB,MAAIC,KAAI,OAAO,KAAK,CAAC;AACrB,MAAI,OAAO,uBAAuB;AAChC,QAAIC,KAAI,OAAO,sBAAsB,CAAC;AACtC,IAAAF,OAAME,KAAIA,GAAE,OAAO,SAAUF,IAAG;AAC9B,aAAO,OAAO,yBAAyB,GAAGA,EAAC,EAAE;AAAA,IAC/C,CAAC,IAAIC,GAAE,KAAK,MAAMA,IAAGC,EAAC;AAAA,EACxB;AACA,SAAOD;AACT;AACA,SAAS,eAAe,GAAG;AACzB,WAASD,KAAI,GAAGA,KAAI,UAAU,QAAQA,MAAK;AACzC,QAAIC,KAAI,QAAQ,UAAUD,EAAC,IAAI,UAAUA,EAAC,IAAI,CAAC;AAC/C,IAAAA,KAAI,IAAI,QAAQ,OAAOC,EAAC,GAAG,IAAE,EAAE,QAAQ,SAAUD,IAAG;AAClD,sBAAgB,GAAGA,IAAGC,GAAED,EAAC,CAAC;AAAA,IAC5B,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,GAAG,OAAO,0BAA0BC,EAAC,CAAC,IAAI,QAAQ,OAAOA,EAAC,CAAC,EAAE,QAAQ,SAAUD,IAAG;AAChJ,aAAO,eAAe,GAAGA,IAAG,OAAO,yBAAyBC,IAAGD,EAAC,CAAC;AAAA,IACnE,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,SAAS,gBAAgBC,IAAG,GAAG;AAC7B,SAAO,kBAAkB,OAAO,iBAAiB,OAAO,eAAe,KAAK,IAAI,SAAUA,IAAGE,IAAG;AAC9F,WAAOF,GAAE,YAAYE,IAAGF;AAAA,EAC1B,GAAG,gBAAgBA,IAAG,CAAC;AACzB;AACA,SAAS,aAAaA,IAAGD,IAAG;AAC1B,MAAI,YAAY,OAAOC,MAAK,CAACA,GAAG,QAAOA;AACvC,MAAI,IAAIA,GAAE,OAAO,WAAW;AAC5B,MAAI,WAAW,GAAG;AAChB,QAAI,IAAI,EAAE,KAAKA,IAAGD,MAAK,SAAS;AAChC,QAAI,YAAY,OAAO,EAAG,QAAO;AACjC,UAAM,IAAI,UAAU,8CAA8C;AAAA,EACpE;AACA,UAAQ,aAAaA,KAAI,SAAS,QAAQC,EAAC;AAC7C;AACA,SAAS,eAAeA,IAAG;AACzB,MAAI,IAAI,aAAaA,IAAG,QAAQ;AAChC,SAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AACxC;AACA,SAAS,cAAc;AACrB,gBAAc,SAAUE,IAAGH,IAAG;AAC5B,WAAO,IAAI,YAAYG,IAAG,QAAQH,EAAC;AAAA,EACrC;AACA,MAAI,IAAI,OAAO,WACbA,KAAI,oBAAI,QAAQ;AAClB,WAAS,YAAYG,IAAGF,IAAGG,IAAG;AAC5B,QAAIF,KAAI,OAAOC,IAAGF,EAAC;AACnB,WAAOD,GAAE,IAAIE,IAAGE,MAAKJ,GAAE,IAAIG,EAAC,CAAC,GAAG,gBAAgBD,IAAG,YAAY,SAAS;AAAA,EAC1E;AACA,WAAS,YAAYC,IAAGF,IAAG;AACzB,QAAIG,KAAIJ,GAAE,IAAIC,EAAC;AACf,WAAO,OAAO,KAAKG,EAAC,EAAE,OAAO,SAAUJ,IAAGC,IAAG;AAC3C,UAAIC,KAAIE,GAAEH,EAAC;AACX,UAAI,YAAY,OAAOC,GAAG,CAAAF,GAAEC,EAAC,IAAIE,GAAED,EAAC;AAAA,WAAO;AACzC,iBAAS,IAAI,GAAG,WAAWC,GAAED,GAAE,CAAC,CAAC,KAAK,IAAI,IAAIA,GAAE,SAAS;AACzD,QAAAF,GAAEC,EAAC,IAAIE,GAAED,GAAE,CAAC,CAAC;AAAA,MACf;AACA,aAAOF;AAAA,IACT,GAAG,uBAAO,OAAO,IAAI,CAAC;AAAA,EACxB;AACA,SAAO,UAAU,aAAa,MAAM,GAAG,YAAY,UAAU,OAAO,SAAUA,IAAG;AAC/E,QAAIC,KAAI,EAAE,KAAK,KAAK,MAAMD,EAAC;AAC3B,QAAIC,IAAG;AACL,MAAAA,GAAE,SAAS,YAAYA,IAAG,IAAI;AAC9B,UAAIG,KAAIH,GAAE;AACV,MAAAG,OAAMA,GAAE,SAAS,YAAYA,IAAG,IAAI;AAAA,IACtC;AACA,WAAOH;AAAA,EACT,GAAG,YAAY,UAAU,OAAO,OAAO,IAAI,SAAUA,IAAGG,IAAG;AACzD,QAAI,YAAY,OAAOA,IAAG;AACxB,UAAIF,KAAIF,GAAE,IAAI,IAAI;AAClB,aAAO,EAAE,OAAO,OAAO,EAAE,KAAK,MAAMC,IAAGG,GAAE,QAAQ,gBAAgB,SAAUD,IAAGH,IAAG;AAC/E,YAAIC,KAAIC,GAAEF,EAAC;AACX,eAAO,OAAO,MAAM,QAAQC,EAAC,IAAIA,GAAE,KAAK,GAAG,IAAIA;AAAA,MACjD,CAAC,CAAC;AAAA,IACJ;AACA,QAAI,cAAc,OAAOG,IAAG;AAC1B,UAAI,IAAI;AACR,aAAO,EAAE,OAAO,OAAO,EAAE,KAAK,MAAMH,IAAG,WAAY;AACjD,YAAIE,KAAI;AACR,eAAO,YAAY,OAAOA,GAAEA,GAAE,SAAS,CAAC,MAAMA,KAAI,CAAC,EAAE,MAAM,KAAKA,EAAC,GAAG,KAAK,YAAYA,IAAG,CAAC,CAAC,GAAGC,GAAE,MAAM,MAAMD,EAAC;AAAA,MAC9G,CAAC;AAAA,IACH;AACA,WAAO,EAAE,OAAO,OAAO,EAAE,KAAK,MAAMF,IAAGG,EAAC;AAAA,EAC1C,GAAG,YAAY,MAAM,MAAM,SAAS;AACtC;AAEA,IAAM,OAAO,MAAM;AAAC;AACpB,IAAI,UAAU,CAAC;AACf,IAAI,YAAY,CAAC;AACjB,IAAI,qBAAqB;AACzB,IAAI,eAAe;AAAA,EACjB,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAI;AACF,MAAI,OAAO,WAAW,YAAa,WAAU;AAC7C,MAAI,OAAO,aAAa,YAAa,aAAY;AACjD,MAAI,OAAO,qBAAqB,YAAa,sBAAqB;AAClE,MAAI,OAAO,gBAAgB,YAAa,gBAAe;AACzD,SAAS,GAAG;AAAC;AACb,IAAM;AAAA,EACJ,YAAY;AACd,IAAI,QAAQ,aAAa,CAAC;AAC1B,IAAM,SAAS;AACf,IAAM,WAAW;AACjB,IAAM,oBAAoB;AAC1B,IAAM,cAAc;AACpB,IAAM,aAAa,CAAC,CAAC,OAAO;AAC5B,IAAM,SAAS,CAAC,CAAC,SAAS,mBAAmB,CAAC,CAAC,SAAS,QAAQ,OAAO,SAAS,qBAAqB,cAAc,OAAO,SAAS,kBAAkB;AACrJ,IAAM,QAAQ,CAAC,UAAU,QAAQ,MAAM,KAAK,CAAC,UAAU,QAAQ,UAAU;AAEzE,IAAI,IAAI;AAAR,IACE,IAAI;AACN,IAAI,IAAI;AAAA,EACJ,SAAS;AAAA,IACP,IAAI;AAAA,IACJ,KAAK;AAAA,IACL,YAAY;AAAA,IACZ,KAAK;AAAA,IACL,cAAc;AAAA,IACd,KAAK;AAAA,IACL,YAAY;AAAA,IACZ,KAAK;AAAA,IACL,WAAW;AAAA,IACX,KAAK;AAAA,IACL,aAAa;AAAA,EACf;AAAA,EACA,SAAS;AAAA,IACP,IAAI;AAAA,IACJ,KAAK;AAAA,IACL,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,MAAM;AAAA,IACN,cAAc;AAAA,IACd,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,WAAW;AAAA,EACb;AAAA,EACA,OAAO;AAAA,IACL,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,cAAc;AAAA,IACd,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,WAAW;AAAA,EACb;AAAA,EACA,iBAAiB;AAAA,IACf,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,cAAc;AAAA,IACd,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,WAAW;AAAA,EACb;AACF;AAhDF,IAiDE,IAAI;AAAA,EACF,OAAO;AAAA,EACP,cAAc;AAAA,EACd,SAAS;AAAA,EACT,WAAW;AACb;AAtDF,IAuDE,IAAI,CAAC,cAAc,cAAc,YAAY,kBAAkB;AACjE,IAAI,IAAI;AAAR,IACE,IAAI;AADN,IAEE,IAAI;AAFN,IAGE,IAAI;AAHN,IAIE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACjB,IAAI,IAAI;AAAA,EACJ,SAAS;AAAA,IACP,KAAK;AAAA,IACL,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAAA,EACA,SAAS;AAAA,IACP,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAAA,EACA,OAAO;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAAA,EACA,iBAAiB;AAAA,IACf,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AACF;AACF,IAAI,KAAK;AAAA,EACL,uBAAuB;AAAA,IACrB,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAAA,EACA,sBAAsB;AAAA,IACpB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAAA,EACA,yBAAyB;AAAA,IACvB,KAAK;AAAA,IACL,QAAQ;AAAA,EACV;AAAA,EACA,0BAA0B;AAAA,IACxB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAAA,EACA,wBAAwB;AAAA,IACtB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAAA,EACA,gCAAgC;AAAA,IAC9B,KAAK;AAAA,IACL,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AACF;AACF,IAAI,KAAK,oBAAI,IAAI,CAAC,CAAC,WAAW;AAAA,EAC1B,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,UAAU,CAAC,SAAS,WAAW,SAAS,QAAQ,QAAQ;AAAA,EACxD,gBAAgB,CAAC;AAAA,EACjB,mBAAmB;AACrB,CAAC,GAAG,CAAC,SAAS;AAAA,EACZ,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,UAAU,CAAC,SAAS,WAAW,SAAS,MAAM;AAAA,EAC9C,gBAAgB,CAAC;AAAA,EACjB,mBAAmB;AACrB,CAAC,GAAG,CAAC,WAAW;AAAA,EACd,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,UAAU,CAAC,SAAS,WAAW,SAAS,MAAM;AAAA,EAC9C,gBAAgB,CAAC;AAAA,EACjB,mBAAmB;AACrB,CAAC,GAAG,CAAC,iBAAiB;AAAA,EACpB,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,UAAU,CAAC,SAAS,WAAW,SAAS,MAAM;AAAA,EAC9C,gBAAgB,CAAC;AAAA,EACjB,mBAAmB;AACrB,CAAC,CAAC,CAAC;AAxBL,IAyBE,KAAK;AAAA,EACH,SAAS;AAAA,IACP,OAAO;AAAA,IACP,SAAS;AAAA,IACT,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ;AAAA,EACV;AAAA,EACA,SAAS;AAAA,IACP,OAAO;AAAA,IACP,SAAS;AAAA,IACT,OAAO;AAAA,IACP,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,OAAO;AAAA,IACP,SAAS;AAAA,IACT,OAAO;AAAA,IACP,MAAM;AAAA,EACR;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,IACP,SAAS;AAAA,IACT,OAAO;AAAA,IACP,MAAM;AAAA,EACR;AACF;AACF,IAAI,KAAK,CAAC,OAAO,UAAU,QAAQ,gBAAgB;AAAnD,IACE,KAAK;AAAA,EACH,KAAK;AAAA,IACH,KAAK;AAAA,IACL,UAAU;AAAA,EACZ;AAAA,EACA,eAAe;AAAA,IACb,MAAM;AAAA,IACN,kBAAkB;AAAA,EACpB;AACF;AAVF,IAWE,KAAK,CAAC,KAAK;AACb,IAAI,KAAK;AAAA,EACP,KAAK;AAAA,IACH,UAAU;AAAA,EACZ;AAAA,EACA,eAAe;AAAA,IACb,kBAAkB;AAAA,EACpB;AACF;AACA,IAAI,KAAK,CAAC,OAAO,MAAM;AAAvB,IACE,KAAK;AAAA,EACH,KAAK;AAAA,IACH,KAAK;AAAA,EACP;AAAA,EACA,eAAe;AAAA,IACb,MAAM;AAAA,EACR;AACF;AACF,IAAI,KAAK;AAAA,EACL,KAAK;AAAA,IACH,KAAK;AAAA,EACP;AAAA,EACA,eAAe;AAAA,IACb,eAAe;AAAA,EACjB;AACF;AAEF,IAAI,MAAM;AAAA,EACN,OAAO;AAAA,EACP,cAAc;AAAA,EACd,SAAS;AAAA,EACT,WAAW;AACb;AALF,IAME,MAAM,CAAC,cAAc,cAAc,YAAY,kBAAkB;AACnE,IAAI,OAAO,CAAC,OAAO,UAAU,QAAQ,gBAAgB;AACrD,IAAI,KAAK;AAAA,EACL,oBAAoB;AAAA,IAClB,KAAK;AAAA,IACL,QAAQ;AAAA,EACV;AAAA,EACA,4BAA4B;AAAA,IAC1B,KAAK;AAAA,IACL,QAAQ;AAAA,EACV;AACF;AACF,IAAI,KAAK;AAAA,EACL,SAAS;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AAAA,EACA,SAAS;AAAA,IACP,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AAAA,EACA,OAAO;AAAA,IACL,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AAAA,EACA,iBAAiB;AAAA,IACf,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AACF;AA1BF,IA2BE,MAAM;AAAA,EACJ,SAAS,CAAC,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAC3C,SAAS,CAAC,QAAQ,QAAQ,MAAM;AAAA,EAChC,OAAO,CAAC,QAAQ,QAAQ,QAAQ,MAAM;AAAA,EACtC,iBAAiB,CAAC,SAAS,SAAS,SAAS,OAAO;AACtD;AAhCF,IAiCE,KAAK;AAAA,EACH,SAAS;AAAA,IACP,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAAA,EACA,SAAS;AAAA,IACP,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,EACR;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,EACT;AACF;AA3DF,IA4DE,IAAI,CAAC,YAAY,cAAc,YAAY,WAAW,cAAc,WAAW;AA5DjF,IA6DE,KAAK,CAAC,MAAM,OAAO,OAAO,OAAO,OAAO,OAAO,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,QAAQ,SAAS,SAAS,SAAS,SAAS,GAAG,KAAK,GAAG,CAAC;AA7DhK,IA8DE,MAAM,CAAC,SAAS,WAAW,SAAS,QAAQ,WAAW,QAAQ;AA9DjE,IA+DE,MAAM,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE;AA/DtC,IAgEE,MAAM,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;AAhE3D,IAiEE,KAAK,CAAC,GAAG,OAAO,KAAK,GAAG,GAAG,GAAG,KAAK,OAAO,MAAM,MAAM,MAAM,MAAM,OAAO,QAAQ,UAAU,QAAQ,aAAa,UAAU,aAAa,mBAAmB,iBAAiB,QAAQ,MAAM,WAAW,kBAAkB,eAAe,UAAU,MAAM,aAAa,cAAc,SAAS,cAAc,cAAc,aAAa,aAAa,SAAS,cAAc,gBAAgB,QAAQ,YAAY,YAAY,SAAS,MAAM,IAAI,OAAO,IAAI,cAAc,IAAI,SAAS,IAAI,SAAS,EAAE,OAAO,IAAI,IAAI,OAAK,GAAG,OAAO,GAAG,GAAG,CAAC,CAAC,EAAE,OAAO,IAAI,IAAI,OAAK,KAAK,OAAO,CAAC,CAAC,CAAC;AAC3iB,IAAI,KAAK;AAAA,EACL,uBAAuB;AAAA,IACrB,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAAA,EACA,sBAAsB;AAAA,IACpB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,KAAK;AAAA,EACP;AAAA,EACA,yBAAyB;AAAA,IACvB,KAAK;AAAA,IACL,QAAQ;AAAA,EACV;AAAA,EACA,0BAA0B;AAAA,IACxB,KAAK;AAAA,EACP;AACF;AAEF,IAAM,uBAAuB;AAC7B,IAAM,gBAAgB;AACtB,IAAM,qBAAqB;AAC3B,IAAM,4BAA4B;AAClC,IAAM,gBAAgB;AACtB,IAAM,yBAAyB;AAC/B,IAAM,iCAAiC;AACvC,IAAM,cAAc;AACpB,IAAM,YAAY;AAClB,IAAM,8BAA8B;AACpC,IAAM,0BAA0B;AAChC,IAAM,sCAAsC,CAAC,QAAQ,QAAQ,SAAS,QAAQ;AAC9E,IAAM,cAAc,MAAM;AACxB,MAAI;AACF,WAAO;AAAA,EACT,SAAS,MAAM;AACb,WAAO;AAAA,EACT;AACF,GAAG;AACH,SAAS,YAAY,KAAK;AAExB,SAAO,IAAI,MAAM,KAAK;AAAA,IACpB,IAAI,QAAQ,MAAM;AAChB,aAAO,QAAQ,SAAS,OAAO,IAAI,IAAI,OAAO,CAAC;AAAA,IACjD;AAAA,EACF,CAAC;AACH;AACA,IAAM,mBAAmB,eAAe,CAAC,GAAG,CAAC;AAK7C,iBAAiB,CAAC,IAAI,eAAe,eAAe,eAAe,eAAe,CAAC,GAAG;AAAA,EACpF,cAAc;AAChB,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,aAAa,CAAC;AACxC,IAAM,kBAAkB,YAAY,gBAAgB;AACpD,IAAM,mBAAmB,eAAe,CAAC,GAAG,EAAE;AAI9C,iBAAiB,CAAC,IAAI,eAAe,eAAe,eAAe,eAAe,CAAC,GAAG;AAAA,EACpF,SAAS;AACX,CAAC,GAAG,iBAAiB,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,aAAa,CAAC;AACvD,IAAM,kBAAkB,YAAY,gBAAgB;AACpD,IAAM,wBAAwB,eAAe,CAAC,GAAG,EAAE;AACnD,sBAAsB,CAAC,IAAI,eAAe,eAAe,CAAC,GAAG,sBAAsB,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC;AACjG,IAAM,uBAAuB,YAAY,qBAAqB;AAC9D,IAAM,wBAAwB,eAAe,CAAC,GAAG,EAAE;AACnD,sBAAsB,CAAC,IAAI,eAAe,eAAe,CAAC,GAAG,sBAAsB,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC;AACjG,IAAM,uBAAuB,YAAY,qBAAqB;AAC9D,IAAM,gCAAgC;AAEtC,IAAM,wBAAwB;AAC9B,IAAM,sBAAsB;AAC5B,IAAM,yBAAyB,eAAe,CAAC,GAAG,CAAC;AACnD,IAAM,wBAAwB,YAAY,sBAAsB;AAChE,IAAM,kCAAkC,CAAC,SAAS,eAAe,aAAa,qBAAqB,cAAc;AACjH,IAAM,kBAAkB;AACxB,IAAM,mBAAmB,CAAC,GAAG,IAAI,GAAG,EAAE;AAEtC,IAAM,UAAU,OAAO,qBAAqB,CAAC;AAC7C,SAAS,cAAc,MAAM;AAC3B,MAAI,UAAU,SAAS,cAAc,YAAY,OAAO,GAAG;AAC3D,MAAI,SAAS;AACX,WAAO,QAAQ,aAAa,IAAI;AAAA,EAClC;AACF;AACA,SAAS,OAAO,KAAK;AAGnB,MAAI,QAAQ,GAAI,QAAO;AACvB,MAAI,QAAQ,QAAS,QAAO;AAC5B,MAAI,QAAQ,OAAQ,QAAO;AAC3B,SAAO;AACT;AACA,IAAI,YAAY,OAAO,SAAS,kBAAkB,YAAY;AAC5D,QAAM,QAAQ,CAAC,CAAC,sBAAsB,cAAc,GAAG,CAAC,mBAAmB,WAAW,GAAG,CAAC,uBAAuB,eAAe,GAAG,CAAC,sBAAsB,cAAc,GAAG,CAAC,0BAA0B,kBAAkB,GAAG,CAAC,yBAAyB,gBAAgB,GAAG,CAAC,qBAAqB,YAAY,GAAG,CAAC,kBAAkB,UAAU,GAAG,CAAC,+BAA+B,sBAAsB,GAAG,CAAC,0BAA0B,kBAAkB,GAAG,CAAC,wBAAwB,gBAAgB,GAAG,CAAC,6BAA6B,oBAAoB,GAAG,CAAC,4BAA4B,oBAAoB,GAAG,CAAC,2BAA2B,kBAAkB,CAAC;AAC1nB,QAAM,QAAQ,UAAQ;AACpB,QAAI,CAAC,MAAM,GAAG,IAAI;AAClB,UAAM,MAAM,OAAO,cAAc,IAAI,CAAC;AACtC,QAAI,QAAQ,UAAa,QAAQ,MAAM;AACrC,cAAQ,GAAG,IAAI;AAAA,IACjB;AAAA,EACF,CAAC;AACH;AACA,IAAM,WAAW;AAAA,EACf,cAAc;AAAA,EACd,eAAe;AAAA,EACf,WAAW;AAAA,EACX,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,kBAAkB;AACpB;AAGA,IAAI,QAAQ,cAAc;AACxB,UAAQ,YAAY,QAAQ;AAC9B;AACA,IAAM,UAAU,eAAe,eAAe,CAAC,GAAG,QAAQ,GAAG,OAAO;AACpE,IAAI,CAAC,QAAQ,eAAgB,SAAQ,mBAAmB;AACxD,IAAM,SAAS,CAAC;AAChB,OAAO,KAAK,QAAQ,EAAE,QAAQ,SAAO;AACnC,SAAO,eAAe,QAAQ,KAAK;AAAA,IACjC,YAAY;AAAA,IACZ,KAAK,SAAU,KAAK;AAClB,cAAQ,GAAG,IAAI;AACf,kBAAY,QAAQ,QAAM,GAAG,MAAM,CAAC;AAAA,IACtC;AAAA,IACA,KAAK,WAAY;AACf,aAAO,QAAQ,GAAG;AAAA,IACpB;AAAA,EACF,CAAC;AACH,CAAC;AAGD,OAAO,eAAe,QAAQ,gBAAgB;AAAA,EAC5C,YAAY;AAAA,EACZ,KAAK,SAAU,KAAK;AAClB,YAAQ,YAAY;AACpB,gBAAY,QAAQ,QAAM,GAAG,MAAM,CAAC;AAAA,EACtC;AAAA,EACA,KAAK,WAAY;AACf,WAAO,QAAQ;AAAA,EACjB;AACF,CAAC;AACD,OAAO,oBAAoB;AAC3B,IAAM,cAAc,CAAC;AACrB,SAAS,SAAS,IAAI;AACpB,cAAY,KAAK,EAAE;AACnB,SAAO,MAAM;AACX,gBAAY,OAAO,YAAY,QAAQ,EAAE,GAAG,CAAC;AAAA,EAC/C;AACF;AAEA,IAAM,MAAM;AACZ,IAAM,uBAAuB;AAAA,EAC3B,MAAM;AAAA,EACN,GAAG;AAAA,EACH,GAAG;AAAA,EACH,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AACT;AACA,SAAS,UAAUC,MAAK;AACtB,MAAI,CAACA,QAAO,CAAC,QAAQ;AACnB;AAAA,EACF;AACA,QAAM,QAAQ,SAAS,cAAc,OAAO;AAC5C,QAAM,aAAa,QAAQ,UAAU;AACrC,QAAM,YAAYA;AAClB,QAAM,eAAe,SAAS,KAAK;AACnC,MAAI,cAAc;AAClB,WAAS,IAAI,aAAa,SAAS,GAAG,IAAI,IAAI,KAAK;AACjD,UAAM,QAAQ,aAAa,CAAC;AAC5B,UAAM,WAAW,MAAM,WAAW,IAAI,YAAY;AAClD,QAAI,CAAC,SAAS,MAAM,EAAE,QAAQ,OAAO,IAAI,IAAI;AAC3C,oBAAc;AAAA,IAChB;AAAA,EACF;AACA,WAAS,KAAK,aAAa,OAAO,WAAW;AAC7C,SAAOA;AACT;AACA,IAAM,SAAS;AACf,SAAS,eAAe;AACtB,MAAI,OAAO;AACX,MAAI,KAAK;AACT,SAAO,SAAS,GAAG;AACjB,UAAM,OAAO,KAAK,OAAO,IAAI,KAAK,CAAC;AAAA,EACrC;AACA,SAAO;AACT;AACA,SAAS,QAAQ,KAAK;AACpB,QAAM,QAAQ,CAAC;AACf,WAAS,KAAK,OAAO,CAAC,GAAG,WAAW,GAAG,OAAM;AAC3C,UAAM,CAAC,IAAI,IAAI,CAAC;AAAA,EAClB;AACA,SAAO;AACT;AACA,SAAS,WAAW,MAAM;AACxB,MAAI,KAAK,WAAW;AAClB,WAAO,QAAQ,KAAK,SAAS;AAAA,EAC/B,OAAO;AACL,YAAQ,KAAK,aAAa,OAAO,KAAK,IAAI,MAAM,GAAG,EAAE,OAAO,OAAK,CAAC;AAAA,EACpE;AACF;AACA,SAAS,WAAW,KAAK;AACvB,SAAO,GAAG,OAAO,GAAG,EAAE,QAAQ,MAAM,OAAO,EAAE,QAAQ,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO,EAAE,QAAQ,MAAM,MAAM,EAAE,QAAQ,MAAM,MAAM;AACxI;AACA,SAAS,eAAe,YAAY;AAClC,SAAO,OAAO,KAAK,cAAc,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,kBAAkB;AAClE,WAAO,MAAM,GAAG,OAAO,eAAe,IAAK,EAAE,OAAO,WAAW,WAAW,aAAa,CAAC,GAAG,IAAK;AAAA,EAClG,GAAG,EAAE,EAAE,KAAK;AACd;AACA,SAAS,WAAWC,SAAQ;AAC1B,SAAO,OAAO,KAAKA,WAAU,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,cAAc;AAC1D,WAAO,MAAM,GAAG,OAAO,WAAW,IAAI,EAAE,OAAOA,QAAO,SAAS,EAAE,KAAK,GAAG,GAAG;AAAA,EAC9E,GAAG,EAAE;AACP;AACA,SAAS,sBAAsB,WAAW;AACxC,SAAO,UAAU,SAAS,qBAAqB,QAAQ,UAAU,MAAM,qBAAqB,KAAK,UAAU,MAAM,qBAAqB,KAAK,UAAU,WAAW,qBAAqB,UAAU,UAAU,SAAS,UAAU;AAC9N;AACA,SAAS,gBAAgB,MAAM;AAC7B,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,WAAW,aAAa,OAAO,iBAAiB,GAAG,OAAO;AAAA,EAC5D;AACA,QAAM,iBAAiB,aAAa,OAAO,UAAU,IAAI,IAAI,IAAI,EAAE,OAAO,UAAU,IAAI,IAAI,IAAI;AAChG,QAAM,aAAa,SAAS,OAAO,UAAU,OAAO,MAAM,UAAU,QAAQ,KAAK,IAAI,IAAI,EAAE,OAAO,UAAU,OAAO,MAAM,UAAU,QAAQ,KAAK,IAAI,IAAI;AACxJ,QAAM,cAAc,UAAU,OAAO,UAAU,QAAQ,OAAO;AAC9D,QAAM,QAAQ;AAAA,IACZ,WAAW,GAAG,OAAO,gBAAgB,GAAG,EAAE,OAAO,YAAY,GAAG,EAAE,OAAO,WAAW;AAAA,EACtF;AACA,QAAM,OAAO;AAAA,IACX,WAAW,aAAa,OAAO,YAAY,IAAI,IAAI,QAAQ;AAAA,EAC7D;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,gBAAgB,OAAO;AAC9B,MAAI;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,gBAAgB;AAAA,EAClB,IAAI;AACJ,MAAI,MAAM;AACV,MAAI,iBAAiB,OAAO;AAC1B,WAAO,aAAa,OAAO,UAAU,IAAI,MAAM,QAAQ,GAAG,MAAM,EAAE,OAAO,UAAU,IAAI,MAAM,SAAS,GAAG,MAAM;AAAA,EACjH,WAAW,eAAe;AACxB,WAAO,yBAAyB,OAAO,UAAU,IAAI,KAAK,mBAAmB,EAAE,OAAO,UAAU,IAAI,KAAK,OAAO;AAAA,EAClH,OAAO;AACL,WAAO,aAAa,OAAO,UAAU,IAAI,KAAK,MAAM,EAAE,OAAO,UAAU,IAAI,KAAK,MAAM;AAAA,EACxF;AACA,SAAO,SAAS,OAAO,UAAU,OAAO,OAAO,UAAU,QAAQ,KAAK,IAAI,IAAI,EAAE,OAAO,UAAU,OAAO,OAAO,UAAU,QAAQ,KAAK,IAAI,IAAI;AAC9I,SAAO,UAAU,OAAO,UAAU,QAAQ,OAAO;AACjD,SAAO;AACT;AAEA,IAAI,aAAa;AAEjB,SAAS,MAAM;AACb,QAAM,MAAM;AACZ,QAAM,MAAM;AACZ,QAAM,KAAK,OAAO;AAClB,QAAM,KAAK,OAAO;AAClB,MAAIC,KAAI;AACR,MAAI,OAAO,OAAO,OAAO,KAAK;AAC5B,UAAM,QAAQ,IAAI,OAAO,MAAM,OAAO,KAAK,KAAK,GAAG,GAAG;AACtD,UAAM,iBAAiB,IAAI,OAAO,OAAO,OAAO,KAAK,KAAK,GAAG,GAAG;AAChE,UAAM,QAAQ,IAAI,OAAO,MAAM,OAAO,GAAG,GAAG,GAAG;AAC/C,IAAAA,KAAIA,GAAE,QAAQ,OAAO,IAAI,OAAO,IAAI,GAAG,CAAC,EAAE,QAAQ,gBAAgB,KAAK,OAAO,IAAI,GAAG,CAAC,EAAE,QAAQ,OAAO,IAAI,OAAO,EAAE,CAAC;AAAA,EACvH;AACA,SAAOA;AACT;AACA,IAAI,eAAe;AACnB,SAAS,YAAY;AACnB,MAAI,OAAO,cAAc,CAAC,cAAc;AACtC,cAAU,IAAI,CAAC;AACf,mBAAe;AAAA,EACjB;AACF;AACA,IAAI,YAAY;AAAA,EACd,SAAS;AACP,WAAO;AAAA,MACL,KAAK;AAAA,QACH;AAAA,QACA,WAAW;AAAA,MACb;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ;AACN,WAAO;AAAA,MACL,2BAA2B;AACzB,kBAAU;AAAA,MACZ;AAAA,MACA,cAAc;AACZ,kBAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,IAAI,UAAU,CAAC;AACrB,IAAI,CAAC,EAAE,oBAAoB,EAAG,GAAE,oBAAoB,IAAI,CAAC;AACzD,IAAI,CAAC,EAAE,oBAAoB,EAAE,OAAQ,GAAE,oBAAoB,EAAE,SAAS,CAAC;AACvE,IAAI,CAAC,EAAE,oBAAoB,EAAE,MAAO,GAAE,oBAAoB,EAAE,QAAQ,CAAC;AACrE,IAAI,CAAC,EAAE,oBAAoB,EAAE,MAAO,GAAE,oBAAoB,EAAE,QAAQ,CAAC;AACrE,IAAI,YAAY,EAAE,oBAAoB;AAEtC,IAAM,YAAY,CAAC;AACnB,IAAM,WAAW,WAAY;AAC3B,WAAS,oBAAoB,oBAAoB,QAAQ;AACzD,WAAS;AACT,YAAU,IAAI,QAAM,GAAG,CAAC;AAC1B;AACA,IAAI,SAAS;AACb,IAAI,QAAQ;AACV,YAAU,SAAS,gBAAgB,WAAW,eAAe,iBAAiB,KAAK,SAAS,UAAU;AACtG,MAAI,CAAC,OAAQ,UAAS,iBAAiB,oBAAoB,QAAQ;AACrE;AACA,SAAS,SAAU,IAAI;AACrB,MAAI,CAAC,OAAQ;AACb,WAAS,WAAW,IAAI,CAAC,IAAI,UAAU,KAAK,EAAE;AAChD;AAEA,SAAS,OAAO,eAAe;AAC7B,QAAM;AAAA,IACJ;AAAA,IACA,aAAa,CAAC;AAAA,IACd,WAAW,CAAC;AAAA,EACd,IAAI;AACJ,MAAI,OAAO,kBAAkB,UAAU;AACrC,WAAO,WAAW,aAAa;AAAA,EACjC,OAAO;AACL,WAAO,IAAI,OAAO,KAAK,GAAG,EAAE,OAAO,eAAe,UAAU,GAAG,GAAG,EAAE,OAAO,SAAS,IAAI,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,EAAE,OAAO,KAAK,GAAG;AAAA,EACjI;AACF;AAEA,SAAS,gBAAgB,SAAS,QAAQ,UAAU;AAClD,MAAI,WAAW,QAAQ,MAAM,KAAK,QAAQ,MAAM,EAAE,QAAQ,GAAG;AAC3D,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,MAAM,QAAQ,MAAM,EAAE,QAAQ;AAAA,IAChC;AAAA,EACF;AACF;AAMA,IAAI,gBAAgB,SAASC,eAAc,MAAM,aAAa;AAC5D,SAAO,SAAU,GAAG,GAAG,GAAG,GAAG;AAC3B,WAAO,KAAK,KAAK,aAAa,GAAG,GAAG,GAAG,CAAC;AAAA,EAC1C;AACF;AAaA,IAAI,SAAS,SAAS,iBAAiB,SAAS,IAAI,cAAc,aAAa;AAC7E,MAAI,OAAO,OAAO,KAAK,OAAO,GAC5B,SAAS,KAAK,QACd,WAAW,gBAAgB,SAAY,cAAc,IAAI,WAAW,IAAI,IACxE,GACA,KACA;AACF,MAAI,iBAAiB,QAAW;AAC9B,QAAI;AACJ,aAAS,QAAQ,KAAK,CAAC,CAAC;AAAA,EAC1B,OAAO;AACL,QAAI;AACJ,aAAS;AAAA,EACX;AACA,SAAO,IAAI,QAAQ,KAAK;AACtB,UAAM,KAAK,CAAC;AACZ,aAAS,SAAS,QAAQ,QAAQ,GAAG,GAAG,KAAK,OAAO;AAAA,EACtD;AACA,SAAO;AACT;AA2BA,SAAS,WAAW,QAAQ;AAC1B,QAAM,SAAS,CAAC;AAChB,MAAIC,WAAU;AACd,QAAM,SAAS,OAAO;AACtB,SAAOA,WAAU,QAAQ;AACvB,UAAM,QAAQ,OAAO,WAAWA,UAAS;AACzC,QAAI,SAAS,SAAU,SAAS,SAAUA,WAAU,QAAQ;AAC1D,YAAM,QAAQ,OAAO,WAAWA,UAAS;AACzC,WAAK,QAAQ,UAAW,OAAQ;AAE9B,eAAO,OAAO,QAAQ,SAAU,OAAO,QAAQ,QAAS,KAAO;AAAA,MACjE,OAAO;AACL,eAAO,KAAK,KAAK;AACjB,QAAAA;AAAA,MACF;AAAA,IACF,OAAO;AACL,aAAO,KAAK,KAAK;AAAA,IACnB;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,MAAM,SAAS;AACtB,QAAM,UAAU,WAAW,OAAO;AAClC,SAAO,QAAQ,WAAW,IAAI,QAAQ,CAAC,EAAE,SAAS,EAAE,IAAI;AAC1D;AACA,SAAS,YAAY,QAAQ,OAAO;AAClC,QAAM,OAAO,OAAO;AACpB,MAAI,QAAQ,OAAO,WAAW,KAAK;AACnC,MAAI;AACJ,MAAI,SAAS,SAAU,SAAS,SAAU,OAAO,QAAQ,GAAG;AAC1D,aAAS,OAAO,WAAW,QAAQ,CAAC;AACpC,QAAI,UAAU,SAAU,UAAU,OAAQ;AACxC,cAAQ,QAAQ,SAAU,OAAQ,SAAS,QAAS;AAAA,IACtD;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,eAAe,OAAO;AAC7B,SAAO,OAAO,KAAK,KAAK,EAAE,OAAO,CAAC,KAAK,aAAa;AAClD,UAAMC,QAAO,MAAM,QAAQ;AAC3B,UAAM,WAAW,CAAC,CAACA,MAAK;AACxB,QAAI,UAAU;AACZ,UAAIA,MAAK,QAAQ,IAAIA,MAAK;AAAA,IAC5B,OAAO;AACL,UAAI,QAAQ,IAAIA;AAAA,IAClB;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AACA,SAAS,YAAY,QAAQ,OAAO;AAClC,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,QAAM;AAAA,IACJ,YAAY;AAAA,EACd,IAAI;AACJ,QAAM,aAAa,eAAe,KAAK;AACvC,MAAI,OAAO,UAAU,MAAM,YAAY,cAAc,CAAC,WAAW;AAC/D,cAAU,MAAM,QAAQ,QAAQ,eAAe,KAAK,CAAC;AAAA,EACvD,OAAO;AACL,cAAU,OAAO,MAAM,IAAI,eAAe,eAAe,CAAC,GAAG,UAAU,OAAO,MAAM,KAAK,CAAC,CAAC,GAAG,UAAU;AAAA,EAC1G;AAQA,MAAI,WAAW,OAAO;AACpB,gBAAY,MAAM,KAAK;AAAA,EACzB;AACF;AAEA,IAAM,gBAAgB,CAAc,YAAY,sCAAsC;AAAA,EACpF,IAAI;AAAA,EACJ,IAAI;AACN,CAAC,GAAgB,YAAY,wEAAwE;AAAA,EACnG,MAAM;AAAA,EACN,IAAI;AAAA,EACJ,MAAM;AAAA,EACN,IAAI;AACN,CAAC,GAAgB,YAAY,qCAAqC;AAAA,EAChE,MAAM;AAAA,EACN,IAAI;AACN,CAAC,CAAC;AAEF,IAAM;AAAA,EACJ;AAAA,EACA;AACF,IAAI;AACJ,IAAM,eAAe,OAAO,KAAK,oBAAoB;AACrD,IAAM,sBAAsB,aAAa,OAAO,CAAC,KAAK,aAAa;AACjE,MAAI,QAAQ,IAAI,OAAO,KAAK,qBAAqB,QAAQ,CAAC;AAC1D,SAAO;AACT,GAAG,CAAC,CAAC;AACL,IAAI,uBAAuB;AAC3B,IAAI,aAAa,CAAC;AAClB,IAAI,cAAc,CAAC;AACnB,IAAI,aAAa,CAAC;AAClB,IAAI,gBAAgB,CAAC;AACrB,IAAI,WAAW,CAAC;AAChB,SAAS,WAAW,MAAM;AACxB,SAAO,CAAC,iBAAiB,QAAQ,IAAI;AACvC;AACA,SAAS,YAAY,WAAW,KAAK;AACnC,QAAM,QAAQ,IAAI,MAAM,GAAG;AAC3B,QAAM,SAAS,MAAM,CAAC;AACtB,QAAM,WAAW,MAAM,MAAM,CAAC,EAAE,KAAK,GAAG;AACxC,MAAI,WAAW,aAAa,aAAa,MAAM,CAAC,WAAW,QAAQ,GAAG;AACpE,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AACA,IAAM,QAAQ,MAAM;AAClB,QAAM,SAAS,aAAW;AACxB,WAAO,OAAO,QAAQ,CAAC,MAAM,OAAO,WAAW;AAC7C,WAAK,MAAM,IAAI,OAAO,OAAO,SAAS,CAAC,CAAC;AACxC,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAAA,EACP;AACA,eAAa,OAAO,CAAC,KAAKA,OAAM,aAAa;AAC3C,QAAIA,MAAK,CAAC,GAAG;AACX,UAAIA,MAAK,CAAC,CAAC,IAAI;AAAA,IACjB;AACA,QAAIA,MAAK,CAAC,GAAG;AACX,YAAM,UAAUA,MAAK,CAAC,EAAE,OAAO,UAAQ;AACrC,eAAO,OAAO,SAAS;AAAA,MACzB,CAAC;AACD,cAAQ,QAAQ,WAAS;AACvB,YAAI,MAAM,SAAS,EAAE,CAAC,IAAI;AAAA,MAC5B,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT,CAAC;AACD,gBAAc,OAAO,CAAC,KAAKA,OAAM,aAAa;AAC5C,QAAI,QAAQ,IAAI;AAChB,QAAIA,MAAK,CAAC,GAAG;AACX,YAAM,UAAUA,MAAK,CAAC,EAAE,OAAO,UAAQ;AACrC,eAAO,OAAO,SAAS;AAAA,MACzB,CAAC;AACD,cAAQ,QAAQ,WAAS;AACvB,YAAI,KAAK,IAAI;AAAA,MACf,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT,CAAC;AACD,aAAW,OAAO,CAAC,KAAKA,OAAM,aAAa;AACzC,UAAM,UAAUA,MAAK,CAAC;AACtB,QAAI,QAAQ,IAAI;AAChB,YAAQ,QAAQ,WAAS;AACvB,UAAI,KAAK,IAAI;AAAA,IACf,CAAC;AACD,WAAO;AAAA,EACT,CAAC;AAID,QAAM,aAAa,SAAS,UAAU,OAAO;AAC7C,QAAM,cAAc,OAAO,OAAO,CAAC,KAAK,SAAS;AAC/C,UAAM,wBAAwB,KAAK,CAAC;AACpC,QAAI,SAAS,KAAK,CAAC;AACnB,UAAM,WAAW,KAAK,CAAC;AACvB,QAAI,WAAW,SAAS,CAAC,YAAY;AACnC,eAAS;AAAA,IACX;AACA,QAAI,OAAO,0BAA0B,UAAU;AAC7C,UAAI,MAAM,qBAAqB,IAAI;AAAA,QACjC;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,QAAI,OAAO,0BAA0B,UAAU;AAC7C,UAAI,SAAS,sBAAsB,SAAS,EAAE,CAAC,IAAI;AAAA,QACjD;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT,GAAG;AAAA,IACD,OAAO,CAAC;AAAA,IACR,UAAU,CAAC;AAAA,EACb,CAAC;AACD,eAAa,YAAY;AACzB,kBAAgB,YAAY;AAC5B,yBAAuB,mBAAmB,OAAO,cAAc;AAAA,IAC7D,QAAQ,OAAO;AAAA,EACjB,CAAC;AACH;AACA,SAAS,UAAQ;AACf,yBAAuB,mBAAmB,KAAK,cAAc;AAAA,IAC3D,QAAQ,OAAO;AAAA,EACjB,CAAC;AACH,CAAC;AACD,MAAM;AACN,SAAS,UAAU,QAAQ,SAAS;AAClC,UAAQ,WAAW,MAAM,KAAK,CAAC,GAAG,OAAO;AAC3C;AACA,SAAS,WAAW,QAAQ,UAAU;AACpC,UAAQ,YAAY,MAAM,KAAK,CAAC,GAAG,QAAQ;AAC7C;AACA,SAAS,QAAQ,QAAQ,OAAO;AAC9B,UAAQ,SAAS,MAAM,KAAK,CAAC,GAAG,KAAK;AACvC;AACA,SAAS,UAAU,MAAM;AACvB,SAAO,WAAW,IAAI,KAAK;AAAA,IACzB,QAAQ;AAAA,IACR,UAAU;AAAA,EACZ;AACF;AACA,SAAS,aAAa,SAAS;AAC7B,QAAM,aAAa,cAAc,OAAO;AACxC,QAAM,aAAa,UAAU,OAAO,OAAO;AAC3C,SAAO,eAAe,aAAa;AAAA,IACjC,QAAQ;AAAA,IACR,UAAU;AAAA,EACZ,IAAI,SAAS;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,EACZ;AACF;AACA,SAAS,yBAAyB;AAChC,SAAO;AACT;AACA,IAAM,qBAAqB,MAAM;AAC/B,SAAO;AAAA,IACL,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,MAAM,CAAC;AAAA,EACT;AACF;AACA,SAAS,YAAY,QAAQ;AAC3B,MAAI,SAAS;AACb,QAAM,WAAW,aAAa,OAAO,CAAC,KAAK,aAAa;AACtD,QAAI,QAAQ,IAAI,GAAG,OAAO,OAAO,WAAW,GAAG,EAAE,OAAO,QAAQ;AAChE,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,IAAE,QAAQ,cAAY;AACpB,QAAI,OAAO,SAAS,SAAS,QAAQ,CAAC,KAAK,OAAO,KAAK,UAAQ,oBAAoB,QAAQ,EAAE,SAAS,IAAI,CAAC,GAAG;AAC5G,eAAS;AAAA,IACX;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,SAAS,mBAAmB,eAAe;AACzC,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,QAAM;AAAA,IACJ,SAAS;AAAA,EACX,IAAI;AACJ,QAAM,QAAQ,gBAAgB,MAAM,EAAE,aAAa;AAGnD,MAAI,WAAW,KAAK,CAAC,eAAe;AAClC,WAAO;AAAA,EACT;AACA,QAAM,SAAS,gBAAgB,MAAM,EAAE,aAAa,KAAK,gBAAgB,MAAM,EAAE,KAAK;AACtF,QAAM,UAAU,iBAAiB,UAAU,SAAS,gBAAgB;AACpE,QAAM,SAAS,UAAU,WAAW;AACpC,SAAO;AACT;AACA,SAAS,uBAAuB,YAAY;AAC1C,MAAI,OAAO,CAAC;AACZ,MAAI,WAAW;AACf,aAAW,QAAQ,SAAO;AACxB,UAAM,SAAS,YAAY,OAAO,WAAW,GAAG;AAChD,QAAI,QAAQ;AACV,iBAAW;AAAA,IACb,WAAW,KAAK;AACd,WAAK,KAAK,GAAG;AAAA,IACf;AAAA,EACF,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,mBAAmB,KAAK;AAC/B,SAAO,IAAI,KAAK,EAAE,OAAO,CAAC,OAAO,OAAOC,SAAQ;AAC9C,WAAOA,KAAI,QAAQ,KAAK,MAAM;AAAA,EAChC,CAAC;AACH;AACA,SAAS,iBAAiB,QAAQ;AAChC,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,QAAM;AAAA,IACJ,cAAc;AAAA,EAChB,IAAI;AACJ,MAAI,cAAc;AAClB,QAAM,oBAAoB,GAAG,OAAO,IAAI;AACxC,QAAM,yBAAyB,mBAAmB,OAAO,OAAO,SAAO,kBAAkB,SAAS,GAAG,CAAC,CAAC;AACvG,QAAM,0BAA0B,mBAAmB,OAAO,OAAO,SAAO,CAAC,GAAG,SAAS,GAAG,CAAC,CAAC;AAC1F,QAAM,WAAW,uBAAuB,OAAO,SAAO;AACpD,kBAAc;AACd,WAAO,CAAC,EAAE,SAAS,GAAG;AAAA,EACxB,CAAC;AACD,QAAM,CAAC,kBAAkB,IAAI,IAAI;AACjC,QAAM,SAAS,YAAY,sBAAsB;AACjD,QAAM,YAAY,eAAe,eAAe,CAAC,GAAG,uBAAuB,uBAAuB,CAAC,GAAG,CAAC,GAAG;AAAA,IACxG,QAAQ,mBAAmB,iBAAiB;AAAA,MAC1C;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,SAAO,eAAe,eAAe,eAAe,CAAC,GAAG,SAAS,GAAG,0BAA0B;AAAA,IAC5F;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,CAAC,GAAG,kBAAkB,aAAa,aAAa,SAAS,CAAC;AAC7D;AACA,SAAS,kBAAkB,aAAa,aAAa,WAAW;AAC9D,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,eAAe,CAAC,UAAU,CAAC,UAAU;AACvC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,OAAO,gBAAgB,OAAO,UAAU,QAAQ,IAAI,CAAC;AAC3D,QAAM,gBAAgB,QAAQ,QAAQ,QAAQ;AAC9C,aAAW,KAAK,YAAY,iBAAiB;AAC7C,WAAS,KAAK,UAAU;AACxB,MAAI,WAAW,SAAS,CAAC,OAAO,KAAK,KAAK,OAAO,KAAK,KAAK,CAAC,OAAO,cAAc;AAG/E,aAAS;AAAA,EACX;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAM,uBAAuB,EAAE,OAAO,cAAY;AAChD,SAAO,aAAa,KAAK,aAAa;AACxC,CAAC;AACD,IAAM,qBAAqB,OAAO,KAAK,EAAE,EAAE,OAAO,SAAO,QAAQ,CAAC,EAAE,IAAI,SAAO,OAAO,KAAK,GAAG,GAAG,CAAC,CAAC,EAAE,KAAK;AAC1G,SAAS,0BAA0B,eAAe;AAChD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA,cAAc;AAAA,IACd,QAAAL,UAAS,CAAC;AAAA,IACV,QAAQ,YAAY,CAAC;AAAA,EACvB,IAAI;AACJ,QAAM,kBAAkB,WAAW;AACnC,QAAM,mBAAmB,OAAO,SAAS,YAAY,KAAK,OAAO,SAAS,KAAK;AAC/E,QAAM,yBAAyB,UAAU,kBAAkB;AAC3D,QAAM,2BAA2B,UAAU,WAAW,SAAS,UAAU,WAAW;AACpF,MAAI,CAAC,oBAAoB,oBAAoB,0BAA0B,2BAA2B;AAChG,cAAU,SAAS;AAAA,EACrB;AACA,MAAI,OAAO,SAAS,WAAW,KAAK,OAAO,SAAS,KAAK,GAAG;AAC1D,cAAU,SAAS;AAAA,EACrB;AACA,MAAI,CAAC,UAAU,UAAU,qBAAqB,SAAS,MAAM,GAAG;AAC9D,UAAM,cAAc,OAAO,KAAKA,OAAM,EAAE,KAAK,SAAO,mBAAmB,SAAS,GAAG,CAAC;AACpF,QAAI,eAAe,UAAU,cAAc;AACzC,YAAM,gBAAgB,GAAG,IAAI,MAAM,EAAE;AACrC,gBAAU,SAAS;AACnB,gBAAU,WAAW,QAAQ,UAAU,QAAQ,UAAU,QAAQ,KAAK,UAAU;AAAA,IAClF;AAAA,EACF;AACA,MAAI,UAAU,WAAW,QAAQ,gBAAgB,MAAM;AAGrD,cAAU,SAAS,uBAAuB,KAAK;AAAA,EACjD;AACA,SAAO;AACT;AAEA,IAAM,UAAN,MAAc;AAAA,EACZ,cAAc;AACZ,SAAK,cAAc,CAAC;AAAA,EACtB;AAAA,EACA,MAAM;AACJ,aAAS,OAAO,UAAU,QAAQ,cAAc,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC9F,kBAAY,IAAI,IAAI,UAAU,IAAI;AAAA,IACpC;AACA,UAAM,YAAY,YAAY,OAAO,KAAK,kBAAkB,CAAC,CAAC;AAC9D,WAAO,KAAK,SAAS,EAAE,QAAQ,SAAO;AACpC,WAAK,YAAY,GAAG,IAAI,eAAe,eAAe,CAAC,GAAG,KAAK,YAAY,GAAG,KAAK,CAAC,CAAC,GAAG,UAAU,GAAG,CAAC;AACtG,kBAAY,KAAK,UAAU,GAAG,CAAC;AAG/B,YAAM,aAAa,qBAAqB,CAAC,EAAE,GAAG;AAC9C,UAAI,WAAY,aAAY,YAAY,UAAU,GAAG,CAAC;AACtD,YAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EACA,QAAQ;AACN,SAAK,cAAc,CAAC;AAAA,EACtB;AAAA,EACA,iBAAiB,WAAW,YAAY;AACtC,UAAM,aAAa,WAAW,UAAU,WAAW,YAAY,WAAW,OAAO;AAAA,MAC/E,GAAG;AAAA,IACL,IAAI;AACJ,WAAO,KAAK,UAAU,EAAE,IAAI,SAAO;AACjC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA,MAAAI;AAAA,MACF,IAAI,WAAW,GAAG;AAClB,YAAM,UAAUA,MAAK,CAAC;AACtB,UAAI,CAAC,UAAU,MAAM,EAAG,WAAU,MAAM,IAAI,CAAC;AAC7C,UAAI,QAAQ,SAAS,GAAG;AACtB,gBAAQ,QAAQ,WAAS;AACvB,cAAI,OAAO,UAAU,UAAU;AAC7B,sBAAU,MAAM,EAAE,KAAK,IAAIA;AAAA,UAC7B;AAAA,QACF,CAAC;AAAA,MACH;AACA,gBAAU,MAAM,EAAE,QAAQ,IAAIA;AAAA,IAChC,CAAC;AACD,WAAO;AAAA,EACT;AACF;AAEA,IAAI,WAAW,CAAC;AAChB,IAAI,SAAS,CAAC;AACd,IAAM,YAAY,CAAC;AACnB,IAAM,sBAAsB,OAAO,KAAK,SAAS;AACjD,SAAS,gBAAgB,aAAa,MAAM;AAC1C,MAAI;AAAA,IACF,WAAW;AAAA,EACb,IAAI;AACJ,aAAW;AACX,WAAS,CAAC;AACV,SAAO,KAAK,SAAS,EAAE,QAAQ,OAAK;AAClC,QAAI,oBAAoB,QAAQ,CAAC,MAAM,IAAI;AACzC,aAAO,UAAU,CAAC;AAAA,IACpB;AAAA,EACF,CAAC;AACD,WAAS,QAAQ,YAAU;AACzB,UAAM,SAAS,OAAO,SAAS,OAAO,OAAO,IAAI,CAAC;AAClD,WAAO,KAAK,MAAM,EAAE,QAAQ,QAAM;AAChC,UAAI,OAAO,OAAO,EAAE,MAAM,YAAY;AACpC,YAAI,EAAE,IAAI,OAAO,EAAE;AAAA,MACrB;AACA,UAAI,OAAO,OAAO,EAAE,MAAM,UAAU;AAClC,eAAO,KAAK,OAAO,EAAE,CAAC,EAAE,QAAQ,QAAM;AACpC,cAAI,CAAC,IAAI,EAAE,GAAG;AACZ,gBAAI,EAAE,IAAI,CAAC;AAAA,UACb;AACA,cAAI,EAAE,EAAE,EAAE,IAAI,OAAO,EAAE,EAAE,EAAE;AAAA,QAC7B,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,QAAI,OAAO,OAAO;AAChB,YAAM,QAAQ,OAAO,MAAM;AAC3B,aAAO,KAAK,KAAK,EAAE,QAAQ,UAAQ;AACjC,YAAI,CAAC,OAAO,IAAI,GAAG;AACjB,iBAAO,IAAI,IAAI,CAAC;AAAA,QAClB;AACA,eAAO,IAAI,EAAE,KAAK,MAAM,IAAI,CAAC;AAAA,MAC/B,CAAC;AAAA,IACH;AACA,QAAI,OAAO,UAAU;AACnB,aAAO,SAAS,SAAS;AAAA,IAC3B;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,SAAS,WAAW,MAAM,aAAa;AACrC,WAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC1G,SAAK,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,EACjC;AACA,QAAM,UAAU,OAAO,IAAI,KAAK,CAAC;AACjC,UAAQ,QAAQ,YAAU;AACxB,kBAAc,OAAO,MAAM,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC;AAAA,EACzD,CAAC;AACD,SAAO;AACT;AACA,SAAS,UAAU,MAAM;AACvB,WAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,QAAQ,IAAI,QAAQ,IAAI,CAAC,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AACjH,SAAK,QAAQ,CAAC,IAAI,UAAU,KAAK;AAAA,EACnC;AACA,QAAM,UAAU,OAAO,IAAI,KAAK,CAAC;AACjC,UAAQ,QAAQ,YAAU;AACxB,WAAO,MAAM,MAAM,IAAI;AAAA,EACzB,CAAC;AACD,SAAO;AACT;AACA,SAAS,eAAe;AACtB,QAAM,OAAO,UAAU,CAAC;AACxB,QAAM,OAAO,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC;AACpD,SAAO,UAAU,IAAI,IAAI,UAAU,IAAI,EAAE,MAAM,MAAM,IAAI,IAAI;AAC/D;AAEA,SAAS,mBAAmB,YAAY;AACtC,MAAI,WAAW,WAAW,MAAM;AAC9B,eAAW,SAAS;AAAA,EACtB;AACA,MAAI;AAAA,IACF;AAAA,EACF,IAAI;AACJ,QAAM,SAAS,WAAW,UAAU,uBAAuB;AAC3D,MAAI,CAAC,SAAU;AACf,aAAW,QAAQ,QAAQ,QAAQ,KAAK;AACxC,SAAO,gBAAgB,QAAQ,aAAa,QAAQ,QAAQ,KAAK,gBAAgB,UAAU,QAAQ,QAAQ,QAAQ;AACrH;AACA,IAAM,UAAU,IAAI,QAAQ;AAC5B,IAAM,SAAS,MAAM;AACnB,SAAO,iBAAiB;AACxB,SAAO,mBAAmB;AAC1B,YAAU,QAAQ;AACpB;AACA,IAAM,MAAM;AAAA,EACV,OAAO,WAAY;AACjB,QAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,QAAI,QAAQ;AACV,gBAAU,eAAe,MAAM;AAC/B,mBAAa,sBAAsB,MAAM;AACzC,aAAO,aAAa,SAAS,MAAM;AAAA,IACrC,OAAO;AACL,aAAO,QAAQ,OAAO,IAAI,MAAM,wCAAwC,CAAC;AAAA,IAC3E;AAAA,EACF;AAAA,EACA,OAAO,WAAY;AACjB,QAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,OAAO,mBAAmB,OAAO;AACnC,aAAO,iBAAiB;AAAA,IAC1B;AACA,WAAO,mBAAmB;AAC1B,aAAS,MAAM;AACb,kBAAY;AAAA,QACV;AAAA,MACF,CAAC;AACD,gBAAU,SAAS,MAAM;AAAA,IAC3B,CAAC;AAAA,EACH;AACF;AACA,IAAM,QAAQ;AAAA,EACZ,MAAM,CAAAA,UAAQ;AACZ,QAAIA,UAAS,MAAM;AACjB,aAAO;AAAA,IACT;AACA,QAAI,OAAOA,UAAS,YAAYA,MAAK,UAAUA,MAAK,UAAU;AAC5D,aAAO;AAAA,QACL,QAAQA,MAAK;AAAA,QACb,UAAU,QAAQA,MAAK,QAAQA,MAAK,QAAQ,KAAKA,MAAK;AAAA,MACxD;AAAA,IACF;AACA,QAAI,MAAM,QAAQA,KAAI,KAAKA,MAAK,WAAW,GAAG;AAC5C,YAAM,WAAWA,MAAK,CAAC,EAAE,QAAQ,KAAK,MAAM,IAAIA,MAAK,CAAC,EAAE,MAAM,CAAC,IAAIA,MAAK,CAAC;AACzE,YAAM,SAAS,mBAAmBA,MAAK,CAAC,CAAC;AACzC,aAAO;AAAA,QACL;AAAA,QACA,UAAU,QAAQ,QAAQ,QAAQ,KAAK;AAAA,MACzC;AAAA,IACF;AACA,QAAI,OAAOA,UAAS,aAAaA,MAAK,QAAQ,GAAG,OAAO,OAAO,WAAW,GAAG,CAAC,IAAI,MAAMA,MAAK,MAAM,6BAA6B,IAAI;AAClI,YAAM,gBAAgB,iBAAiBA,MAAK,MAAM,GAAG,GAAG;AAAA,QACtD,aAAa;AAAA,MACf,CAAC;AACD,aAAO;AAAA,QACL,QAAQ,cAAc,UAAU,uBAAuB;AAAA,QACvD,UAAU,QAAQ,cAAc,QAAQ,cAAc,QAAQ,KAAK,cAAc;AAAA,MACnF;AAAA,IACF;AACA,QAAI,OAAOA,UAAS,UAAU;AAC5B,YAAM,SAAS,uBAAuB;AACtC,aAAO;AAAA,QACL;AAAA,QACA,UAAU,QAAQ,QAAQA,KAAI,KAAKA;AAAA,MACrC;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,MAAM;AAAA,EACV;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,cAAc,WAAY;AAC9B,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,QAAM;AAAA,IACJ,qBAAqB;AAAA,EACvB,IAAI;AACJ,OAAK,OAAO,KAAK,UAAU,MAAM,EAAE,SAAS,KAAK,OAAO,iBAAiB,UAAU,OAAO,eAAgB,KAAI,IAAI,MAAM;AAAA,IACtH,MAAM;AAAA,EACR,CAAC;AACH;AAEA,SAAS,YAAY,KAAK,iBAAiB;AACzC,SAAO,eAAe,KAAK,YAAY;AAAA,IACrC,KAAK;AAAA,EACP,CAAC;AACD,SAAO,eAAe,KAAK,QAAQ;AAAA,IACjC,KAAK,WAAY;AACf,aAAO,IAAI,SAAS,IAAI,OAAK,OAAO,CAAC,CAAC;AAAA,IACxC;AAAA,EACF,CAAC;AACD,SAAO,eAAe,KAAK,QAAQ;AAAA,IACjC,KAAK,WAAY;AACf,UAAI,CAAC,OAAQ;AACb,YAAM,YAAY,SAAS,cAAc,KAAK;AAC9C,gBAAU,YAAY,IAAI;AAC1B,aAAO,UAAU;AAAA,IACnB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEA,SAAS,OAAQ,MAAM;AACrB,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAAJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,sBAAsB,SAAS,KAAK,KAAK,SAAS,CAAC,KAAK,OAAO;AACjE,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,SAAS;AAAA,MACb,GAAG,QAAQ,SAAS;AAAA,MACpB,GAAG;AAAA,IACL;AACA,eAAW,OAAO,IAAI,WAAW,eAAe,eAAe,CAAC,GAAGA,OAAM,GAAG,CAAC,GAAG;AAAA,MAC9E,oBAAoB,GAAG,OAAO,OAAO,IAAI,UAAU,IAAI,IAAI,KAAK,EAAE,OAAO,OAAO,IAAI,UAAU,IAAI,IAAI,IAAI;AAAA,IAC5G,CAAC,CAAC;AAAA,EACJ;AACA,SAAO,CAAC;AAAA,IACN,KAAK;AAAA,IACL;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAEA,SAAS,SAAU,MAAM;AACvB,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,KAAK,WAAW,OAAO,GAAG,OAAO,QAAQ,GAAG,EAAE,OAAO,OAAO,WAAW,GAAG,EAAE,OAAO,QAAQ,IAAI;AACrG,SAAO,CAAC;AAAA,IACN,KAAK;AAAA,IACL,YAAY;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU,CAAC;AAAA,MACT,KAAK;AAAA,MACL,YAAY,eAAe,eAAe,CAAC,GAAG,UAAU,GAAG,CAAC,GAAG;AAAA,QAC7D;AAAA,MACF,CAAC;AAAA,MACD;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AAEA,SAAS,sBAAsB,QAAQ;AACrC,QAAM;AAAA,IACJ,OAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAY;AAAA,EACd,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,KAAK,QAAQ,OAAO;AACxB,QAAM,iBAAiB,GAAG,SAAS,MAAM;AACzC,QAAM,YAAY,CAAC,OAAO,kBAAkB,WAAW,GAAG,OAAO,OAAO,WAAW,GAAG,EAAE,OAAO,QAAQ,IAAI,EAAE,EAAE,OAAO,UAAQ,MAAM,QAAQ,QAAQ,IAAI,MAAM,EAAE,EAAE,OAAO,UAAQ,SAAS,MAAM,CAAC,CAAC,IAAI,EAAE,OAAO,MAAM,OAAO,EAAE,KAAK,GAAG;AACtO,MAAI,UAAU;AAAA,IACZ,UAAU,CAAC;AAAA,IACX,YAAY,eAAe,eAAe,CAAC,GAAG,MAAM,UAAU,GAAG,CAAC,GAAG;AAAA,MACnE,eAAe;AAAA,MACf,aAAa;AAAA,MACb,SAAS;AAAA,MACT,QAAQ,MAAM,WAAW,QAAQ;AAAA,MACjC,SAAS;AAAA,MACT,WAAW,OAAO,OAAO,OAAO,GAAG,EAAE,OAAO,MAAM;AAAA,IACpD,CAAC;AAAA,EACH;AACA,QAAM,yBAAyB,kBAAkB,CAAC,CAAC,MAAM,QAAQ,QAAQ,OAAO,IAAI;AAAA,IAClF,OAAO,GAAG,OAAO,QAAQ,SAAS,KAAK,QAAQ,IAAI;AAAA,EACrD,IAAI,CAAC;AACL,MAAI,WAAW;AACb,YAAQ,WAAW,aAAa,IAAI;AAAA,EACtC;AACA,MAAI,OAAO;AACT,YAAQ,SAAS,KAAK;AAAA,MACpB,KAAK;AAAA,MACL,YAAY;AAAA,QACV,IAAI,QAAQ,WAAW,iBAAiB,KAAK,SAAS,OAAO,WAAW,aAAa,CAAC;AAAA,MACxF;AAAA,MACA,UAAU,CAAC,KAAK;AAAA,IAClB,CAAC;AACD,WAAO,QAAQ,WAAW;AAAA,EAC5B;AACA,QAAM,OAAO,eAAe,eAAe,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG;AAAA,IAC3D;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ,eAAe,eAAe,CAAC,GAAG,sBAAsB,GAAG,MAAM,MAAM;AAAA,EACjF,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,KAAK,SAAS,KAAK,QAAQ,aAAa,wBAAwB,IAAI,KAAK;AAAA,IAC3E,UAAU,CAAC;AAAA,IACX,YAAY,CAAC;AAAA,EACf,IAAI,aAAa,wBAAwB,IAAI,KAAK;AAAA,IAChD,UAAU,CAAC;AAAA,IACX,YAAY,CAAC;AAAA,EACf;AACA,OAAK,WAAW;AAChB,OAAK,aAAa;AAClB,MAAI,QAAQ;AACV,WAAO,SAAS,IAAI;AAAA,EACtB,OAAO;AACL,WAAO,OAAO,IAAI;AAAA,EACpB;AACF;AACA,SAAS,uBAAuB,QAAQ;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAY;AAAA,EACd,IAAI;AACJ,QAAM,aAAa,eAAe,eAAe,eAAe,CAAC,GAAG,MAAM,UAAU,GAAG,QAAQ;AAAA,IAC7F,SAAS;AAAA,EACX,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG;AAAA,IACX,SAAS,MAAM,QAAQ,KAAK,GAAG;AAAA,EACjC,CAAC;AACD,MAAI,WAAW;AACb,eAAW,aAAa,IAAI;AAAA,EAC9B;AACA,QAAMA,UAAS,eAAe,CAAC,GAAG,MAAM,MAAM;AAC9C,MAAI,sBAAsB,SAAS,GAAG;AACpC,IAAAA,QAAO,WAAW,IAAI,gBAAgB;AAAA,MACpC;AAAA,MACA,eAAe;AAAA,MACf;AAAA,MACA;AAAA,IACF,CAAC;AACD,IAAAA,QAAO,mBAAmB,IAAIA,QAAO,WAAW;AAAA,EAClD;AACA,QAAM,cAAc,WAAWA,OAAM;AACrC,MAAI,YAAY,SAAS,GAAG;AAC1B,eAAW,OAAO,IAAI;AAAA,EACxB;AACA,QAAM,MAAM,CAAC;AACb,MAAI,KAAK;AAAA,IACP,KAAK;AAAA,IACL;AAAA,IACA,UAAU,CAAC,OAAO;AAAA,EACpB,CAAC;AACD,MAAI,OAAO;AACT,QAAI,KAAK;AAAA,MACP,KAAK;AAAA,MACL,YAAY;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU,CAAC,KAAK;AAAA,IAClB,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,SAAS,0BAA0B,QAAQ;AACzC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,aAAa,eAAe,eAAe,eAAe,CAAC,GAAG,MAAM,UAAU,GAAG,QAAQ;AAAA,IAC7F,SAAS;AAAA,EACX,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG;AAAA,IACX,SAAS,MAAM,QAAQ,KAAK,GAAG;AAAA,EACjC,CAAC;AACD,QAAM,cAAc,WAAW,MAAM,MAAM;AAC3C,MAAI,YAAY,SAAS,GAAG;AAC1B,eAAW,OAAO,IAAI;AAAA,EACxB;AACA,QAAM,MAAM,CAAC;AACb,MAAI,KAAK;AAAA,IACP,KAAK;AAAA,IACL;AAAA,IACA,UAAU,CAAC,OAAO;AAAA,EACpB,CAAC;AACD,MAAI,OAAO;AACT,QAAI,KAAK;AAAA,MACP,KAAK;AAAA,MACL,YAAY;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU,CAAC,KAAK;AAAA,IAClB,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAEA,IAAM;AAAA,EACJ,QAAQ;AACV,IAAI;AACJ,SAAS,YAAYI,OAAM;AACzB,QAAM,QAAQA,MAAK,CAAC;AACpB,QAAM,SAASA,MAAK,CAAC;AACrB,QAAM,CAAC,UAAU,IAAIA,MAAK,MAAM,CAAC;AACjC,MAAI,UAAU;AACd,MAAI,MAAM,QAAQ,UAAU,GAAG;AAC7B,cAAU;AAAA,MACR,KAAK;AAAA,MACL,YAAY;AAAA,QACV,OAAO,GAAG,OAAO,OAAO,WAAW,GAAG,EAAE,OAAO,gBAAgB,KAAK;AAAA,MACtE;AAAA,MACA,UAAU,CAAC;AAAA,QACT,KAAK;AAAA,QACL,YAAY;AAAA,UACV,OAAO,GAAG,OAAO,OAAO,WAAW,GAAG,EAAE,OAAO,gBAAgB,SAAS;AAAA,UACxE,MAAM;AAAA,UACN,GAAG,WAAW,CAAC;AAAA,QACjB;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,YAAY;AAAA,UACV,OAAO,GAAG,OAAO,OAAO,WAAW,GAAG,EAAE,OAAO,gBAAgB,OAAO;AAAA,UACtE,MAAM;AAAA,UACN,GAAG,WAAW,CAAC;AAAA,QACjB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,OAAO;AACL,cAAU;AAAA,MACR,KAAK;AAAA,MACL,YAAY;AAAA,QACV,MAAM;AAAA,QACN,GAAG;AAAA,MACL;AAAA,IACF;AAAA,EACF;AACA,SAAO;AAAA,IACL,OAAO;AAAA,IACP;AAAA,IACA;AAAA,IACA,MAAM;AAAA,EACR;AACF;AACA,IAAM,6BAA6B;AAAA,EACjC,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AACV;AACA,SAAS,mBAAmB,UAAU,QAAQ;AAC5C,MAAI,CAAC,cAAc,CAAC,OAAO,oBAAoB,UAAU;AACvD,YAAQ,MAAM,mBAAoB,OAAO,UAAU,gBAAkB,EAAE,OAAO,QAAQ,eAAgB,CAAC;AAAA,EACzG;AACF;AACA,SAAS,SAAS,UAAU,QAAQ;AAClC,MAAI,cAAc;AAClB,MAAI,WAAW,QAAQ,OAAO,iBAAiB,MAAM;AACnD,aAAS,uBAAuB;AAAA,EAClC;AACA,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,QAAI,gBAAgB,MAAM;AACxB,YAAM,OAAO,UAAU,QAAQ,KAAK,CAAC;AACrC,iBAAW,KAAK,YAAY;AAC5B,eAAS,KAAK,UAAU;AAAA,IAC1B;AACA,QAAI,YAAY,UAAU,SAAS,MAAM,KAAK,SAAS,MAAM,EAAE,QAAQ,GAAG;AACxE,YAAMA,QAAO,SAAS,MAAM,EAAE,QAAQ;AACtC,aAAO,QAAQ,YAAYA,KAAI,CAAC;AAAA,IAClC;AACA,uBAAmB,UAAU,MAAM;AACnC,YAAQ,eAAe,eAAe,CAAC,GAAG,0BAA0B,GAAG,CAAC,GAAG;AAAA,MACzE,MAAM,OAAO,oBAAoB,WAAW,aAAa,qBAAqB,KAAK,CAAC,IAAI,CAAC;AAAA,IAC3F,CAAC,CAAC;AAAA,EACJ,CAAC;AACH;AAEA,IAAM,SAAS,MAAM;AAAC;AACtB,IAAM,MAAM,OAAO,sBAAsB,eAAe,YAAY,QAAQ,YAAY,UAAU,cAAc;AAAA,EAC9G,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAM,WAAW;AACjB,IAAM,QAAQ,UAAQ;AACpB,MAAI,KAAK,GAAG,OAAO,UAAU,GAAG,EAAE,OAAO,MAAM,SAAS,CAAC;AACzD,SAAO,MAAM,IAAI,IAAI;AACvB;AACA,IAAM,MAAM,UAAQ;AAClB,MAAI,KAAK,GAAG,OAAO,UAAU,GAAG,EAAE,OAAO,MAAM,OAAO,CAAC;AACvD,MAAI,QAAQ,GAAG,OAAO,UAAU,GAAG,EAAE,OAAO,IAAI,GAAG,GAAG,OAAO,UAAU,GAAG,EAAE,OAAO,MAAM,SAAS,GAAG,GAAG,OAAO,UAAU,GAAG,EAAE,OAAO,MAAM,OAAO,CAAC;AACrJ;AACA,IAAI,OAAO;AAAA,EACT;AAAA,EACA;AACF;AAEA,IAAM,SAAS,MAAM;AAAC;AACtB,SAAS,UAAU,MAAM;AACvB,QAAM,QAAQ,KAAK,eAAe,KAAK,aAAa,aAAa,IAAI;AACrE,SAAO,OAAO,UAAU;AAC1B;AACA,SAAS,iBAAiB,MAAM;AAC9B,QAAM,SAAS,KAAK,eAAe,KAAK,aAAa,WAAW,IAAI;AACpE,QAAMA,QAAO,KAAK,eAAe,KAAK,aAAa,SAAS,IAAI;AAChE,SAAO,UAAUA;AACnB;AACA,SAAS,gBAAgB,MAAM;AAC7B,SAAO,QAAQ,KAAK,aAAa,KAAK,UAAU,YAAY,KAAK,UAAU,SAAS,OAAO,gBAAgB;AAC7G;AACA,SAAS,aAAa;AACpB,MAAI,OAAO,mBAAmB,MAAM;AAClC,WAAO,SAAS;AAAA,EAClB;AACA,QAAM,UAAU,SAAS,OAAO,cAAc;AAC9C,SAAO,WAAW,SAAS;AAC7B;AACA,SAAS,gBAAgB,KAAK;AAC5B,SAAO,SAAS,gBAAgB,8BAA8B,GAAG;AACnE;AACA,SAAS,cAAc,KAAK;AAC1B,SAAO,SAAS,cAAc,GAAG;AACnC;AACA,SAAS,WAAW,aAAa;AAC/B,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,QAAM;AAAA,IACJ,OAAO,YAAY,QAAQ,QAAQ,kBAAkB;AAAA,EACvD,IAAI;AACJ,MAAI,OAAO,gBAAgB,UAAU;AACnC,WAAO,SAAS,eAAe,WAAW;AAAA,EAC5C;AACA,QAAM,MAAM,KAAK,YAAY,GAAG;AAChC,SAAO,KAAK,YAAY,cAAc,CAAC,CAAC,EAAE,QAAQ,SAAU,KAAK;AAC/D,QAAI,aAAa,KAAK,YAAY,WAAW,GAAG,CAAC;AAAA,EACnD,CAAC;AACD,QAAM,WAAW,YAAY,YAAY,CAAC;AAC1C,WAAS,QAAQ,SAAU,OAAO;AAChC,QAAI,YAAY,WAAW,OAAO;AAAA,MAChC;AAAA,IACF,CAAC,CAAC;AAAA,EACJ,CAAC;AACD,SAAO;AACT;AACA,SAAS,cAAc,MAAM;AAC3B,MAAI,UAAU,IAAI,OAAO,KAAK,WAAW,GAAG;AAE5C,YAAU,GAAG,OAAO,SAAS,+BAA+B;AAE5D,SAAO;AACT;AACA,IAAM,WAAW;AAAA,EACf,SAAS,SAAU,UAAU;AAC3B,UAAM,OAAO,SAAS,CAAC;AACvB,QAAI,KAAK,YAAY;AACnB,eAAS,CAAC,EAAE,QAAQ,cAAY;AAC9B,aAAK,WAAW,aAAa,WAAW,QAAQ,GAAG,IAAI;AAAA,MACzD,CAAC;AACD,UAAI,KAAK,aAAa,aAAa,MAAM,QAAQ,OAAO,oBAAoB;AAC1E,YAAI,UAAU,SAAS,cAAc,cAAc,IAAI,CAAC;AACxD,aAAK,WAAW,aAAa,SAAS,IAAI;AAAA,MAC5C,OAAO;AACL,aAAK,OAAO;AAAA,MACd;AAAA,IACF;AAAA,EACF;AAAA,EACA,MAAM,SAAU,UAAU;AACxB,UAAM,OAAO,SAAS,CAAC;AACvB,UAAM,WAAW,SAAS,CAAC;AAI3B,QAAI,CAAC,WAAW,IAAI,EAAE,QAAQ,OAAO,gBAAgB,GAAG;AACtD,aAAO,SAAS,QAAQ,QAAQ;AAAA,IAClC;AACA,UAAM,SAAS,IAAI,OAAO,GAAG,OAAO,OAAO,WAAW,KAAK,CAAC;AAC5D,WAAO,SAAS,CAAC,EAAE,WAAW;AAC9B,QAAI,SAAS,CAAC,EAAE,WAAW,OAAO;AAChC,YAAM,eAAe,SAAS,CAAC,EAAE,WAAW,MAAM,MAAM,GAAG,EAAE,OAAO,CAAC,KAAK,QAAQ;AAChF,YAAI,QAAQ,OAAO,oBAAoB,IAAI,MAAM,MAAM,GAAG;AACxD,cAAI,MAAM,KAAK,GAAG;AAAA,QACpB,OAAO;AACL,cAAI,OAAO,KAAK,GAAG;AAAA,QACrB;AACA,eAAO;AAAA,MACT,GAAG;AAAA,QACD,QAAQ,CAAC;AAAA,QACT,OAAO,CAAC;AAAA,MACV,CAAC;AACD,eAAS,CAAC,EAAE,WAAW,QAAQ,aAAa,MAAM,KAAK,GAAG;AAC1D,UAAI,aAAa,OAAO,WAAW,GAAG;AACpC,aAAK,gBAAgB,OAAO;AAAA,MAC9B,OAAO;AACL,aAAK,aAAa,SAAS,aAAa,OAAO,KAAK,GAAG,CAAC;AAAA,MAC1D;AAAA,IACF;AACA,UAAM,eAAe,SAAS,IAAI,OAAK,OAAO,CAAC,CAAC,EAAE,KAAK,IAAI;AAC3D,SAAK,aAAa,eAAe,EAAE;AACnC,SAAK,YAAY;AAAA,EACnB;AACF;AACA,SAAS,qBAAqB,IAAI;AAChC,KAAG;AACL;AACA,SAAS,QAAQ,WAAW,UAAU;AACpC,QAAM,mBAAmB,OAAO,aAAa,aAAa,WAAW;AACrE,MAAI,UAAU,WAAW,GAAG;AAC1B,qBAAiB;AAAA,EACnB,OAAO;AACL,QAAI,QAAQ;AACZ,QAAI,OAAO,mBAAmB,yBAAyB;AACrD,cAAQ,OAAO,yBAAyB;AAAA,IAC1C;AACA,UAAM,MAAM;AACV,YAAM,UAAU,WAAW;AAC3B,YAAM,OAAO,KAAK,MAAM,QAAQ;AAChC,gBAAU,IAAI,OAAO;AACrB,WAAK;AACL,uBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,IAAI,WAAW;AACf,SAAS,qBAAqB;AAC5B,aAAW;AACb;AACA,SAAS,oBAAoB;AAC3B,aAAW;AACb;AACA,IAAI,KAAK;AACT,SAAS,QAAQ,SAAS;AACxB,MAAI,CAAC,mBAAmB;AACtB;AAAA,EACF;AACA,MAAI,CAAC,OAAO,kBAAkB;AAC5B;AAAA,EACF;AACA,QAAM;AAAA,IACJ,eAAe;AAAA,IACf,eAAe;AAAA,IACf,yBAAyB;AAAA,IACzB,uBAAuB;AAAA,EACzB,IAAI;AACJ,OAAK,IAAI,kBAAkB,aAAW;AACpC,QAAI,SAAU;AACd,UAAM,gBAAgB,uBAAuB;AAC7C,YAAQ,OAAO,EAAE,QAAQ,oBAAkB;AACzC,UAAI,eAAe,SAAS,eAAe,eAAe,WAAW,SAAS,KAAK,CAAC,UAAU,eAAe,WAAW,CAAC,CAAC,GAAG;AAC3H,YAAI,OAAO,sBAAsB;AAC/B,iCAAuB,eAAe,MAAM;AAAA,QAC9C;AACA,qBAAa,eAAe,MAAM;AAAA,MACpC;AACA,UAAI,eAAe,SAAS,gBAAgB,eAAe,OAAO,cAAc,OAAO,sBAAsB;AAC3G,+BAAuB,eAAe,OAAO,UAAU;AAAA,MACzD;AACA,UAAI,eAAe,SAAS,gBAAgB,UAAU,eAAe,MAAM,KAAK,CAAC,gCAAgC,QAAQ,eAAe,aAAa,GAAG;AACtJ,YAAI,eAAe,kBAAkB,WAAW,iBAAiB,eAAe,MAAM,GAAG;AACvF,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI,iBAAiB,WAAW,eAAe,MAAM,CAAC;AACtD,yBAAe,OAAO,aAAa,aAAa,UAAU,aAAa;AACvE,cAAI,SAAU,gBAAe,OAAO,aAAa,WAAW,QAAQ;AAAA,QACtE,WAAW,gBAAgB,eAAe,MAAM,GAAG;AACjD,uBAAa,eAAe,MAAM;AAAA,QACpC;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,MAAI,CAAC,OAAQ;AACb,KAAG,QAAQ,sBAAsB;AAAA,IAC/B,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,SAAS;AAAA,EACX,CAAC;AACH;AACA,SAAS,aAAa;AACpB,MAAI,CAAC,GAAI;AACT,KAAG,WAAW;AAChB;AAEA,SAAS,YAAa,MAAM;AAC1B,QAAM,QAAQ,KAAK,aAAa,OAAO;AACvC,MAAI,MAAM,CAAC;AACX,MAAI,OAAO;AACT,UAAM,MAAM,MAAM,GAAG,EAAE,OAAO,CAAC,KAAKE,WAAU;AAC5C,YAAMN,UAASM,OAAM,MAAM,GAAG;AAC9B,YAAM,OAAON,QAAO,CAAC;AACrB,YAAM,QAAQA,QAAO,MAAM,CAAC;AAC5B,UAAI,QAAQ,MAAM,SAAS,GAAG;AAC5B,YAAI,IAAI,IAAI,MAAM,KAAK,GAAG,EAAE,KAAK;AAAA,MACnC;AACA,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAAA,EACP;AACA,SAAO;AACT;AAEA,SAAS,YAAa,MAAM;AAC1B,QAAM,iBAAiB,KAAK,aAAa,aAAa;AACtD,QAAM,mBAAmB,KAAK,aAAa,WAAW;AACtD,QAAM,YAAY,KAAK,cAAc,SAAY,KAAK,UAAU,KAAK,IAAI;AACzE,MAAI,MAAM,iBAAiB,WAAW,IAAI,CAAC;AAC3C,MAAI,CAAC,IAAI,QAAQ;AACf,QAAI,SAAS,uBAAuB;AAAA,EACtC;AACA,MAAI,kBAAkB,kBAAkB;AACtC,QAAI,SAAS;AACb,QAAI,WAAW;AAAA,EACjB;AACA,MAAI,IAAI,YAAY,IAAI,QAAQ;AAC9B,WAAO;AAAA,EACT;AACA,MAAI,IAAI,UAAU,UAAU,SAAS,GAAG;AACtC,QAAI,WAAW,WAAW,IAAI,QAAQ,KAAK,SAAS,KAAK,UAAU,IAAI,QAAQ,MAAM,KAAK,SAAS,CAAC;AAAA,EACtG;AACA,MAAI,CAAC,IAAI,YAAY,OAAO,gBAAgB,KAAK,cAAc,KAAK,WAAW,aAAa,KAAK,WAAW;AAC1G,QAAI,WAAW,KAAK,WAAW;AAAA,EACjC;AACA,SAAO;AACT;AAEA,SAAS,iBAAkB,MAAM;AAC/B,QAAM,kBAAkB,QAAQ,KAAK,UAAU,EAAE,OAAO,CAAC,KAAK,SAAS;AACrE,QAAI,IAAI,SAAS,WAAW,IAAI,SAAS,SAAS;AAChD,UAAI,KAAK,IAAI,IAAI,KAAK;AAAA,IACxB;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,QAAM,QAAQ,KAAK,aAAa,OAAO;AACvC,QAAM,UAAU,KAAK,aAAa,kBAAkB;AACpD,MAAI,OAAO,UAAU;AACnB,QAAI,OAAO;AACT,sBAAgB,iBAAiB,IAAI,GAAG,OAAO,OAAO,kBAAkB,SAAS,EAAE,OAAO,WAAW,aAAa,CAAC;AAAA,IACrH,OAAO;AACL,sBAAgB,aAAa,IAAI;AACjC,sBAAgB,WAAW,IAAI;AAAA,IACjC;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,YAAY;AACnB,SAAO;AAAA,IACL,UAAU;AAAA,IACV,OAAO;AAAA,IACP,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,MAAM;AAAA,MACJ,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,MAAM,CAAC;AAAA,IACT;AAAA,IACA,QAAQ;AAAA,IACR,OAAO;AAAA,MACL,SAAS,CAAC;AAAA,MACV,QAAQ,CAAC;AAAA,MACT,YAAY,CAAC;AAAA,IACf;AAAA,EACF;AACF;AACA,SAAS,UAAU,MAAM;AACvB,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAAA,IAC/E,aAAa;AAAA,EACf;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,MAAM;AAAA,EACR,IAAI,YAAY,IAAI;AACpB,QAAM,kBAAkB,iBAAiB,IAAI;AAC7C,QAAM,aAAa,WAAW,uBAAuB,CAAC,GAAG,IAAI;AAC7D,MAAI,cAAc,OAAO,cAAc,YAAY,IAAI,IAAI,CAAC;AAC5D,SAAO,eAAe;AAAA,IACpB;AAAA,IACA,OAAO,KAAK,aAAa,OAAO;AAAA,IAChC,SAAS,KAAK,aAAa,kBAAkB;AAAA,IAC7C;AAAA,IACA,WAAW;AAAA,IACX,MAAM;AAAA,MACJ,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,MAAM,CAAC;AAAA,IACT;AAAA,IACA,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,OAAO;AAAA,MACL,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,YAAY;AAAA,IACd;AAAA,EACF,GAAG,UAAU;AACf;AAEA,IAAM;AAAA,EACJ,QAAQ;AACV,IAAI;AACJ,SAAS,iBAAiB,MAAM;AAC9B,QAAM,WAAW,OAAO,mBAAmB,SAAS,UAAU,MAAM;AAAA,IAClE,aAAa;AAAA,EACf,CAAC,IAAI,UAAU,IAAI;AACnB,MAAI,CAAC,SAAS,MAAM,QAAQ,QAAQ,qBAAqB,GAAG;AAC1D,WAAO,aAAa,sBAAsB,MAAM,QAAQ;AAAA,EAC1D,OAAO;AACL,WAAO,aAAa,kCAAkC,MAAM,QAAQ;AAAA,EACtE;AACF;AACA,SAAS,mBAAmB;AAC1B,SAAO,CAAC,GAAG,IAAI,GAAG,EAAE;AACtB;AACA,SAAS,OAAO,MAAM;AACpB,MAAI,WAAW,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACnF,MAAI,CAAC,OAAQ,QAAO,QAAQ,QAAQ;AACpC,QAAM,gBAAgB,SAAS,gBAAgB;AAC/C,QAAM,SAAS,YAAU,cAAc,IAAI,GAAG,OAAO,6BAA6B,GAAG,EAAE,OAAO,MAAM,CAAC;AACrG,QAAM,YAAY,YAAU,cAAc,OAAO,GAAG,OAAO,6BAA6B,GAAG,EAAE,OAAO,MAAM,CAAC;AAC3G,QAAM,WAAW,OAAO,eAAe,iBAAiB,IAAI,EAAE,OAAO,OAAO,KAAK,QAAQ,CAAC;AAC1F,MAAI,CAAC,SAAS,SAAS,IAAI,GAAG;AAC5B,aAAS,KAAK,IAAI;AAAA,EACpB;AACA,QAAM,mBAAmB,CAAC,IAAI,OAAO,uBAAuB,QAAQ,EAAE,OAAO,eAAe,IAAI,CAAC,EAAE,OAAO,SAAS,IAAI,UAAQ,IAAI,OAAO,MAAM,QAAQ,EAAE,OAAO,eAAe,IAAI,CAAC,CAAC,EAAE,KAAK,IAAI;AACjM,MAAI,iBAAiB,WAAW,GAAG;AACjC,WAAO,QAAQ,QAAQ;AAAA,EACzB;AACA,MAAI,aAAa,CAAC;AAClB,MAAI;AACF,iBAAa,QAAQ,KAAK,iBAAiB,gBAAgB,CAAC;AAAA,EAC9D,SAAS,MAAM;AAAA,EAEf;AACA,MAAI,WAAW,SAAS,GAAG;AACzB,WAAO,SAAS;AAChB,cAAU,UAAU;AAAA,EACtB,OAAO;AACL,WAAO,QAAQ,QAAQ;AAAA,EACzB;AACA,QAAM,OAAO,KAAK,MAAM,QAAQ;AAChC,QAAM,YAAY,WAAW,OAAO,CAAC,KAAK,SAAS;AACjD,QAAI;AACF,YAAM,WAAW,iBAAiB,IAAI;AACtC,UAAI,UAAU;AACZ,YAAI,KAAK,QAAQ;AAAA,MACnB;AAAA,IACF,SAAS,MAAM;AACb,UAAI,CAAC,YAAY;AACf,YAAI,KAAK,SAAS,eAAe;AAC/B,kBAAQ,MAAM,IAAI;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,YAAQ,IAAI,SAAS,EAAE,KAAK,uBAAqB;AAC/C,cAAQ,mBAAmB,MAAM;AAC/B,eAAO,QAAQ;AACf,eAAO,UAAU;AACjB,kBAAU,SAAS;AACnB,YAAI,OAAO,aAAa,WAAY,UAAS;AAC7C,aAAK;AACL,gBAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC,EAAE,MAAM,UAAQ;AACf,WAAK;AACL,aAAO,IAAI;AAAA,IACb,CAAC;AAAA,EACH,CAAC;AACH;AACA,SAAS,OAAO,MAAM;AACpB,MAAI,WAAW,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACnF,mBAAiB,IAAI,EAAE,KAAK,cAAY;AACtC,QAAI,UAAU;AACZ,cAAQ,CAAC,QAAQ,GAAG,QAAQ;AAAA,IAC9B;AAAA,EACF,CAAC;AACH;AACA,SAAS,aAAa,MAAM;AAC1B,SAAO,SAAU,qBAAqB;AACpC,QAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,UAAM,kBAAkB,uBAAuB,CAAC,GAAG,OAAO,sBAAsB,mBAAmB,uBAAuB,CAAC,CAAC;AAC5H,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,QAAI,MAAM;AACR,cAAQ,QAAQ,CAAC,GAAG,OAAO,OAAO,mBAAmB,QAAQ,CAAC,CAAC;AAAA,IACjE;AACA,WAAO,KAAK,gBAAgB,eAAe,eAAe,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG;AAAA,MACzE;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AACF;AACA,IAAM,SAAS,SAAU,gBAAgB;AACvC,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,OAAO;AAAA,IACP,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,UAAU,CAAC;AAAA,IACX,aAAa,CAAC;AAAA,IACd,QAAAA,UAAS,CAAC;AAAA,EACZ,IAAI;AACJ,MAAI,CAAC,eAAgB;AACrB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,MAAAI;AAAA,EACF,IAAI;AACJ,SAAO,YAAY,eAAe;AAAA,IAChC,MAAM;AAAA,EACR,GAAG,cAAc,GAAG,MAAM;AACxB,cAAU,4BAA4B;AAAA,MACpC;AAAA,MACA;AAAA,IACF,CAAC;AACD,QAAI,OAAO,UAAU;AACnB,UAAI,OAAO;AACT,mBAAW,iBAAiB,IAAI,GAAG,OAAO,OAAO,kBAAkB,SAAS,EAAE,OAAO,WAAW,aAAa,CAAC;AAAA,MAChH,OAAO;AACL,mBAAW,aAAa,IAAI;AAC5B,mBAAW,WAAW,IAAI;AAAA,MAC5B;AAAA,IACF;AACA,WAAO,sBAAsB;AAAA,MAC3B,OAAO;AAAA,QACL,MAAM,YAAYA,KAAI;AAAA,QACtB,MAAM,OAAO,YAAY,KAAK,IAAI,IAAI;AAAA,UACpC,OAAO;AAAA,UACP,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,MAAM,CAAC;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW,eAAe,eAAe,CAAC,GAAG,oBAAoB,GAAG,SAAS;AAAA,MAC7E;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAO;AAAA,QACL;AAAA,QACA,QAAAJ;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AACA,IAAI,kBAAkB;AAAA,EACpB,SAAS;AACP,WAAO;AAAA,MACL,MAAM,aAAa,MAAM;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,QAAQ;AACN,WAAO;AAAA,MACL,0BAA0B,aAAa;AACrC,oBAAY,eAAe;AAC3B,oBAAY,eAAe;AAC3B,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS,cAAc;AACrB,iBAAa,QAAQ,SAAU,QAAQ;AACrC,YAAM;AAAA,QACJ,OAAO;AAAA,QACP,WAAW,MAAM;AAAA,QAAC;AAAA,MACpB,IAAI;AACJ,aAAO,OAAO,MAAM,QAAQ;AAAA,IAC9B;AACA,iBAAa,iCAAiC,SAAU,MAAM,UAAU;AACtE,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,gBAAQ,IAAI,CAAC,SAAS,UAAU,MAAM,GAAG,KAAK,WAAW,SAAS,KAAK,UAAU,KAAK,MAAM,IAAI,QAAQ,QAAQ;AAAA,UAC9G,OAAO;AAAA,UACP,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,MAAM,CAAC;AAAA,QACT,CAAC,CAAC,CAAC,EAAE,KAAK,UAAQ;AAChB,cAAI,CAAC,MAAMO,KAAI,IAAI;AACnB,kBAAQ,CAAC,MAAM,sBAAsB;AAAA,YACnC,OAAO;AAAA,cACL;AAAA,cACA,MAAAA;AAAA,YACF;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,WAAW;AAAA,UACb,CAAC,CAAC,CAAC;AAAA,QACL,CAAC,EAAE,MAAM,MAAM;AAAA,MACjB,CAAC;AAAA,IACH;AACA,iBAAa,uBAAuB,SAAU,OAAO;AACnD,UAAI;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,QAAAP;AAAA,MACF,IAAI;AACJ,YAAM,cAAc,WAAWA,OAAM;AACrC,UAAI,YAAY,SAAS,GAAG;AAC1B,mBAAW,OAAO,IAAI;AAAA,MACxB;AACA,UAAI;AACJ,UAAI,sBAAsB,SAAS,GAAG;AACpC,oBAAY,aAAa,qCAAqC;AAAA,UAC5D;AAAA,UACA;AAAA,UACA,gBAAgB,KAAK;AAAA,UACrB,WAAW,KAAK;AAAA,QAClB,CAAC;AAAA,MACH;AACA,eAAS,KAAK,aAAa,KAAK,IAAI;AACpC,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,SAAS;AAAA,EACX,SAAS;AACP,WAAO;AAAA,MACL,MAAM,WAAW;AACf,YAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,cAAM;AAAA,UACJ,UAAU,CAAC;AAAA,QACb,IAAI;AACJ,eAAO,YAAY;AAAA,UACjB,MAAM;AAAA,QACR,GAAG,MAAM;AACP,oBAAU,4BAA4B;AAAA,YACpC;AAAA,YACA;AAAA,UACF,CAAC;AACD,cAAI,WAAW,CAAC;AAChB,oBAAU,UAAQ;AAChB,kBAAM,QAAQ,IAAI,IAAI,KAAK,IAAI,OAAK;AAClC,yBAAW,SAAS,OAAO,EAAE,QAAQ;AAAA,YACvC,CAAC,IAAI,WAAW,SAAS,OAAO,KAAK,QAAQ;AAAA,UAC/C,CAAC;AACD,iBAAO,CAAC;AAAA,YACN,KAAK;AAAA,YACL,YAAY;AAAA,cACV,OAAO,CAAC,GAAG,OAAO,OAAO,WAAW,SAAS,GAAG,GAAG,OAAO,EAAE,KAAK,GAAG;AAAA,YACtE;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,gBAAgB;AAAA,EAClB,SAAS;AACP,WAAO;AAAA,MACL,QAAQ,SAAS;AACf,YAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,cAAM;AAAA,UACJ,QAAQ;AAAA,UACR,UAAU,CAAC;AAAA,UACX,aAAa,CAAC;AAAA,UACd,QAAAA,UAAS,CAAC;AAAA,QACZ,IAAI;AACJ,eAAO,YAAY;AAAA,UACjB,MAAM;AAAA,UACN;AAAA,QACF,GAAG,MAAM;AACP,oBAAU,4BAA4B;AAAA,YACpC;AAAA,YACA;AAAA,UACF,CAAC;AACD,iBAAO,0BAA0B;AAAA,YAC/B,SAAS,QAAQ,SAAS;AAAA,YAC1B;AAAA,YACA,OAAO;AAAA,cACL;AAAA,cACA,QAAAA;AAAA,cACA,SAAS,CAAC,GAAG,OAAO,OAAO,WAAW,iBAAiB,GAAG,GAAG,OAAO;AAAA,YACtE;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,aAAa;AAAA,EACf,SAAS;AACP,WAAO;AAAA,MACL,KAAK,SAAS;AACZ,YAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,cAAM;AAAA,UACJ,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR,UAAU,CAAC;AAAA,UACX,aAAa,CAAC;AAAA,UACd,QAAAA,UAAS,CAAC;AAAA,QACZ,IAAI;AACJ,eAAO,YAAY;AAAA,UACjB,MAAM;AAAA,UACN;AAAA,QACF,GAAG,MAAM;AACP,oBAAU,4BAA4B;AAAA,YACpC;AAAA,YACA;AAAA,UACF,CAAC;AACD,iBAAO,uBAAuB;AAAA,YAC5B;AAAA,YACA,WAAW,eAAe,eAAe,CAAC,GAAG,oBAAoB,GAAG,SAAS;AAAA,YAC7E;AAAA,YACA,OAAO;AAAA,cACL;AAAA,cACA,QAAAA;AAAA,cACA,SAAS,CAAC,GAAG,OAAO,OAAO,WAAW,cAAc,GAAG,GAAG,OAAO;AAAA,YACnE;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS,cAAc;AACrB,iBAAa,qBAAqB,SAAU,MAAM,UAAU;AAC1D,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,QAAQ;AACZ,UAAI,SAAS;AACb,UAAI,OAAO;AACT,cAAM,mBAAmB,SAAS,iBAAiB,IAAI,EAAE,UAAU,EAAE;AACrE,cAAM,qBAAqB,KAAK,sBAAsB;AACtD,gBAAQ,mBAAmB,QAAQ;AACnC,iBAAS,mBAAmB,SAAS;AAAA,MACvC;AACA,UAAI,OAAO,YAAY,CAAC,OAAO;AAC7B,cAAM,WAAW,aAAa,IAAI;AAAA,MACpC;AACA,aAAO,QAAQ,QAAQ,CAAC,MAAM,uBAAuB;AAAA,QACnD,SAAS,KAAK;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,WAAW;AAAA,MACb,CAAC,CAAC,CAAC;AAAA,IACL;AAAA,EACF;AACF;AAEA,IAAM,wBAAwB,IAAI,OAAO,KAAU,IAAI;AACvD,IAAM,0BAA0B,CAAC,SAAS,OAAO;AACjD,IAAM,gCAAgC,eAAe,eAAe,eAAe,eAAe,CAAC,GAAG;AAAA,EACpG,aAAa;AAAA,IACX,QAAQ;AAAA,IACR,KAAK;AAAA,EACP;AACF,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChB,IAAM,+BAA+B,OAAO,KAAK,6BAA6B,EAAE,OAAO,CAAC,KAAK,QAAQ;AACnG,MAAI,IAAI,YAAY,CAAC,IAAI,8BAA8B,GAAG;AAC1D,SAAO;AACT,GAAG,CAAC,CAAC;AACL,IAAM,8BAA8B,OAAO,KAAK,4BAA4B,EAAE,OAAO,CAAC,KAAK,eAAe;AACxG,QAAM,UAAU,6BAA6B,UAAU;AACvD,MAAI,UAAU,IAAI,QAAQ,GAAG,KAAK,CAAC,GAAG,OAAO,QAAQ,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC;AACnE,SAAO;AACT,GAAG,CAAC,CAAC;AACL,SAAS,oBAAoB,SAAS;AACpC,QAAM,UAAU,QAAQ,QAAQ,uBAAuB,EAAE;AACzD,QAAM,YAAY,YAAY,SAAS,CAAC;AACxC,QAAM,eAAe,aAAa,wBAAwB,CAAC,KAAK,aAAa,wBAAwB,CAAC;AACtG,QAAM,YAAY,QAAQ,WAAW,IAAI,QAAQ,CAAC,MAAM,QAAQ,CAAC,IAAI;AACrE,SAAO;AAAA,IACL,OAAO,YAAY,MAAM,QAAQ,CAAC,CAAC,IAAI,MAAM,OAAO;AAAA,IACpD,aAAa,gBAAgB;AAAA,EAC/B;AACF;AACA,SAAS,UAAU,YAAY,YAAY;AACzC,QAAM,sBAAsB,WAAW,QAAQ,gBAAgB,EAAE,EAAE,YAAY;AAC/E,QAAM,oBAAoB,SAAS,UAAU;AAC7C,QAAM,sBAAsB,MAAM,iBAAiB,IAAI,WAAW;AAClE,UAAQ,6BAA6B,mBAAmB,KAAK,CAAC,GAAG,mBAAmB,KAAK,4BAA4B,mBAAmB;AAC1I;AACA,SAAS,mBAAmB,MAAM,UAAU;AAC1C,QAAM,mBAAmB,GAAG,OAAO,8BAA8B,EAAE,OAAO,SAAS,QAAQ,KAAK,GAAG,CAAC;AACpG,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,QAAI,KAAK,aAAa,gBAAgB,MAAM,MAAM;AAEhD,aAAO,QAAQ;AAAA,IACjB;AACA,UAAM,WAAW,QAAQ,KAAK,QAAQ;AACtC,UAAM,gCAAgC,SAAS,OAAO,UAAQ,KAAK,aAAa,sBAAsB,MAAM,QAAQ,EAAE,CAAC;AACvH,UAAMA,UAAS,OAAO,iBAAiB,MAAM,QAAQ;AACrD,UAAM,aAAaA,QAAO,iBAAiB,aAAa;AACxD,UAAM,kBAAkB,WAAW,MAAM,mBAAmB;AAC5D,UAAM,aAAaA,QAAO,iBAAiB,aAAa;AACxD,UAAM,UAAUA,QAAO,iBAAiB,SAAS;AACjD,QAAI,iCAAiC,CAAC,iBAAiB;AAIrD,WAAK,YAAY,6BAA6B;AAC9C,aAAO,QAAQ;AAAA,IACjB,WAAW,mBAAmB,YAAY,UAAU,YAAY,IAAI;AAClE,YAAMQ,WAAUR,QAAO,iBAAiB,SAAS;AACjD,UAAI,SAAS,UAAU,YAAY,UAAU;AAC7C,YAAM;AAAA,QACJ,OAAO;AAAA,QACP;AAAA,MACF,IAAI,oBAAoBQ,QAAO;AAC/B,YAAM,OAAO,gBAAgB,CAAC,EAAE,WAAW,aAAa;AACxD,UAAI,WAAW,UAAU,QAAQ,QAAQ;AACzC,UAAI,iBAAiB;AACrB,UAAI,MAAM;AACR,cAAM,YAAY,aAAa,QAAQ;AACvC,YAAI,UAAU,YAAY,UAAU,QAAQ;AAC1C,qBAAW,UAAU;AACrB,mBAAS,UAAU;AAAA,QACrB;AAAA,MACF;AAIA,UAAI,YAAY,CAAC,gBAAgB,CAAC,iCAAiC,8BAA8B,aAAa,WAAW,MAAM,UAAU,8BAA8B,aAAa,SAAS,MAAM,iBAAiB;AAClN,aAAK,aAAa,kBAAkB,cAAc;AAClD,YAAI,+BAA+B;AAEjC,eAAK,YAAY,6BAA6B;AAAA,QAChD;AACA,cAAM,OAAO,UAAU;AACvB,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,cAAM,WAAW,sBAAsB,IAAI;AAC3C,iBAAS,UAAU,MAAM,EAAE,KAAK,UAAQ;AACtC,gBAAM,WAAW,sBAAsB,eAAe,eAAe,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;AAAA,YAClF,OAAO;AAAA,cACL;AAAA,cACA,MAAM,mBAAmB;AAAA,YAC3B;AAAA,YACA;AAAA,YACA,UAAU;AAAA,YACV;AAAA,YACA,WAAW;AAAA,UACb,CAAC,CAAC;AACF,gBAAM,UAAU,SAAS,gBAAgB,8BAA8B,KAAK;AAC5E,cAAI,aAAa,YAAY;AAC3B,iBAAK,aAAa,SAAS,KAAK,UAAU;AAAA,UAC5C,OAAO;AACL,iBAAK,YAAY,OAAO;AAAA,UAC1B;AACA,kBAAQ,YAAY,SAAS,IAAI,UAAQ,OAAO,IAAI,CAAC,EAAE,KAAK,IAAI;AAChE,eAAK,gBAAgB,gBAAgB;AACrC,kBAAQ;AAAA,QACV,CAAC,EAAE,MAAM,MAAM;AAAA,MACjB,OAAO;AACL,gBAAQ;AAAA,MACV;AAAA,IACF,OAAO;AACL,cAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACH;AACA,SAAS,QAAQ,MAAM;AACrB,SAAO,QAAQ,IAAI,CAAC,mBAAmB,MAAM,UAAU,GAAG,mBAAmB,MAAM,SAAS,CAAC,CAAC;AAChG;AACA,SAAS,YAAY,MAAM;AACzB,SAAO,KAAK,eAAe,SAAS,QAAQ,CAAC,CAAC,oCAAoC,QAAQ,KAAK,QAAQ,YAAY,CAAC,KAAK,CAAC,KAAK,aAAa,sBAAsB,MAAM,CAAC,KAAK,cAAc,KAAK,WAAW,YAAY;AAC1N;AACA,SAAS,qBAAqB,MAAM;AAClC,MAAI,CAAC,OAAQ;AACb,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAM,aAAa,QAAQ,KAAK,iBAAiB,GAAG,CAAC,EAAE,OAAO,WAAW,EAAE,IAAI,OAAO;AACtF,UAAMC,OAAM,KAAK,MAAM,sBAAsB;AAC7C,uBAAmB;AACnB,YAAQ,IAAI,UAAU,EAAE,KAAK,MAAM;AACjC,MAAAA,KAAI;AACJ,wBAAkB;AAClB,cAAQ;AAAA,IACV,CAAC,EAAE,MAAM,MAAM;AACb,MAAAA,KAAI;AACJ,wBAAkB;AAClB,aAAO;AAAA,IACT,CAAC;AAAA,EACH,CAAC;AACH;AACA,IAAI,iBAAiB;AAAA,EACnB,QAAQ;AACN,WAAO;AAAA,MACL,0BAA0B,aAAa;AACrC,oBAAY,yBAAyB;AACrC,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAASC,YAAW;AAClB,IAAAA,WAAU,qBAAqB,SAAU,QAAQ;AAC/C,YAAM;AAAA,QACJ,OAAO;AAAA,MACT,IAAI;AACJ,UAAI,OAAO,sBAAsB;AAC/B,6BAAqB,IAAI;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,aAAa;AACjB,IAAI,qBAAqB;AAAA,EACvB,SAAS;AACP,WAAO;AAAA,MACL,KAAK;AAAA,QACH,UAAU;AACR,6BAAmB;AACnB,uBAAa;AAAA,QACf;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ;AACN,WAAO;AAAA,MACL,YAAY;AACV,gBAAQ,WAAW,6BAA6B,CAAC,CAAC,CAAC;AAAA,MACrD;AAAA,MACA,SAAS;AACP,mBAAW;AAAA,MACb;AAAA,MACA,MAAM,QAAQ;AACZ,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,YAAI,YAAY;AACd,4BAAkB;AAAA,QACpB,OAAO;AACL,kBAAQ,WAAW,6BAA6B;AAAA,YAC9C;AAAA,UACF,CAAC,CAAC;AAAA,QACJ;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,uBAAuB,qBAAmB;AAC9C,MAAI,YAAY;AAAA,IACd,MAAM;AAAA,IACN,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AACA,SAAO,gBAAgB,YAAY,EAAE,MAAM,GAAG,EAAE,OAAO,CAAC,KAAK,MAAM;AACjE,UAAM,QAAQ,EAAE,YAAY,EAAE,MAAM,GAAG;AACvC,UAAM,QAAQ,MAAM,CAAC;AACrB,QAAI,OAAO,MAAM,MAAM,CAAC,EAAE,KAAK,GAAG;AAClC,QAAI,SAAS,SAAS,KAAK;AACzB,UAAI,QAAQ;AACZ,aAAO;AAAA,IACT;AACA,QAAI,SAAS,SAAS,KAAK;AACzB,UAAI,QAAQ;AACZ,aAAO;AAAA,IACT;AACA,WAAO,WAAW,IAAI;AACtB,QAAI,MAAM,IAAI,GAAG;AACf,aAAO;AAAA,IACT;AACA,YAAQ,OAAO;AAAA,MACb,KAAK;AACH,YAAI,OAAO,IAAI,OAAO;AACtB;AAAA,MACF,KAAK;AACH,YAAI,OAAO,IAAI,OAAO;AACtB;AAAA,MACF,KAAK;AACH,YAAI,IAAI,IAAI,IAAI;AAChB;AAAA,MACF,KAAK;AACH,YAAI,IAAI,IAAI,IAAI;AAChB;AAAA,MACF,KAAK;AACH,YAAI,IAAI,IAAI,IAAI;AAChB;AAAA,MACF,KAAK;AACH,YAAI,IAAI,IAAI,IAAI;AAChB;AAAA,MACF,KAAK;AACH,YAAI,SAAS,IAAI,SAAS;AAC1B;AAAA,IACJ;AACA,WAAO;AAAA,EACT,GAAG,SAAS;AACd;AACA,IAAI,kBAAkB;AAAA,EACpB,SAAS;AACP,WAAO;AAAA,MACL,OAAO;AAAA,QACL,WAAW,qBAAmB;AAC5B,iBAAO,qBAAqB,eAAe;AAAA,QAC7C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ;AACN,WAAO;AAAA,MACL,oBAAoB,aAAa,MAAM;AACrC,cAAM,kBAAkB,KAAK,aAAa,mBAAmB;AAC7D,YAAI,iBAAiB;AACnB,sBAAY,YAAY,qBAAqB,eAAe;AAAA,QAC9D;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAASA,YAAW;AAClB,IAAAA,WAAU,oCAAoC,SAAU,MAAM;AAC5D,UAAI;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,QAAQ;AAAA,QACZ,WAAW,aAAa,OAAO,iBAAiB,GAAG,OAAO;AAAA,MAC5D;AACA,YAAM,iBAAiB,aAAa,OAAO,UAAU,IAAI,IAAI,IAAI,EAAE,OAAO,UAAU,IAAI,IAAI,IAAI;AAChG,YAAM,aAAa,SAAS,OAAO,UAAU,OAAO,MAAM,UAAU,QAAQ,KAAK,IAAI,IAAI,EAAE,OAAO,UAAU,OAAO,MAAM,UAAU,QAAQ,KAAK,IAAI,IAAI;AACxJ,YAAM,cAAc,UAAU,OAAO,UAAU,QAAQ,OAAO;AAC9D,YAAM,QAAQ;AAAA,QACZ,WAAW,GAAG,OAAO,gBAAgB,GAAG,EAAE,OAAO,YAAY,GAAG,EAAE,OAAO,WAAW;AAAA,MACtF;AACA,YAAM,OAAO;AAAA,QACX,WAAW,aAAa,OAAO,YAAY,IAAI,IAAI,QAAQ;AAAA,MAC7D;AACA,YAAM,aAAa;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,aAAO;AAAA,QACL,KAAK;AAAA,QACL,YAAY,eAAe,CAAC,GAAG,WAAW,KAAK;AAAA,QAC/C,UAAU,CAAC;AAAA,UACT,KAAK;AAAA,UACL,YAAY,eAAe,CAAC,GAAG,WAAW,KAAK;AAAA,UAC/C,UAAU,CAAC;AAAA,YACT,KAAK,KAAK,KAAK;AAAA,YACf,UAAU,KAAK,KAAK;AAAA,YACpB,YAAY,eAAe,eAAe,CAAC,GAAG,KAAK,KAAK,UAAU,GAAG,WAAW,IAAI;AAAA,UACtF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,YAAY;AAAA,EAChB,GAAG;AAAA,EACH,GAAG;AAAA,EACH,OAAO;AAAA,EACP,QAAQ;AACV;AACA,SAAS,UAAU,UAAU;AAC3B,MAAI,QAAQ,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAChF,MAAI,SAAS,eAAe,SAAS,WAAW,QAAQ,QAAQ;AAC9D,aAAS,WAAW,OAAO;AAAA,EAC7B;AACA,SAAO;AACT;AACA,SAAS,QAAQ,UAAU;AACzB,MAAI,SAAS,QAAQ,KAAK;AACxB,WAAO,SAAS;AAAA,EAClB,OAAO;AACL,WAAO,CAAC,QAAQ;AAAA,EAClB;AACF;AACA,IAAI,QAAQ;AAAA,EACV,QAAQ;AACN,WAAO;AAAA,MACL,oBAAoB,aAAa,MAAM;AACrC,cAAM,WAAW,KAAK,aAAa,cAAc;AACjD,cAAM,OAAO,CAAC,WAAW,mBAAmB,IAAI,iBAAiB,SAAS,MAAM,GAAG,EAAE,IAAI,OAAK,EAAE,KAAK,CAAC,CAAC;AACvG,YAAI,CAAC,KAAK,QAAQ;AAChB,eAAK,SAAS,uBAAuB;AAAA,QACvC;AACA,oBAAY,OAAO;AACnB,oBAAY,SAAS,KAAK,aAAa,iBAAiB;AACxD,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAASA,YAAW;AAClB,IAAAA,WAAU,uBAAuB,SAAU,MAAM;AAC/C,UAAI;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,QACR;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,IAAI;AACJ,YAAM;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,IAAI;AACJ,YAAM,QAAQ,gBAAgB;AAAA,QAC5B;AAAA,QACA,gBAAgB;AAAA,QAChB,WAAW;AAAA,MACb,CAAC;AACD,YAAM,WAAW;AAAA,QACf,KAAK;AAAA,QACL,YAAY,eAAe,eAAe,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG;AAAA,UAC5D,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AACA,YAAM,8BAA8B,SAAS,WAAW;AAAA,QACtD,UAAU,SAAS,SAAS,IAAI,SAAS;AAAA,MAC3C,IAAI,CAAC;AACL,YAAM,iBAAiB;AAAA,QACrB,KAAK;AAAA,QACL,YAAY,eAAe,CAAC,GAAG,MAAM,KAAK;AAAA,QAC1C,UAAU,CAAC,UAAU,eAAe;AAAA,UAClC,KAAK,SAAS;AAAA,UACd,YAAY,eAAe,eAAe,CAAC,GAAG,SAAS,UAAU,GAAG,MAAM,IAAI;AAAA,QAChF,GAAG,2BAA2B,CAAC,CAAC;AAAA,MAClC;AACA,YAAM,iBAAiB;AAAA,QACrB,KAAK;AAAA,QACL,YAAY,eAAe,CAAC,GAAG,MAAM,KAAK;AAAA,QAC1C,UAAU,CAAC,cAAc;AAAA,MAC3B;AACA,YAAM,SAAS,QAAQ,OAAO,kBAAkB,aAAa,CAAC;AAC9D,YAAM,SAAS,QAAQ,OAAO,kBAAkB,aAAa,CAAC;AAC9D,YAAM,UAAU;AAAA,QACd,KAAK;AAAA,QACL,YAAY,eAAe,eAAe,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG;AAAA,UAC5D,IAAI;AAAA,UACJ,WAAW;AAAA,UACX,kBAAkB;AAAA,QACpB,CAAC;AAAA,QACD,UAAU,CAAC,UAAU,cAAc;AAAA,MACrC;AACA,YAAM,OAAO;AAAA,QACX,KAAK;AAAA,QACL,UAAU,CAAC;AAAA,UACT,KAAK;AAAA,UACL,YAAY;AAAA,YACV,IAAI;AAAA,UACN;AAAA,UACA,UAAU,QAAQ,QAAQ;AAAA,QAC5B,GAAG,OAAO;AAAA,MACZ;AACA,eAAS,KAAK,MAAM;AAAA,QAClB,KAAK;AAAA,QACL,YAAY,eAAe;AAAA,UACzB,MAAM;AAAA,UACN,aAAa,QAAQ,OAAO,QAAQ,GAAG;AAAA,UACvC,MAAM,QAAQ,OAAO,QAAQ,GAAG;AAAA,QAClC,GAAG,SAAS;AAAA,MACd,CAAC;AACD,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,uBAAuB;AAAA,EACzB,SAASA,YAAW;AAClB,QAAI,eAAe;AACnB,QAAI,OAAO,YAAY;AACrB,qBAAe,OAAO,WAAW,kCAAkC,EAAE;AAAA,IACvE;AACA,IAAAA,WAAU,sBAAsB,WAAY;AAC1C,YAAM,YAAY,CAAC;AACnB,YAAM,OAAO;AAAA,QACX,MAAM;AAAA,MACR;AACA,YAAM,iBAAiB;AAAA,QACrB,eAAe;AAAA,QACf,aAAa;AAAA,QACb,KAAK;AAAA,MACP;AAGA,gBAAU,KAAK;AAAA,QACb,KAAK;AAAA,QACL,YAAY,eAAe,eAAe,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;AAAA,UACvD,GAAG;AAAA,QACL,CAAC;AAAA,MACH,CAAC;AACD,YAAM,kBAAkB,eAAe,eAAe,CAAC,GAAG,cAAc,GAAG,CAAC,GAAG;AAAA,QAC7E,eAAe;AAAA,MACjB,CAAC;AACD,YAAM,MAAM;AAAA,QACV,KAAK;AAAA,QACL,YAAY,eAAe,eAAe,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;AAAA,UACvD,IAAI;AAAA,UACJ,IAAI;AAAA,UACJ,GAAG;AAAA,QACL,CAAC;AAAA,QACD,UAAU,CAAC;AAAA,MACb;AACA,UAAI,CAAC,cAAc;AACjB,YAAI,SAAS,KAAK;AAAA,UAChB,KAAK;AAAA,UACL,YAAY,eAAe,eAAe,CAAC,GAAG,cAAc,GAAG,CAAC,GAAG;AAAA,YACjE,eAAe;AAAA,YACf,QAAQ;AAAA,UACV,CAAC;AAAA,QACH,GAAG;AAAA,UACD,KAAK;AAAA,UACL,YAAY,eAAe,eAAe,CAAC,GAAG,eAAe,GAAG,CAAC,GAAG;AAAA,YAClE,QAAQ;AAAA,UACV,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,gBAAU,KAAK,GAAG;AAClB,gBAAU,KAAK;AAAA,QACb,KAAK;AAAA,QACL,YAAY,eAAe,eAAe,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;AAAA,UACvD,SAAS;AAAA,UACT,GAAG;AAAA,QACL,CAAC;AAAA,QACD,UAAU,eAAe,CAAC,IAAI,CAAC;AAAA,UAC7B,KAAK;AAAA,UACL,YAAY,eAAe,eAAe,CAAC,GAAG,eAAe,GAAG,CAAC,GAAG;AAAA,YAClE,QAAQ;AAAA,UACV,CAAC;AAAA,QACH,CAAC;AAAA,MACH,CAAC;AACD,UAAI,CAAC,cAAc;AAEjB,kBAAU,KAAK;AAAA,UACb,KAAK;AAAA,UACL,YAAY,eAAe,eAAe,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;AAAA,YACvD,SAAS;AAAA,YACT,GAAG;AAAA,UACL,CAAC;AAAA,UACD,UAAU,CAAC;AAAA,YACT,KAAK;AAAA,YACL,YAAY,eAAe,eAAe,CAAC,GAAG,eAAe,GAAG,CAAC,GAAG;AAAA,cAClE,QAAQ;AAAA,YACV,CAAC;AAAA,UACH,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,aAAO;AAAA,QACL,KAAK;AAAA,QACL,YAAY;AAAA,UACV,SAAS;AAAA,QACX;AAAA,QACA,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,aAAa;AAAA,EACf,QAAQ;AACN,WAAO;AAAA,MACL,oBAAoB,aAAa,MAAM;AACrC,cAAM,aAAa,KAAK,aAAa,gBAAgB;AACrD,cAAM,SAAS,eAAe,OAAO,QAAQ,eAAe,KAAK,OAAO;AACxE,oBAAY,QAAQ,IAAI;AACxB,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,UAAU,CAAC,WAAW,iBAAiB,QAAQ,eAAe,YAAY,gBAAgB,oBAAoB,iBAAiB,OAAO,sBAAsB,UAAU;AAE1K,gBAAgB,SAAS;AAAA,EACvB,WAAW;AACb,CAAC;AACD,IAAM,WAAW,IAAI;AACrB,IAAM,WAAW,IAAI;AACrB,IAAM,YAAY,IAAI;AACtB,IAAM,QAAQ,IAAI;AAClB,IAAM,UAAU,IAAI;AACpB,IAAM,uBAAuB,IAAI;AACjC,IAAM,WAAW,IAAI;AACrB,IAAM,OAAO,IAAI;AACjB,IAAM,QAAQ,IAAI;AAClB,IAAM,OAAO,IAAI;AACjB,IAAM,UAAU,IAAI;;;ACt8FpB,wBAAsB;AACtB,mBAAkB;AAElB,SAASC,SAAQ,QAAQ,gBAAgB;AACvC,MAAI,OAAO,OAAO,KAAK,MAAM;AAE7B,MAAI,OAAO,uBAAuB;AAChC,QAAI,UAAU,OAAO,sBAAsB,MAAM;AACjD,uBAAmB,UAAU,QAAQ,OAAO,SAAU,KAAK;AACzD,aAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,IACtD,CAAC,IAAI,KAAK,KAAK,MAAM,MAAM,OAAO;AAAA,EACpC;AAEA,SAAO;AACT;AAEA,SAASC,gBAAe,QAAQ;AAC9B,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,SAAS,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;AACpD,QAAI,IAAID,SAAQ,OAAO,MAAM,GAAG,IAAE,EAAE,QAAQ,SAAU,KAAK;AACzD,MAAAE,iBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,IAC1C,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC,IAAIF,SAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AACjK,aAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,IACjF,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AAEA,SAAS,QAAQ,KAAK;AACpB;AAEA,SAAO,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUG,MAAK;AAClG,WAAO,OAAOA;AAAA,EAChB,IAAI,SAAUA,MAAK;AACjB,WAAOA,QAAO,cAAc,OAAO,UAAUA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,OAAOA;AAAA,EAC1H,GAAG,QAAQ,GAAG;AAChB;AAEA,SAASD,iBAAgB,KAAK,KAAK,OAAO;AACxC,MAAI,OAAO,KAAK;AACd,WAAO,eAAe,KAAK,KAAK;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,OAAO;AACL,QAAI,GAAG,IAAI;AAAA,EACb;AAEA,SAAO;AACT;AAEA,SAAS,8BAA8B,QAAQ,UAAU;AACvD,MAAI,UAAU,KAAM,QAAO,CAAC;AAC5B,MAAI,SAAS,CAAC;AACd,MAAI,aAAa,OAAO,KAAK,MAAM;AACnC,MAAI,KAAK;AAET,OAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACtC,UAAM,WAAW,CAAC;AAClB,QAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAChC,WAAO,GAAG,IAAI,OAAO,GAAG;AAAA,EAC1B;AAEA,SAAO;AACT;AAEA,SAAS,yBAAyB,QAAQ,UAAU;AAClD,MAAI,UAAU,KAAM,QAAO,CAAC;AAE5B,MAAI,SAAS,8BAA8B,QAAQ,QAAQ;AAE3D,MAAI,KAAK;AAET,MAAI,OAAO,uBAAuB;AAChC,QAAI,mBAAmB,OAAO,sBAAsB,MAAM;AAE1D,SAAK,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAC5C,YAAM,iBAAiB,CAAC;AACxB,UAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAChC,UAAI,CAAC,OAAO,UAAU,qBAAqB,KAAK,QAAQ,GAAG,EAAG;AAC9D,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC1B;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,mBAAmB,KAAK;AAC/B,SAAO,mBAAmB,GAAG,KAAK,iBAAiB,GAAG,KAAK,4BAA4B,GAAG,KAAK,mBAAmB;AACpH;AAEA,SAAS,mBAAmB,KAAK;AAC/B,MAAI,MAAM,QAAQ,GAAG,EAAG,QAAO,kBAAkB,GAAG;AACtD;AAEA,SAAS,iBAAiB,MAAM;AAC9B,MAAI,OAAO,WAAW,eAAe,KAAK,OAAO,QAAQ,KAAK,QAAQ,KAAK,YAAY,KAAK,KAAM,QAAO,MAAM,KAAK,IAAI;AAC1H;AAEA,SAAS,4BAA4BE,IAAG,QAAQ;AAC9C,MAAI,CAACA,GAAG;AACR,MAAI,OAAOA,OAAM,SAAU,QAAO,kBAAkBA,IAAG,MAAM;AAC7D,MAAI,IAAI,OAAO,UAAU,SAAS,KAAKA,EAAC,EAAE,MAAM,GAAG,EAAE;AACrD,MAAI,MAAM,YAAYA,GAAE,YAAa,KAAIA,GAAE,YAAY;AACvD,MAAI,MAAM,SAAS,MAAM,MAAO,QAAO,MAAM,KAAKA,EAAC;AACnD,MAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC,EAAG,QAAO,kBAAkBA,IAAG,MAAM;AACjH;AAEA,SAAS,kBAAkB,KAAK,KAAK;AACnC,MAAI,OAAO,QAAQ,MAAM,IAAI,OAAQ,OAAM,IAAI;AAE/C,WAAS,IAAI,GAAG,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,IAAK,MAAK,CAAC,IAAI,IAAI,CAAC;AAEpE,SAAO;AACT;AAEA,SAAS,qBAAqB;AAC5B,QAAM,IAAI,UAAU,sIAAsI;AAC5J;AAGA,SAAS,UAAU,OAAO;AACxB,MAAI;AAEJ,MAAI,OAAO,MAAM,MACb,OAAO,MAAM,MACb,WAAW,MAAM,UACjB,SAAS,MAAM,QACf,QAAQ,MAAM,OACd,QAAQ,MAAM,OACd,OAAO,MAAM,MACb,YAAY,MAAM,WAClB,cAAc,MAAM,aACpB,QAAQ,MAAM,OACd,aAAa,MAAM,YACnB,UAAU,MAAM,SAChB,SAAS,MAAM,QACf,WAAW,MAAM,UACjB,OAAO,MAAM,MACb,OAAO,MAAM,MACb,WAAW,MAAM,UACjB,OAAO,MAAM;AAEjB,MAAI,WAAW,WAAW;AAAA,IACxB,WAAW;AAAA,IACX,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,cAAc;AAAA,IACd,aAAa;AAAA,IACb,SAAS;AAAA,IACT,WAAW,SAAS;AAAA,IACpB,sBAAsB,SAAS,gBAAgB,SAAS;AAAA,IACxD,oBAAoB,SAAS,cAAc,SAAS;AAAA,EACtD,GAAGF,iBAAgB,UAAU,MAAM,OAAO,IAAI,GAAG,OAAO,SAAS,eAAe,SAAS,IAAI,GAAGA,iBAAgB,UAAU,aAAa,OAAO,QAAQ,GAAG,OAAO,aAAa,eAAe,aAAa,QAAQ,aAAa,CAAC,GAAGA,iBAAgB,UAAU,WAAW,OAAO,IAAI,GAAG,OAAO,SAAS,eAAe,SAAS,IAAI,GAAGA,iBAAgB,UAAU,mBAAmB,MAAM,WAAW,GAAG;AAGrY,SAAO,OAAO,KAAK,OAAO,EAAE,IAAI,SAAU,KAAK;AAC7C,WAAO,QAAQ,GAAG,IAAI,MAAM;AAAA,EAC9B,CAAC,EAAE,OAAO,SAAU,KAAK;AACvB,WAAO;AAAA,EACT,CAAC;AACH;AAMA,SAAS,aAAa,KAAK;AACzB,QAAM,MAAM;AAEZ,SAAO,QAAQ;AACjB;AAEA,SAAS,SAAS,QAAQ;AACxB,MAAI,aAAa,MAAM,GAAG;AACxB,WAAO;AAAA,EACT;AAGA,WAAS,OAAO,QAAQ,iBAAiB,SAAU,OAAO,KAAK;AAC7D,WAAO,MAAM,IAAI,YAAY,IAAI;AAAA,EACnC,CAAC;AAED,SAAO,OAAO,OAAO,GAAG,CAAC,EAAE,YAAY,IAAI,OAAO,OAAO,CAAC;AAC5D;AAEA,IAAI,YAAY,CAAC,OAAO;AAExB,SAAS,WAAW,KAAK;AACvB,SAAO,IAAI,OAAO,CAAC,EAAE,YAAY,IAAI,IAAI,MAAM,CAAC;AAClD;AAEA,SAAS,cAAc,OAAO;AAC5B,SAAO,MAAM,MAAM,GAAG,EAAE,IAAI,SAAUG,IAAG;AACvC,WAAOA,GAAE,KAAK;AAAA,EAChB,CAAC,EAAE,OAAO,SAAUA,IAAG;AACrB,WAAOA;AAAA,EACT,CAAC,EAAE,OAAO,SAAU,KAAK,MAAM;AAC7B,QAAI,IAAI,KAAK,QAAQ,GAAG;AACxB,QAAI,OAAO,SAAS,KAAK,MAAM,GAAG,CAAC,CAAC;AACpC,QAAI,QAAQ,KAAK,MAAM,IAAI,CAAC,EAAE,KAAK;AACnC,SAAK,WAAW,QAAQ,IAAI,IAAI,WAAW,IAAI,CAAC,IAAI,QAAQ,IAAI,IAAI,IAAI;AACxE,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AAEA,SAAS,QAAQC,gBAAe,SAAS;AACvC,MAAI,aAAa,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAEtF,MAAI,OAAO,YAAY,UAAU;AAC/B,WAAO;AAAA,EACT;AAEA,MAAI,YAAY,QAAQ,YAAY,CAAC,GAAG,IAAI,SAAU,OAAO;AAC3D,WAAO,QAAQA,gBAAe,KAAK;AAAA,EACrC,CAAC;AAGD,MAAI,SAAS,OAAO,KAAK,QAAQ,cAAc,CAAC,CAAC,EAAE,OAAO,SAAU,KAAK,KAAK;AAC5E,QAAI,MAAM,QAAQ,WAAW,GAAG;AAEhC,YAAQ,KAAK;AAAA,MACX,KAAK;AACH,YAAI,MAAM,WAAW,IAAI;AACzB,eAAO,QAAQ,WAAW,OAAO;AACjC;AAAA,MAEF,KAAK;AACH,YAAI,MAAM,OAAO,IAAI,cAAc,GAAG;AACtC;AAAA,MAEF;AACE,YAAI,IAAI,QAAQ,OAAO,MAAM,KAAK,IAAI,QAAQ,OAAO,MAAM,GAAG;AAC5D,cAAI,MAAM,IAAI,YAAY,CAAC,IAAI;AAAA,QACjC,OAAO;AACL,cAAI,MAAM,SAAS,GAAG,CAAC,IAAI;AAAA,QAC7B;AAAA,IAEJ;AAEA,WAAO;AAAA,EACT,GAAG;AAAA,IACD,OAAO,CAAC;AAAA,EACV,CAAC;AAED,MAAI,oBAAoB,WAAW,OAC/B,gBAAgB,sBAAsB,SAAS,CAAC,IAAI,mBACpD,YAAY,yBAAyB,YAAY,SAAS;AAE9D,SAAO,MAAM,OAAO,IAAIL,gBAAeA,gBAAe,CAAC,GAAG,OAAO,MAAM,OAAO,CAAC,GAAG,aAAa;AAG/F,SAAOK,eAAc,MAAM,QAAQ,CAAC,QAAQ,KAAKL,gBAAeA,gBAAe,CAAC,GAAG,OAAO,KAAK,GAAG,SAAS,CAAC,EAAE,OAAO,mBAAmB,QAAQ,CAAC,CAAC;AACpJ;AAEA,IAAIM,cAAa;AAEjB,IAAI;AACF,EAAAA,cAAa;AACf,SAAS,GAAG;AAAC;AAEb,SAAS,MAAO;AACd,MAAI,CAACA,eAAc,WAAW,OAAO,QAAQ,UAAU,YAAY;AACjE,QAAI;AAEJ,KAAC,WAAW,SAAS,MAAM,MAAM,UAAU,SAAS;AAAA,EACtD;AACF;AAEA,SAAS,kBAAkBC,OAAM;AAG/B,MAAIA,SAAQ,QAAQA,KAAI,MAAM,YAAYA,MAAK,UAAUA,MAAK,YAAYA,MAAK,MAAM;AACnF,WAAOA;AAAA,EACT;AAEA,MAAI,QAAM,MAAM;AACd,WAAO,QAAM,KAAKA,KAAI;AAAA,EACxB;AAGA,MAAIA,UAAS,MAAM;AACjB,WAAO;AAAA,EACT;AAGA,MAAIA,SAAQ,QAAQA,KAAI,MAAM,YAAYA,MAAK,UAAUA,MAAK,UAAU;AACtE,WAAOA;AAAA,EACT;AAGA,MAAI,MAAM,QAAQA,KAAI,KAAKA,MAAK,WAAW,GAAG;AAE5C,WAAO;AAAA,MACL,QAAQA,MAAK,CAAC;AAAA,MACd,UAAUA,MAAK,CAAC;AAAA,IAClB;AAAA,EACF;AAGA,MAAI,OAAOA,UAAS,UAAU;AAC5B,WAAO;AAAA,MACL,QAAQ;AAAA,MACR,UAAUA;AAAA,IACZ;AAAA,EACF;AACF;AAKA,SAAS,cAAc,KAAK,OAAO;AAKjC,SAAO,MAAM,QAAQ,KAAK,KAAK,MAAM,SAAS,KAAK,CAAC,MAAM,QAAQ,KAAK,KAAK,QAAQN,iBAAgB,CAAC,GAAG,KAAK,KAAK,IAAI,CAAC;AACzH;AAEA,IAAI,eAAe;AAAA,EACjB,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,MAAM;AAAA,EACN,MAAM;AAAA,EACN,UAAU;AAAA,EACV,MAAM;AAAA,EACN,OAAO;AAAA,EACP,UAAU;AAAA,EACV,MAAM;AAAA,EACN,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,SAAS;AAAA,EACT,WAAW;AAAA,EACX,aAAa;AACf;AACA,IAAI,kBAA+B,aAAAO,QAAM,WAAW,SAAU,OAAO,KAAK;AACxE,MAAI,WAAWR,gBAAeA,gBAAe,CAAC,GAAG,YAAY,GAAG,KAAK;AAErE,MAAI,WAAW,SAAS,MACpB,WAAW,SAAS,MACpB,SAAS,SAAS,QAClB,YAAY,SAAS,WACrB,QAAQ,SAAS,OACjB,UAAU,SAAS,SACnB,SAAS,SAAS;AACtB,MAAI,aAAa,kBAAkB,QAAQ;AAC3C,MAAI,UAAU,cAAc,WAAW,CAAC,EAAE,OAAO,mBAAmB,UAAU,QAAQ,CAAC,GAAG,oBAAoB,aAAa,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC;AAC3I,MAAI,YAAY,cAAc,aAAa,OAAO,SAAS,cAAc,WAAW,QAAM,UAAU,SAAS,SAAS,IAAI,SAAS,SAAS;AAC5I,MAAI,OAAO,cAAc,QAAQ,kBAAkB,QAAQ,CAAC;AAC5D,MAAI,eAAe,KAAK,YAAYA,gBAAeA,gBAAeA,gBAAeA,gBAAe,CAAC,GAAG,OAAO,GAAG,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG;AAAA,IACnI;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,CAAC;AAEF,MAAI,CAAC,cAAc;AACjB,QAAI,uBAAuB,UAAU;AACrC,WAAO;AAAA,EACT;AAEA,MAAI,WAAW,aAAa;AAC5B,MAAI,aAAa;AAAA,IACf;AAAA,EACF;AACA,SAAO,KAAK,QAAQ,EAAE,QAAQ,SAAU,KAAK;AAE3C,QAAI,CAAC,aAAa,eAAe,GAAG,GAAG;AACrC,iBAAW,GAAG,IAAI,SAAS,GAAG;AAAA,IAChC;AAAA,EACF,CAAC;AACD,SAAO,aAAa,SAAS,CAAC,GAAG,UAAU;AAC7C,CAAC;AACD,gBAAgB,cAAc;AAC9B,gBAAgB,YAAY;AAAA,EAC1B,MAAM,kBAAAS,QAAU;AAAA,EAChB,QAAQ,kBAAAA,QAAU;AAAA,EAClB,UAAU,kBAAAA,QAAU;AAAA,EACpB,QAAQ,kBAAAA,QAAU;AAAA,EAClB,WAAW,kBAAAA,QAAU;AAAA,EACrB,MAAM,kBAAAA,QAAU;AAAA,EAChB,OAAO,kBAAAA,QAAU;AAAA,EACjB,MAAM,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,OAAO,kBAAAA,QAAU,MAAM,CAAC;AAAA,EAC/E,QAAQ,kBAAAA,QAAU;AAAA,EAClB,YAAY,kBAAAA,QAAU;AAAA,EACtB,SAAS,kBAAAA,QAAU;AAAA,EACnB,MAAM,kBAAAA,QAAU,MAAM,CAAC,MAAM,OAAO,cAAc,YAAY,MAAM,CAAC;AAAA,EACrE,MAAM,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,OAAO,kBAAAA,QAAU,MAAM,CAAC;AAAA,EAC/E,UAAU,kBAAAA,QAAU;AAAA,EACpB,MAAM,kBAAAA,QAAU,MAAM,CAAC,SAAS,MAAM,CAAC;AAAA,EACvC,OAAO,kBAAAA,QAAU;AAAA,EACjB,UAAU,kBAAAA,QAAU,MAAM,CAAC,GAAG,IAAI,KAAK,GAAG,CAAC;AAAA,EAC3C,OAAO,kBAAAA,QAAU;AAAA,EACjB,MAAM,kBAAAA,QAAU,MAAM,CAAC,OAAO,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,KAAK,CAAC;AAAA,EACzH,MAAM,kBAAAA,QAAU;AAAA,EAChB,WAAW,kBAAAA,QAAU;AAAA,EACrB,aAAa,kBAAAA,QAAU;AAAA,EACvB,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,EAC9D,OAAO,kBAAAA,QAAU;AAAA,EACjB,SAAS,kBAAAA,QAAU;AAAA,EACnB,WAAW,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM,CAAC;AAAA,EACnE,aAAa,kBAAAA,QAAU;AACzB;AACA,IAAI,eAAe,QAAQ,KAAK,MAAM,aAAAD,QAAM,aAAa;", "names": ["r", "t", "o", "e", "p", "css", "styles", "s", "bindInternal4", "counter", "icon", "arr", "style", "mask", "content", "end", "providers", "ownKeys", "_objectSpread2", "_defineProperty", "obj", "o", "s", "createElement", "PRODUCTION", "icon", "React", "PropTypes"]}